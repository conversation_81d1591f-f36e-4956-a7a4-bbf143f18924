import { marked } from 'marked';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 測試不同的 marked 配置
console.log('=== 測試 marked 配置 ===');

// 測試 1：默認配置
console.log('\n1. 默認配置測試：');
const test1 = marked('**test**');
console.log('輸入：**test**');
console.log('輸出：', test1);

// 測試 2：設置配置
console.log('\n2. 設置配置測試：');
marked.setOptions({
  gfm: true,
  breaks: true
});
const test2 = marked('**test**');
console.log('輸入：**test**');
console.log('輸出：', test2);

// 測試 3：葉元之的實際內容
console.log('\n3. 葉元之內容測試：');
const mdxPath = path.join(__dirname, 'src/content/legislators/葉元之.mdx');
const mdxContent = fs.readFileSync(mdxPath, 'utf8');

// 提取 body 內容
const bodyMatch = mdxContent.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
if (bodyMatch) {
  const bodyContent = bodyMatch[2];
  
  // 找到包含 **「凹之」** 的行
  const lines = bodyContent.split('\n');
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.includes('**「凹之」**')) {
      console.log(`找到目標行 ${i + 1}：`, line);
      
      // 測試這一行
      const parsedLine = marked(line);
      console.log('解析結果：', parsedLine);
      console.log('包含 <strong>：', parsedLine.includes('<strong>'));
      
      // 測試整行內容
      const fullContent = `葉元之，國民黨籍立法委員，代表新北市板橋東區。在政壇上，他以**「凹之」**的稱號聞名，這個稱號源於他在某些議題上的立場轉變。`;
      console.log('\n測試完整句子：');
      console.log('輸入：', fullContent);
      const parsedFull = marked(fullContent);
      console.log('輸出：', parsedFull);
      console.log('包含 <strong>：', parsedFull.includes('<strong>'));
      
      break;
    }
  }
}

// 測試 4：檢查字符編碼
console.log('\n4. 字符編碼測試：');
const testString = '**「凹之」**';
console.log('原始字符串：', testString);
console.log('字符編碼：', Buffer.from(testString, 'utf8').toString('hex'));
console.log('解析結果：', marked(testString)); 