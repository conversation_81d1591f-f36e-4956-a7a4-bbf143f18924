{"name": "recall-reasons-website", "type": "module", "version": "0.0.1", "scripts": {"dev": "tinacms dev -c \"astro dev\" && npx @agentdeskai/browser-tools-server@1.2.0", "build": "echo \"⚠️ 注意: 跳過 Tina 構建，僅使用 Astro 構建。如果需要 Tina 管理界面，請先創建 .env 文件包含 TINA_CLIENT_ID 和 TINA_TOKEN\" && astro build", "preview": "astro preview", "astro": "astro", "tinacms": "tinacms", "tina-dev": "tinacms dev -c \"astro dev\"", "tina-local-dev": "NODE_ENV=development npx tinacms dev --no-tina-cloud -c \"astro dev\"", "migrate-controversies": "node scripts/migrate-controversies-simple.mjs", "optimize-images": "node scripts/optimize-lcp-image.js"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.2.3", "@astrojs/sitemap": "^3.3.1", "@astrojs/tailwind": "^6.0.2", "@tinacms/auth": "^1.0.4", "@tinacms/cli": "^1.5.39", "astro": "^5.5.5", "flexsearch": "^0.8.154", "marked": "^15.0.8", "nanoid": "^5.1.5", "tailwindcss": "^3.4.17", "tinacms": "^2.7.5", "typeit": "^8.7.1", "typescript": "^5.8.3"}, "devDependencies": {"@types/node": "^22.14.0"}}