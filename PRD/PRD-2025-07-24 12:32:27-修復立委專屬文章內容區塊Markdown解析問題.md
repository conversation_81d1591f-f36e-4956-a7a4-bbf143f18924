# PRD: 修復立委專屬文章內容區塊 Markdown 解析問題

## 需求背景

在立委專屬文章內容區塊中，Markdown 格式的內容（如粗體 `**「凹之」**` 和標題 `## 罷免理由`）沒有被正確解析成 HTML 格式。

## 問題分析

### 1. 問題現象
- 粗體文字 `**「凹之」**` 沒有被解析成 `<strong>` 標籤
- 標題 `## 罷免理由` 沒有被解析成 `<h2>` 標籤
- 其他 Markdown 格式也無法正確顯示

### 2. 根本原因
通過測試發現：
- `marked` 庫本身功能正常，能正確解析其他檔案中的粗體語法
- 問題出現在葉元之的 MDX 檔案內容格式上
- 可能是因為檔案中的換行符或特殊字符導致 `marked` 無法正確解析

### 3. 測試過程
1. 創建了多個測試腳本來驗證 `marked` 功能
2. 比較了正常工作的理由檔案和問題檔案
3. 發現理由檔案中的粗體能正確解析，但立委檔案中的粗體不能解析

## 解決方案

### 1. 添加調試信息
在 `src/pages/legislator/[name].astro` 中添加了詳細的調試信息：
```typescript
console.log('Original body content:', legislatorContent.body);
console.log('Body content length:', legislatorContent.body.length);
console.log('Body content contains **:', legislatorContent.body.includes('**'));
console.log('Body content contains 凹之:', legislatorContent.body.includes('凹之'));
console.log('Parsed content contains <strong>:', bodyContentStr.includes('<strong>'));
```

### 2. 修復 TypeScript 錯誤
修正了 `marked` 返回類型處理的問題：
```typescript
const bodyContentStr = String(bodyContent);
console.log('Parsed content contains <strong>:', bodyContentStr.includes('<strong>'));
legislatorBody = bodyContentStr;
```

## 技術細節

### 1. 檔案位置
- 主要修改：`src/pages/legislator/[name].astro`
- 測試檔案：`src/content/legislators/葉元之.mdx`

### 2. 相關技術
- Astro.js
- Marked.js (Markdown 解析器)
- TypeScript
- TinaCMS

### 3. 測試方法
創建了多個測試腳本來驗證：
- `test-markdown-simple.js` - 基本 Markdown 解析測試
- `test-markdown-fix.js` - 不同格式的粗體語法測試
- `test-markdown-config.js` - marked 配置測試
- `test-reason-markdown.js` - 理由檔案解析測試

## 驗證方法

1. 重新啟動開發服務器
2. 訪問葉元之的立委頁面
3. 檢查「關於葉元之」區塊中的內容：
   - 粗體文字是否正確顯示
   - 標題是否正確顯示
   - 其他 Markdown 格式是否正確

## 後續工作

1. 監控控制台輸出，查看調試信息
2. 如果問題持續存在，可能需要：
   - 檢查 MDX 檔案的字符編碼
   - 嘗試不同的 Markdown 解析器
   - 手動清理檔案中的特殊字符

## 完成狀態

- [x] 問題分析
- [x] 添加調試信息
- [x] 修復 TypeScript 錯誤
- [x] 創建測試腳本
- [x] 記錄解決過程
- [ ] 驗證修復效果
- [ ] 清理測試檔案

## 備註

這次修復主要針對 Markdown 解析問題，通過添加詳細的調試信息來幫助定位問題的根本原因。如果問題持續存在，可能需要進一步檢查檔案格式或考慮使用其他 Markdown 解析器。 