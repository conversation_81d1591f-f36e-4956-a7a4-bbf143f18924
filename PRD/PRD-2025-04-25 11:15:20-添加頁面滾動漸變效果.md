# 添加頁面滾動漸變效果

## 需求描述
為了提升用戶體驗，在畫面最上方和最下方添加漸層淡出效果，讓用戶的視覺聚焦在畫面中央內容。在捲動頁面時，頂部和底部的內容會逐漸消失，確保用戶的注意力始終集中在頁面的核心內容上。

## 實現方式
1. 使用 CSS 偽元素（::before 和 ::after）創建固定位置的漸變遮罩
2. 應用溫暖舒適的漸變色調，確保自然過渡
3. 支持深色模式和淺色模式，避免過於生硬的黑白對比
4. 使用 fixed 定位確保滾動時漸變效果保持在視口頂部和底部
5. 採用較緩和的透明度過渡，分段控制過渡效果

## 技術實現
添加以下 CSS 樣式到 happy-recall.astro 頁面：

```css
:root {
  /* 控制 fade 區高度 */
  --fade-size: 20vh;
}

/* 使用偽元素創建固定位置的漸變遮罩 */
.scroll-focus {
  position: relative;
}

/* 頂部和底部漸變偽元素 */
.scroll-focus::before,
.scroll-focus::after {
  content: "";
  position: fixed;
  left: 0;
  right: 0;
  height: var(--fade-size);
  pointer-events: none;
  z-index: 10;
}

/* 頂部漸變 - 使用較溫暖的顏色 */
.scroll-focus::before {
  top: 0;
  background: linear-gradient(to bottom, 
    rgba(255, 252, 248, 0.95) 0%, 
    rgba(255, 252, 248, 0.7) 30%,
    rgba(255, 252, 248, 0.3) 70%,
    rgba(255, 252, 248, 0) 100%
  );
}

/* 深色模式下的漸變色適配 */
@media (prefers-color-scheme: dark) {
  .scroll-focus::before {
    background: linear-gradient(to bottom, 
      rgba(36, 37, 46, 0.95) 0%, 
      rgba(36, 37, 46, 0.7) 30%,
      rgba(36, 37, 46, 0.3) 70%,
      rgba(36, 37, 46, 0) 100%
    );
  }
}

/* 底部漸變 */
.scroll-focus::after {
  bottom: 0;
  background: linear-gradient(to top, 
    rgba(255, 255, 255, 1) 0%, 
    rgba(255, 255, 255, 0) 100%
  );
}
```

## 效果
- 使用暖色調漸變，創造溫馨舒適的視覺體驗
- 漸變過渡更為自然，避免生硬的黑白對比
- 分段透明度過渡，更精細的控制淡入淡出效果
- 頁面背景保持明亮溫暖，不會變得灰暗
- 滾動時漸變效果保持固定，提供一致的視覺體驗

## 兼容性
- 主要針對現代瀏覽器
- 使用多段線性漸變，確保過渡更加自然
- 使用媒體查詢支持深色模式，保持深色模式下的舒適體驗
- 使用 CSS 變量便於全局調整漸變區域大小 