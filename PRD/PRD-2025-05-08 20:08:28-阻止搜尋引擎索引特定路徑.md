# 阻止搜尋引擎索引特定路徑

## 需求背景
根據 Google Search Console 的回報，以下頁面已被檢索但尚未建立索引：
- https://recall.islandcountry.tw/recall-reasons/
- https://recall.islandcountry.tw/timeline/
- https://recall.islandcountry.tw/social-media-examples/
- https://recall.islandcountry.tw/search-test/

這些頁面需要被阻止搜尋引擎索引，以避免影響網站的SEO效能。

## 實施方案
修改網站根目錄的 robots.txt 檔案，明確禁止搜尋引擎爬蟲存取這些特定路徑。

## 具體改動
在 robots.txt 中添加以下 Disallow 指令：
```
Disallow: /recall-reasons/
Disallow: /timeline/
Disallow: /social-media-examples/
Disallow: /search-test/
```

## 預期效果
- 搜尋引擎爬蟲將遵循 robots.txt 的指示，不再索引這些頁面
- Google Search Console 應該會在下次爬蟲造訪時，遵循新的規則
- 這些頁面將不再出現在搜尋引擎的結果中

## 備註
- 更新 robots.txt 後，搜尋引擎需要一段時間才會重新爬取並應用新的規則
- 如果頁面已經被索引，可能需要使用 Google Search Console 的 URL 移除工具來加速從搜尋結果中移除 