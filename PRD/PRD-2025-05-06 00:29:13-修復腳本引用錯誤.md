# 修復腳本引用錯誤

## 需求描述
修復在 Astro 構建過程中出現的腳本引用錯誤。錯誤訊息顯示 TypeIt 腳本需要添加 `is:inline` 指令以避免公共目錄的資源被捆綁。

## 錯誤訊息
```
24:28:24 [ERROR] [vite] Internal server error: 

<script src="/js/typeit.umd.js"> references an asset in the "public/" directory. Please add the "is:inline" directive to keep this asset from being bundled.

File: /Users/<USER>/Downloads/recall-reasons-website/src/layouts/Layout.astro?astro&type=script&index=1&lang.ts
```

## 變更項目
- 在 `Layout.astro` 的 TypeIt 腳本標籤中添加 `is:inline` 指令

## 變更代碼
```diff
- <script src="/js/typeit.umd.js"></script>
+ <script src="/js/typeit.umd.js" is:inline></script>
```

## 問題原因
Astro 框架需要使用 `is:inline` 指令來標記那些不需要通過 Vite 進行捆綁處理的外部腳本。當引用 `public/` 目錄中的資源時，必須加上這個指令，否則 Astro 會嘗試將它作為模塊處理，導致構建錯誤。

## 效益
- 解決了構建過程中的錯誤
- 確保 TypeIt 腳本能夠正確載入和執行
- 改善網站的穩定性和開發體驗

## 完成狀態
✅ 已完成 