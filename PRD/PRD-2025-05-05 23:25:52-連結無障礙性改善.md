# 網站無障礙性改善：可辨識的連結名稱

## 問題摘要
根據 Google Lighthouse 審計結果，網站存在無障礙性問題：連結缺少可辨識的名稱。對於使用螢幕閱讀器的用戶來說，沒有可辨識名稱的連結會造成導航困難。

## 失敗元素
- YouTube 圖標連結：`<a href="https://www.youtube.com/@IslandCountryTw" target="_blank" class="text-gray-400 hover:text-white transition-colors">`

## 解決方案
1. 為社交媒體圖標連結添加 `aria-label` 屬性，提供可辨識的名稱
2. 將 SVG 圖標標記為裝飾性元素，通過添加 `aria-hidden="true"` 屬性

### 具體變更
1. 為 YouTube 連結添加描述性 `aria-label="島國物語 YouTube 頻道"`
2. 在 SVG 圖標添加 `aria-hidden="true"` 屬性，確保螢幕閱讀器忽略裝飾性圖標

## 實施狀態
✅ 已完成對 `src/layouts/Layout.astro` 的更改

## 結果
改善了網站的無障礙性，使使用螢幕閱讀器的用戶能夠更好地識別和瀏覽網站中的連結，特別是社交媒體連結。

## 後續步驟
1. 重新運行 Google Lighthouse 審計以確認連結名稱問題已解決
2. 檢查網站中的其他圖標連結，確保它們也有適當的 `aria-label` 屬性
3. 考慮添加更多的無障礙性功能，如鍵盤導航增強 