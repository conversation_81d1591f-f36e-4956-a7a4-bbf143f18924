# 設定Astro網站sitemap整合

## 需求背景
網站需要一個有效的網站地圖(sitemap)，以便搜索引擎能夠更有效地抓取和索引網站內容。已經成功建立了 robots.txt 檔案，現在需要配置 @astrojs/sitemap 整合來自動生成網站地圖。

## 解決方案
1. 確認 @astrojs/sitemap 已經安裝
2. 在 astro.config.mjs 文件中配置 sitemap 整合
3. 設定必要的 site 屬性以及其他選項

## 實現細節
在 astro.config.mjs 中添加以下配置：
```javascript
// @ts-check
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import mdx from '@astrojs/mdx';

import sitemap from '@astrojs/sitemap';

// https://astro.build/config
export default defineConfig({
  site: 'https://recall.islandcountry.tw',
  base: '/',
  integrations: [tailwind(), mdx(), sitemap({
    changefreq: 'daily',
    lastmod: new Date()
  })],
  // ... 其他配置
});
```

## 效益
1. 自動生成網站地圖，幫助搜索引擎更全面地索引網站內容
2. 提高網站在搜索結果中的可見性
3. 設定內容更新頻率，幫助搜索引擎決定抓取頻率
4. 與已配置的 robots.txt 協同工作，優化網站的 SEO 表現

## 後續步驟
1. 在構建網站後，確認 sitemap.xml 檔案已正確生成
2. 可能需要根據實際需求調整 changefreq 和其他參數
3. 若網站有多語言版本，可考慮添加 i18n 配置 