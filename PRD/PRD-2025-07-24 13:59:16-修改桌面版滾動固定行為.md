# PRD: 修改桌面版滾動固定行為

## 需求背景
用戶希望在桌面版（desktop view）滾動時，不需要固定顯示整個立委圖示區域，而是只固定顯示"江湖稱號"區塊。

## 問題分析
原本的程式碼結構：
- 整個左側欄位（包含立委照片、江湖稱號、罷免團體資訊）都使用了 `sticky top-4` 類別
- 這導致在桌面版滾動時，整個左側區域都會固定顯示

## 解決方案
修改立委頁面的滾動行為：
1. 移除整個左側欄位的 `sticky` 定位
2. 只對"江湖稱號"區塊應用 `md:sticky md:top-4` 類別
3. 立委照片和罷免團體資訊區塊保持正常滾動

## 修改內容
### 檔案：`src/pages/legislator/[name].astro`

**修改前：**
```astro
<div class="sticky top-4 space-y-6">
  <!-- 立委照片 -->
  <div class="relative rounded-lg overflow-hidden bg-white dark:bg-gray-800 shadow-lg group">
    <!-- ... -->
  </div>
  
  <!-- 江湖稱號 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
    <!-- ... -->
  </div>
  
  <!-- 罷免團體資訊 -->
  {recallTeam && (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <!-- ... -->
    </div>
  )}
</div>
```

**修改後：**
```astro
<div class="space-y-6">
  <!-- 立委照片 - 桌面版不固定 -->
  <div class="relative rounded-lg overflow-hidden bg-white dark:bg-gray-800 shadow-lg group">
    <!-- ... -->
  </div>
  
  <!-- 江湖稱號 - 桌面版固定顯示 -->
  <div class="md:sticky md:top-4 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
    <!-- ... -->
  </div>
  
  <!-- 罷免團體資訊 - 桌面版不固定 -->
  {recallTeam && (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <!-- ... -->
    </div>
  )}
</div>
```

## 技術細節
- 使用 `md:sticky md:top-4` 確保只在桌面版（md 以上）啟用固定定位
- 保持手機版的正常滾動行為
- 只有"江湖稱號"區塊在桌面版滾動時固定顯示

## 效果
- 桌面版滾動時，立委照片會正常滾動消失
- 只有"江湖稱號"區塊會固定在頁面頂部
- 罷免團體資訊也會正常滾動
- 手機版行為保持不變

## 完成時間
2025-07-24 13:59:16 