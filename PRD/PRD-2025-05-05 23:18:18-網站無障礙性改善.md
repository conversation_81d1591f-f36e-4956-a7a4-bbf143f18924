# 網站無障礙性改善：提高對比度

## 問題摘要
根據 Google Lighthouse 審計結果，網站存在無障礙性問題：背景和前景顏色沒有足夠的對比度。低對比度的文字對許多用戶來說難以閱讀或完全無法閱讀。

## 失敗元素
- `span` 元素
- `footer.bg-black.text-white.py-8.sm:py-12.mt-auto`
- `span.block.md:inline`

## 解決方案
將頁腳中的文字顏色從低對比度的灰色（如 text-gray-400, text-gray-500）更改為較高對比度的灰色（如 text-gray-200, text-gray-300）。

### 具體變更
1. 將頁腳中的文字從 `text-gray-400` 更改為 `text-gray-200`
2. 將版權訊息從 `text-gray-500` 更改為 `text-gray-300`
3. 將社交媒體圖標從 `text-gray-400` 更改為 `text-gray-200`
4. 將分隔線從 `border-gray-800` 更改為 `border-gray-700` 以提高可見度

## 實施狀態
✅ 已完成對 `src/layouts/Layout.astro` 的更改

## 結果
提高了頁腳元素的對比度，改善了網站的無障礙性，使其符合 WCAG 標準，確保所有用戶（包括視力障礙用戶）都能更容易地閱讀網站內容。

## 後續步驟
1. 重新運行 Google Lighthouse 審計以確認對比度問題已解決
2. 檢查網站中的其他頁面和組件，確保它們也符合對比度要求
3. 考慮實施更全面的無障礙性審查，以發現並解決其他潛在問題 