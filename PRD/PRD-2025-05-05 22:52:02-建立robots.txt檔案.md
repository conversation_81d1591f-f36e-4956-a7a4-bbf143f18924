# 建立robots.txt檔案

## 需求背景
網站缺少有效的robots.txt檔案，導致產生兩個錯誤：
1. 第一行包含HTML內容（`<!DOCTYPE html><html lang="zh-TW">...`）
2. 第二行包含未知的SVG路徑指令

## 解決方案
建立標準的robots.txt檔案，包含以下內容：
- 允許所有爬蟲訪問大部分網站內容
- 禁止爬蟲訪問管理員和私人頁面
- 指定網站地圖位置
- 設定爬蟲延遲時間

## 實現細節
```
User-agent: *
Allow: /
Disallow: /admin/
Disallow: /private/

# Allow all robots complete access to site
# Disallow folders that shouldn't be indexed

Sitemap: https://recall.islandcountry.tw/sitemap.xml

# Crawl-delay is respected by some crawlers
Crawl-delay: 10
```

## 效益
1. 符合搜索引擎標準，避免錯誤
2. 適當控制爬蟲行為，保護網站私人內容
3. 通過指定sitemap.xml幫助搜索引擎更有效地索引網站內容 