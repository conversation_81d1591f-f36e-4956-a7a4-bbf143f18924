# PRD: 新增立委專屬文章內容區塊

## 需求概述
在立委頁面的「相關影片」區塊上方新增一個可編輯的文章內容區塊，讓管理員可以在後台為每個立委添加專屬的介紹內容。

## 實作內容

### 1. 後端修改
- **檔案**: `src/pages/legislator/[name].astro`
- **修改內容**:
  - 新增 `legislatorBody` 變數來存儲立委的 body 內容
  - 使用 `marked.parse()` 將 Markdown 內容轉換為 HTML
  - 在頁面最上方新增文章內容區塊（比爭議事項區塊還要上面）

### 2. 前端顯示
- **區塊設計**:
  - 使用藍色漸層標題背景 (`from-blue-500 to-indigo-600`)
  - 標題為「關於{立委姓名}」
  - 使用文件圖示
  - 內容使用 `prose` 樣式，支援 Markdown 格式
  - **位置**: 顯示在頁面最上方，比爭議事項區塊還要上面

### 3. 後台編輯
- **Tina CMS 配置**: 已在 `tina/config.ts` 中配置
- **字段類型**: `rich-text`
- **字段名稱**: `body`
- **標籤**: "簡介"
- **功能**: 支援富文本編輯，可添加標題、段落、粗體等格式

### 4. 示例內容
為以下立委添加了示例內容：
- **李彥秀**: 包含主要爭議、租金補貼預算凍結、國防預算爭議等內容
- **葉元之**: 包含名嘴身份爭議、立場轉變等內容

## 技術細節

### Markdown 轉換
```typescript
// 使用 marked.parse 確保返回字符串
legislatorBody = marked.parse(legislatorContent.body);
```

### 條件渲染
```astro
{legislatorBody && (
  <div class="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
    <!-- 內容區塊 -->
  </div>
)}
```

### 樣式設計
- 響應式設計，支援深色模式
- 使用 Tailwind CSS 的 `prose` 類別
- 漸層標題背景增加視覺層次

## 使用方式

### 在後台編輯
1. 登入 Tina CMS 後台
2. 進入「立法委員」集合
3. 選擇要編輯的立委
4. 在「簡介」字段中編輯內容
5. 支援 Markdown 格式，可添加標題、粗體、列表等

### 內容格式建議
- 使用 `#` 作為主標題
- 使用 `##` 作為副標題
- 使用 `**文字**` 作為粗體強調
- 使用 `-` 或 `*` 作為列表項目

## 預期效果
- 為每個立委提供專屬的介紹內容
- 提升頁面的資訊豐富度
- 讓管理員可以靈活編輯立委相關資訊
- 保持與現有設計風格的一致性

## 完成狀態
✅ 後端邏輯實作完成
✅ 前端顯示區塊完成
✅ 區塊位置調整完成（顯示在最上方）
✅ 後台編輯功能確認
✅ 示例內容添加完成
✅ 樣式設計完成

## 後續優化建議
1. 可考慮添加內容版本控制
2. 可考慮添加內容審核機制
3. 可考慮添加內容統計功能
4. 可考慮添加內容搜尋功能 