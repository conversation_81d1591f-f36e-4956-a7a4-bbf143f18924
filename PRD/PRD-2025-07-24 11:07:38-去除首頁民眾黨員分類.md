# PRD: 去除首頁民眾黨員分類

## 需求概述
用戶要求去除網站首頁中的"民眾黨員"分類選項。

## 執行過程

### 1. 識別相關文件
- 發現 `src/content/roles/tpp.mdx` 文件定義了"民眾黨員"角色
- 檢查是否有其他文件引用了這個角色

### 2. 刪除角色定義文件
- 刪除了 `src/content/roles/tpp.mdx` 文件
- 該文件包含：
  - title: "民眾黨員"
  - icon: "tpp"
  - orderID: 6

### 3. 更新相關理由文件
發現有兩個理由文件引用了 `tpp` 角色：
- `src/content/reasons/94-黨內決策不透明.mdx`
- `src/content/reasons/91-政策立場不一致.mdx`

將這兩個文件的 `relatedRoles` 從 `["tpp"]` 改為 `[]`

### 4. 清理角色頁面模板
- 在 `src/pages/roles/[id].astro` 中移除了 `tpp` 圖標的 SVG 定義
- 確保角色頁面不會因為缺少 `tpp` 角色而出錯

### 5. 驗證清理結果
- 使用 grep 搜索確認沒有遺漏的 `tpp` 引用
- 確認所有相關文件都已正確更新

## 技術細節

### 修改的文件列表
1. `src/content/roles/tpp.mdx` - 已刪除
2. `src/content/reasons/94-黨內決策不透明.mdx` - 更新 relatedRoles
3. `src/content/reasons/91-政策立場不一致.mdx` - 更新 relatedRoles
4. `src/pages/roles/[id].astro` - 移除 tpp 圖標定義

### 影響範圍
- 首頁角色選擇區將不再顯示"民眾黨員"選項
- 原本屬於"民眾黨員"分類的兩個理由現在不會顯示在任何角色分類下
- 角色頁面模板已清理，不會因為缺少 tpp 角色而出錯

## 完成狀態
✅ 已完成所有必要的修改
✅ 已驗證沒有遺漏的引用
✅ 網站功能正常運作

## 備註
- 原本屬於"民眾黨員"分類的兩個理由（黨內決策不透明、政策立場不一致）現在沒有關聯任何角色
- 如果需要，可以將這些理由重新分配給其他角色分類 