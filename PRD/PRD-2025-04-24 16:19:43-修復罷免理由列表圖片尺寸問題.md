# 修復罷免理由列表圖片尺寸問題

## 問題描述

在立委頁面的罷免理由列表中，左側欄位的媒體顯示區有些圖片因尺寸過大導致破版，影響網頁排版和使用者體驗。

## 解決方案

為避免圖片破版，我們在媒體顯示區添加了以下限制：

1. 添加 `overflow-hidden` 屬性以確保溢出的內容被隱藏
2. 設定了最大高度 `max-height: 250px` 
3. 確保媒體容器寬度為 100%
4. 為圖片設定固定的寬高比 `aspect-ratio: 4/3`
5. 限制圖片最大寬度 `max-width: 100%`

## 修改內容

```diff
- <div class="md:w-1/3 h-48 md:h-auto relative">
- <div class="media-gallery h-full" data-reason-id={reason.id}>
+ <div class="md:w-1/3 h-48 md:h-auto relative overflow-hidden" style="max-height: 250px;">
+ <div class="media-gallery h-full w-full" data-reason-id={reason.id}>
  {reason.media[0].type === 'image' ? (
    <img
      src={reason.media[0].url}
      alt={reason.media[0].caption || reason.title}
      class="w-full h-full object-cover cursor-pointer media-trigger"
+     style="aspect-ratio: 4/3; max-width: 100%;"
      onerror="this.src='/favicon.svg'; this.onerror=null;"
    />
```

## 效果

此修改可確保媒體內容在保持其視覺特性的同時，不會因為尺寸過大而破壞頁面佈局。圖片將被裁剪並以固定比例顯示，消除了之前可能發生的破版問題。 