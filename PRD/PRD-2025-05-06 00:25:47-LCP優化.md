# LCP優化

## 需求描述
優化首頁的 Largest Contentful Paint (LCP) 元素，以提升網站載入效能。根據 Google Lighthouse 的建議，現有的 LCP 元素載入時間為 2,320 ms，需要進行優化。

## 變更項目

### 1. 圖片格式與尺寸優化
- 建立了優化腳本 `scripts/optimize-lcp-image.js`，用於生成不同尺寸的圖片
- 將原始圖片 `/images/index-各種角色思考罷免的理由.webp` 轉換為多種尺寸的優化版本
- 為不同裝置生成了適當的圖片尺寸 (320px, 480px, 640px)
- 同時生成了 WebP 和 AVIF 格式以支援不同瀏覽器

### 2. 圖片載入優化
- 在 `index.astro` 中使用了 `<picture>` 元素搭配 `srcset` 和 `sizes` 屬性，實現響應式圖片載入
- 針對支援 AVIF 的瀏覽器提供更小的文件
- 添加了 `loading="eager"`, `decoding="async"` 和 `fetchpriority="high"` 屬性以優先載入 LCP 圖片

### 3. 預載入優化
- 在 `Layout.astro` 中添加了首頁 LCP 圖片的預載入標籤，使瀏覽器更早開始下載圖片

## 變更前後的代碼

### 圖片標籤優化:
```diff
- <img 
-   src="/images/index-各種角色思考罷免的理由.webp"
-   class="w-full h-auto max-h-[450px] object-contain rounded-xl shadow-md mx-auto transition-all duration-300 hover:shadow-lg"
-   alt="各種角色一起思考罷免的理由"
-   width="640"
-   height="427"
- >
+ <picture>
+   <!-- AVIF format for browsers that support it -->
+   <source
+     type="image/avif"
+     sizes="(max-width: 640px) 100vw, 640px"
+     srcset="
+       /images/optimized/index-roles-320.avif 320w,
+       /images/optimized/index-roles-480.avif 480w,
+       /images/optimized/index-roles-640.avif 640w
+     "
+   >
+   <!-- WebP format as fallback -->
+   <source
+     type="image/webp"
+     sizes="(max-width: 640px) 100vw, 640px"
+     srcset="
+       /images/optimized/index-roles-320.webp 320w,
+       /images/optimized/index-roles-480.webp 480w,
+       /images/optimized/index-roles-640.webp 640w
+     "
+   >
+   <!-- Original image as final fallback -->
+   <img 
+     src="/images/optimized/index-roles-640.webp"
+     class="w-full h-auto max-h-[450px] object-contain rounded-xl shadow-md mx-auto transition-all duration-300 hover:shadow-lg"
+     alt="各種角色一起思考罷免的理由"
+     width="640"
+     height="427"
+     loading="eager"
+     decoding="async"
+     fetchpriority="high"
+   >
+ </picture>
```

### 預載入標籤添加:
```diff
+ <!-- Preload critical LCP image -->
+ {currentPath === '/' && (
+   <>
+     <link 
+       rel="preload" 
+       as="image" 
+       href="/images/optimized/index-roles-640.webp" 
+       type="image/webp"
+       fetchpriority="high"
+     />
+   </>
+ )}
```

## 效益
- 減少圖片檔案大小：原始圖片 128KB vs. 優化後最大圖片 38KB (WebP 640px) 和 46KB (AVIF 640px)
- 提供適應不同設備螢幕大小的圖片，減少行動裝置不必要的大圖下載
- 透過預載入與優先級提示讓瀏覽器更早開始下載圖片
- 預期 LCP 時間將從原本的 2,320ms 大幅降低

## 完成狀態
✅ 已完成

## 後續追蹤工作
- 監控實際生產環境中 LCP 表現
- 考慮將相同的圖片最佳化策略應用到其他關鍵頁面
- 可添加自動化構建流程，在發布前自動優化所有圖片 