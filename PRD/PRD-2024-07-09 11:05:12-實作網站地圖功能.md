# 網站地圖 (Sitemap) 功能實作需求

## 需求背景
- 需要為網站實作 sitemap.xml 功能，以利搜尋引擎爬蟲索引網站內容
- 已有安裝 Astro sitemap 套件，但 sitemap.xml 檔案未顯示在 public 目錄中
- sitemap 索引檔在瀏覽器中顯示不友善，需要美化

## 問題診斷
- 檢查了 public 目錄，未找到 sitemap.xml
- 檢查了 dist 目錄，找到 sitemap-0.xml 和 sitemap-index.xml
- 確認 Astro sitemap 套件僅在建置 (build) 階段產生檔案，而非開發階段
- sitemap XML 檔案缺少 XSL 樣式表，導致瀏覽器顯示原始的 XML 結構

## 解決方案
1. 確認 astro.config.mjs 中的 sitemap 設定是正確的，並添加 XSL 樣式表參考：
   ```javascript
   sitemap({
     changefreq: 'daily',
     lastmod: new Date(),
     xslURL: '/sitemap.xsl'
   })
   ```

2. 更新 robots.txt 中的 sitemap 路徑，指向 sitemap-index.xml
   ```
   Sitemap: https://recall.islandcountry.tw/sitemap-index.xml
   ```

3. 創建 public/sitemap.xsl 檔案，提供美觀的 XML 呈現格式

## 實施步驟
- 執行專案建置指令 (npm run build) 產生 sitemap 檔案
- 更新 robots.txt 中的 sitemap 參考路徑
- 創建 sitemap.xsl 樣式表文件
- 確認部署後 sitemap 能透過 https://recall.islandcountry.tw/sitemap-index.xml 訪問，並以友善格式顯示

## 預期結果
- 搜尋引擎能正確找到網站的 sitemap
- 網站所有頁面能被搜尋引擎正確索引
- 提升網站在搜尋引擎的能見度與 SEO 表現
- 當使用瀏覽器訪問 sitemap 時，能以美觀易讀的格式呈現 