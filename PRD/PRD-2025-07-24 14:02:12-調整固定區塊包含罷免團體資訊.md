# PRD: 調整固定區塊包含罷免團體資訊

## 需求背景
用戶希望進一步調整桌面版滾動行為，讓"江湖稱號"和"罷免團體資訊"兩個區塊都固定在頁面頂部。

## 問題分析
之前的修改只固定了"江湖稱號"區塊，但用戶希望罷免團體資訊也能在滾動時保持可見。

## 解決方案
創建一個固定容器，將"江湖稱號"和"罷免團體資訊"兩個區塊都包含在內，讓它們在桌面版滾動時一起固定在頁面頂部。

## 修改內容
### 檔案：`src/pages/legislator/[name].astro`

**修改前：**
```astro
{/* 江湖稱號 - 桌面版固定顯示 */}
<div class="md:sticky md:top-4 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
  <!-- 江湖稱號內容 -->
</div>

{/* 罷免團體資訊 - 桌面版不固定 */}
{recallTeam && (
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
    <!-- 罷免團體資訊內容 -->
  </div>
)}
```

**修改後：**
```astro
{/* 固定區塊容器 - 包含江湖稱號和罷免團體資訊 */}
<div class="md:sticky md:top-4 space-y-4">
  {/* 江湖稱號 - 桌面版固定顯示 */}
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
    <!-- 江湖稱號內容 -->
  </div>

  {/* 罷免團體資訊 - 桌面版固定顯示 */}
  {recallTeam && (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <!-- 罷免團體資訊內容 -->
    </div>
  )}
</div>
```

## 技術細節
- 創建一個外層容器 `div` 應用 `md:sticky md:top-4` 類別
- 使用 `space-y-4` 為兩個區塊之間提供適當的間距
- 兩個區塊都包含在固定容器內，確保它們在桌面版滾動時一起固定
- 手機版行為保持不變

## 效果
- **桌面版**：滾動時"江湖稱號"和"罷免團體資訊"兩個區塊都會固定在頁面頂部
- **手機版**：所有區塊都正常滾動，不受影響
- **立委照片**：仍然正常滾動，不會固定顯示

## 完成時間
2025-07-24 14:02:12 