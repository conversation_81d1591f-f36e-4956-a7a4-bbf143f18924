# 網站無障礙性改善：移除冗餘圖片替代文字

## 問題摘要
根據 Google Lighthouse 審計結果，網站存在無障礙性問題：圖片元素具有與鄰近文字完全相同的替代文字（alt 屬性）。對於使用螢幕閱讀器的用戶來說，這會導致相同文字被讀取兩次，造成混淆。

## 失敗元素
- 網站 Logo 圖片：`<img src="/logo-island-country.png" alt="島國物語" class="w-8 h-8 mr-2">`
- 問題在於圖片的 alt 屬性值 "島國物語" 與緊鄰的連結文字內容相同

## 解決方案
當圖片作為連結的一部分，而連結本身已包含描述文字時，圖片應被視為裝飾性元素：
1. 將圖片的 alt 屬性設為空值 `alt=""`
2. 添加 `aria-hidden="true"` 屬性，明確指示螢幕閱讀器忽略此元素

### 具體變更
1. 將圖片的 alt 屬性從 `alt="島國物語"` 改為 `alt=""`
2. 添加 `aria-hidden="true"` 屬性，確保螢幕閱讀器不會重複朗讀內容

## 實施狀態
✅ 已完成對 `src/layouts/Layout.astro` 的更改

## 結果
改善了網站的無障礙性，避免了螢幕閱讀器用戶聽到重複信息的混淆情況，提供了更好的用戶體驗。

## 後續步驟
1. 重新運行 Google Lighthouse 審計以確認冗餘替代文字問題已解決
2. 檢查網站中的其他圖片元素，確保它們都有適當的替代文字設置
3. 考慮制定圖片替代文字的最佳實踐指南，以便未來開發 