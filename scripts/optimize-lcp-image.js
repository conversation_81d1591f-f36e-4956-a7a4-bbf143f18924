import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { fileURLToPath } from 'url';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SOURCE_IMAGE = path.join(process.cwd(), 'public', 'images', 'index-各種角色思考罷免的理由.webp');
const OUTPUT_DIR = path.join(process.cwd(), 'public', 'images', 'optimized');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Image sizes for responsive design
const sizes = [320, 480, 640, 800, 1024];

async function optimizeImage() {
  try {
    // Get image metadata
    const metadata = await sharp(SOURCE_IMAGE).metadata();
    console.log('Original image metadata:', metadata);

    // Generate different sizes of the image in WebP format
    for (const width of sizes) {
      if (width > metadata.width) continue; // Skip if requested width is larger than original

      const outputFileName = path.join(OUTPUT_DIR, `index-roles-${width}.webp`);
      
      await sharp(SOURCE_IMAGE)
        .resize(width)
        .webp({ quality: 80, effort: 6 })
        .toFile(outputFileName);
      
      console.log(`Created optimized image: ${outputFileName}`);
    }

    // Generate AVIF version for modern browsers (smaller file size but less compatibility)
    for (const width of sizes) {
      if (width > metadata.width) continue;

      const outputFileName = path.join(OUTPUT_DIR, `index-roles-${width}.avif`);
      
      await sharp(SOURCE_IMAGE)
        .resize(width)
        .avif({ quality: 65, effort: 9 })
        .toFile(outputFileName);
      
      console.log(`Created AVIF image: ${outputFileName}`);
    }

    console.log('Image optimization completed successfully!');
  } catch (error) {
    console.error('Error optimizing images:', error);
  }
}

optimizeImage(); 