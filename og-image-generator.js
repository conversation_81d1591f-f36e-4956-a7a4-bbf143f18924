// 將立委照片轉換為英文檔名的 OG images
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 獲取當前檔案的目錄路徑
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 來源和目標目錄
const sourceDir = path.join(__dirname, 'public/legislators');
const targetDir = path.join(__dirname, 'public/og-images');

// 確保目標目錄存在
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
  console.log(`建立目錄: ${targetDir}`);
}

// 讀取所有立委照片
const files = fs.readdirSync(sourceDir);

files.forEach(file => {
  // 只處理 jpg 檔案
  if (!file.endsWith('.jpg')) return;
  
  // 取得立委名字 (移除 .jpg 副檔名)
  const name = file.replace('.jpg', '');
  
  // 使用 Base64 編碼建立英文檔名 (移除 padding = 符號)
  const encodedName = Buffer.from(name).toString('base64').replace(/=/g, '');
  const newFilename = `legislator-${encodedName}.jpg`;
  
  // 讀取原始檔案
  const sourceFile = path.join(sourceDir, file);
  const targetFile = path.join(targetDir, newFilename);
  
  // 複製檔案
  fs.copyFileSync(sourceFile, targetFile);
  console.log(`已複製: ${file} → ${newFilename} (${name})`);
});

// 生成對照表內容
const mappingTable = files
  .filter(file => file.endsWith('.jpg'))
  .map(file => {
    const name = file.replace('.jpg', '');
    const encodedName = Buffer.from(name).toString('base64').replace(/=/g, '');
    return { name, encodedName, filename: `legislator-${encodedName}.jpg` };
  })
  .sort((a, b) => a.name.localeCompare(b.name, 'zh-TW'))
  .map(item => `| ${item.name} | ${item.encodedName} | ${item.filename} |`)
  .join('\n');

console.log('完成: 所有立委照片已複製為 OG 圖片格式');

// 輸出對照表 (可選)
console.log('\n立委名稱與檔名對照表:\n');
console.log('| 立委名稱 | Base64 編碼 | OG 圖片檔名 |');
console.log('|----------|------------|------------|');
console.log(mappingTable); 