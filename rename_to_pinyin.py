import os
import re
import glob
from pypinyin import lazy_pinyin, Style
import datetime

# Define the legislators directory path
legislators_img_dir = "public/legislators"
legislators_mdx_dir = "src/content/legislators"

# Function to convert Chinese to pinyin
def to_pinyin(chinese_text):
    # Convert Chinese characters to pinyin, use dashes to separate words
    pinyin_list = lazy_pinyin(chinese_text, style=Style.NORMAL)
    return ''.join(pinyin_list)

# Create mapping of Chinese to pinyin names
name_mapping = {}

# Process all JPG files in the legislators directory
for file_path in glob.glob(f"{legislators_img_dir}/*.jpg"):
    basename = os.path.basename(file_path)
    if re.search(r'[\u4e00-\u9fff]', basename):  # Check if contains Chinese characters
        chinese_name = os.path.splitext(basename)[0]
        pinyin_name = to_pinyin(chinese_name)
        new_filename = f"{pinyin_name}.jpg"
        name_mapping[chinese_name] = pinyin_name
        
        # Rename the file
        new_file_path = os.path.join(legislators_img_dir, new_filename)
        os.rename(file_path, new_file_path)
        print(f"Renamed: {basename} -> {new_filename}")

# Files to check for dynamic references (using patterns like `${name}.jpg`)
files_to_check = [
    "src/pages/legislators.astro",
    "src/pages/reasons/[id].astro",
    "src/pages/happy-recall.astro",
    "src/pages/legislator/[name].astro",
    # Add any other files that might have dynamic references
]

# Update MDX files to use the new filenames
for mdx_file in glob.glob(f"{legislators_mdx_dir}/*.mdx"):
    with open(mdx_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if this MDX file needs to be updated
    file_updated = False
    for chinese_name, pinyin_name in name_mapping.items():
        # Update image references in the format '/legislators/中文名.jpg'
        old_ref = f"'/legislators/{chinese_name}.jpg'"
        new_ref = f"'/legislators/{pinyin_name}.jpg'"
        if old_ref in content:
            content = content.replace(old_ref, new_ref)
            file_updated = True
    
    # Write back the content if the file was updated
    if file_updated:
        with open(mdx_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Updated references in: {os.path.basename(mdx_file)}")

# Update any hardcoded references in other files
for file_path in files_to_check:
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        file_updated = False
        
        # Update hardcoded references like src="/legislators/林沛祥.jpg"
        for chinese_name, pinyin_name in name_mapping.items():
            # Different formats of references
            patterns = [
                f'src="/legislators/{chinese_name}.jpg"',
                f'src="/legislators/{chinese_name}.jpg',
                f'"/legislators/{chinese_name}.jpg"',
                f"'/legislators/{chinese_name}.jpg'",
            ]
            
            replacements = [
                f'src="/legislators/{pinyin_name}.jpg"',
                f'src="/legislators/{pinyin_name}.jpg',
                f'"/legislators/{pinyin_name}.jpg"',
                f"'/legislators/{pinyin_name}.jpg'",
            ]
            
            for pattern, replacement in zip(patterns, replacements):
                if pattern in content:
                    content = content.replace(pattern, replacement)
                    file_updated = True
        
        # Write back the content if the file was updated
        if file_updated:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Updated references in: {file_path}")

# Create a record of this operation in the PRD directory
now = datetime.datetime.now()
formatted_time = now.strftime("%Y-%m-%d %H:%M:%S")
prd_dir = "PRD"

# Create the PRD directory if it doesn't exist
if not os.path.exists(prd_dir):
    os.makedirs(prd_dir)

prd_filename = f"PRD-{formatted_time}-將立委圖檔轉換為拼音檔名.md"
prd_path = os.path.join(prd_dir, prd_filename)

with open(prd_path, 'w', encoding='utf-8') as f:
    f.write(f"# 將立委圖檔轉換為拼音檔名\n\n")
    f.write(f"日期: {formatted_time}\n\n")
    f.write("## 需求描述\n\n")
    f.write("將立委圖檔從中文檔名轉換為拼音檔名，並更新所有引用這些圖檔的地方。\n\n")
    f.write("## 實施方式\n\n")
    f.write("1. 使用Python的pypinyin庫將中文檔名轉換為拼音\n")
    f.write("2. 重命名public/legislators目錄下的所有中文檔名圖檔\n")
    f.write("3. 更新所有MDX檔案中的圖檔引用\n")
    f.write("4. 更新其他需要動態引用圖檔的檔案\n\n")
    f.write("## 檔名映射表\n\n")
    f.write("| 原始檔名 | 新檔名 |\n")
    f.write("|---------|--------|\n")
    
    for chinese_name, pinyin_name in name_mapping.items():
        f.write(f"| {chinese_name}.jpg | {pinyin_name}.jpg |\n")

print(f"Created PRD record at: {prd_path}")
print("Conversion complete!") 