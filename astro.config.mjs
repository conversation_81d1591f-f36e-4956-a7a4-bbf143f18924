// @ts-check
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import mdx from '@astrojs/mdx';

import sitemap from '@astrojs/sitemap';

// https://astro.build/config
export default defineConfig({
  site: 'https://recall.islandcountry.tw',
  base: '/',
  integrations: [tailwind(), mdx(), sitemap({
    changefreq: 'daily',
    lastmod: new Date(),
    xslURL: '/sitemap.xsl'
  })],
  vite: {
    optimizeDeps: {
      exclude: ['astro:content']
    },
    server: {
      host: true,
      allowedHosts: ['60f4-1-34-243-125.ngrok-free.app']
    }
  }
});