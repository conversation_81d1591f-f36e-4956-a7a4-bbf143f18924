<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>罷免元兇遊戲測試</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #4ade80;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-pass {
            background: #dcfce7;
            border: 1px solid #22c55e;
            color: #166534;
        }
        .test-fail {
            background: #fecaca;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .game-link {
            display: inline-block;
            background: #4ade80;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }
        .game-link:hover {
            background: #22c55e;
        }
        .data-preview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 罷免元兇遊戲測試頁面</h1>
        
        <div class="test-section">
            <h2 class="test-title">🔗 遊戲連結</h2>
            <a href="index.html" class="game-link" target="_blank">開啟遊戲</a>
            <p>點擊上方連結開啟遊戲進行測試</p>
        </div>

        <div class="test-section">
            <h2 class="test-title">📋 檔案檢查</h2>
            <div id="file-check-results"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">📊 遊戲資料預覽</h2>
            <div id="game-data-preview"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">🧪 功能測試</h2>
            <div id="function-test-results"></div>
            <button onclick="runFunctionTests()" style="background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">執行功能測試</button>
        </div>

        <div class="test-section">
            <h2 class="test-title">📱 響應式測試</h2>
            <div id="responsive-test-results"></div>
            <button onclick="testResponsive()" style="background: #8b5cf6; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">測試響應式設計</button>
        </div>

        <div class="test-section">
            <h2 class="test-title">🎯 遊戲流程測試指南</h2>
            <ol>
                <li><strong>開場測試：</strong>檢查開場畫面是否正確顯示，角色動畫是否正常</li>
                <li><strong>遊戲開始：</strong>點擊「開始遊戲」按鈕，確認畫面切換正常</li>
                <li><strong>回合一測試：</strong>嘗試點擊話術破綻關鍵字，檢查血條變化</li>
                <li><strong>計時器測試：</strong>觀察計時器是否正常倒數</li>
                <li><strong>Fact Check：</strong>完成回合後檢查事實查核卡片顯示</li>
                <li><strong>煙霧彈測試：</strong>到達第五回合時測試特殊機制</li>
                <li><strong>結算測試：</strong>完成遊戲後檢查評級和分享功能</li>
                <li><strong>重新開始：</strong>測試重新開始遊戲功能</li>
            </ol>
        </div>
    </div>

    <script>
        // 檢查必要檔案
        function checkFiles() {
            const files = [
                'styles.css',
                'game-data.js', 
                'game-engine.js',
                'ui-controller.js',
                'main.js'
            ];
            
            const resultsDiv = document.getElementById('file-check-results');
            resultsDiv.innerHTML = '';
            
            files.forEach(file => {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = file;
                
                const script = document.createElement('script');
                script.src = file;
                
                const result = document.createElement('div');
                result.className = 'test-result test-pass';
                result.innerHTML = `✅ ${file} - 檔案存在`;
                resultsDiv.appendChild(result);
            });
        }

        // 載入並預覽遊戲資料
        function loadGameData() {
            const script = document.createElement('script');
            script.src = 'game-data.js';
            script.onload = function() {
                if (typeof GAME_DATA !== 'undefined') {
                    const preview = document.getElementById('game-data-preview');
                    preview.innerHTML = `
                        <div class="data-preview">
                            <strong>遊戲設定：</strong><br>
                            總時間: ${GAME_DATA.gameSettings.totalTime}秒<br>
                            回合數: ${GAME_DATA.rounds.length}<br>
                            角色: ${GAME_DATA.character}<br><br>
                            
                            <strong>回合資料預覽：</strong><br>
                            ${GAME_DATA.rounds.map((round, index) => 
                                `回合${index + 1}: ${round.reasonTitle}`
                            ).join('<br>')}
                        </div>
                    `;
                    
                    const result = document.createElement('div');
                    result.className = 'test-result test-pass';
                    result.innerHTML = '✅ 遊戲資料載入成功';
                    preview.appendChild(result);
                } else {
                    const result = document.createElement('div');
                    result.className = 'test-result test-fail';
                    result.innerHTML = '❌ 遊戲資料載入失敗';
                    document.getElementById('game-data-preview').appendChild(result);
                }
            };
            script.onerror = function() {
                const result = document.createElement('div');
                result.className = 'test-result test-fail';
                result.innerHTML = '❌ 無法載入 game-data.js';
                document.getElementById('game-data-preview').appendChild(result);
            };
            document.head.appendChild(script);
        }

        // 功能測試
        function runFunctionTests() {
            const resultsDiv = document.getElementById('function-test-results');
            resultsDiv.innerHTML = '';
            
            const tests = [
                {
                    name: 'DOM元素檢查',
                    test: () => {
                        const requiredIds = [
                            'game-container', 'intro-screen', 'game-screen', 
                            'result-screen', 'start-game-btn'
                        ];
                        return requiredIds.every(id => document.getElementById(id) !== null);
                    }
                },
                {
                    name: 'CSS樣式載入',
                    test: () => {
                        const testElement = document.createElement('div');
                        testElement.className = 'btn btn-primary';
                        document.body.appendChild(testElement);
                        const styles = window.getComputedStyle(testElement);
                        const hasStyles = styles.backgroundColor !== 'rgba(0, 0, 0, 0)';
                        document.body.removeChild(testElement);
                        return hasStyles;
                    }
                },
                {
                    name: '響應式設計',
                    test: () => {
                        return window.innerWidth > 0 && window.innerHeight > 0;
                    }
                },
                {
                    name: '瀏覽器相容性',
                    test: () => {
                        return 'addEventListener' in window && 
                               'querySelector' in document &&
                               'classList' in document.createElement('div');
                    }
                }
            ];
            
            tests.forEach(test => {
                const result = document.createElement('div');
                try {
                    const passed = test.test();
                    result.className = passed ? 'test-result test-pass' : 'test-result test-fail';
                    result.innerHTML = passed ? `✅ ${test.name}` : `❌ ${test.name}`;
                } catch (error) {
                    result.className = 'test-result test-fail';
                    result.innerHTML = `❌ ${test.name} - 錯誤: ${error.message}`;
                }
                resultsDiv.appendChild(result);
            });
        }

        // 響應式測試
        function testResponsive() {
            const resultsDiv = document.getElementById('responsive-test-results');
            resultsDiv.innerHTML = '';
            
            const breakpoints = [
                { name: '桌面版', width: 1200 },
                { name: '平板版', width: 768 },
                { name: '手機版', width: 480 }
            ];
            
            breakpoints.forEach(bp => {
                const result = document.createElement('div');
                result.className = 'test-result test-pass';
                result.innerHTML = `📱 ${bp.name} (${bp.width}px) - 建議手動測試`;
                resultsDiv.appendChild(result);
            });
            
            const tip = document.createElement('div');
            tip.innerHTML = `
                <p><strong>響應式測試建議：</strong></p>
                <ul>
                    <li>使用瀏覽器開發者工具切換不同裝置尺寸</li>
                    <li>檢查按鈕和文字是否適當縮放</li>
                    <li>確認遊戲在手機上可正常操作</li>
                    <li>測試橫向和直向模式</li>
                </ul>
            `;
            resultsDiv.appendChild(tip);
        }

        // 頁面載入時執行檢查
        window.addEventListener('load', function() {
            checkFiles();
            loadGameData();
        });
    </script>
</body>
</html>
