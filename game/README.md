# 罷免元兇？- 立委攻防戰遊戲

## 🎮 遊戲簡介

《罷免元兇？》是一款針對葉元之立委罷免案設計的互動式政治教育遊戲。透過有趣的遊戲機制，讓玩家在3分鐘內體驗罷免理由與答辯書之間的攻防，快速理解政治話術，培養批判思考能力。

## ✨ 遊戲特色

### 🎯 核心玩法
- **話術破綻識別**：在答辯文字中快速點擊話術關鍵字
- **血條對戰系統**：公民怒氣值 vs 委員誠信值
- **時間壓力**：3分鐘限時挑戰，增加緊張感
- **即時反饋**：點擊正確/錯誤立即顯示效果

### 🌪️ 特殊機制
- **煙霧彈攻擊**：第五回合的特殊玩法
- **撥開迷霧**：快速點擊破除地方政績的煙霧彈
- **核心問題揭露**：撥開煙霧後顯示關鍵質疑

### 📊 教育價值
- **事實查核**：每回合結束後提供事實查核資訊
- **證據連結**：可查看相關新聞報導和證據
- **評級系統**：根據表現給予不同等級評價
- **分享機制**：鼓勵傳播和討論

## 🎲 遊戲流程

### 第一階段：開場介紹
1. Q版葉元之角色登場
2. 遊戲規則說明
3. 點擊「開始遊戲」進入戰鬥

### 第二階段：五回合攻防
1. **回合一**：胡亂刪預算爭議
2. **回合二**：盲從國會擴權爭議  
3. **回合三**：機密會議直播爭議
4. **回合四**：刪打黃牛預算爭議
5. **回合五**：黨意vs民意 + 煙霧彈特殊回合

### 第三階段：結算與行動
1. 根據分數評級（罷免達人/監督新星/關心市民/要小心話術）
2. 分享遊戲結果到社群媒體
3. 引導至罷免連署頁面
4. 提供完整理由書連結

## 🛠️ 技術架構

### 前端技術
- **HTML5**：語義化標記，無障礙設計
- **CSS3**：響應式設計，動畫效果
- **Vanilla JavaScript**：無框架依賴，輕量化

### 核心模組
- **game-data.js**：遊戲資料與設定
- **game-engine.js**：遊戲邏輯引擎
- **ui-controller.js**：使用者介面控制
- **main.js**：主程式啟動器

### 設計特點
- **模組化架構**：易於維護和擴展
- **錯誤處理**：完整的錯誤捕獲機制
- **性能監控**：載入時間和記憶體使用監控
- **響應式設計**：支援桌面、平板、手機

## 📱 相容性

### 支援瀏覽器
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 支援裝置
- 桌面電腦（1200px+）
- 平板電腦（768px-1199px）
- 智慧手機（320px-767px）

## 🚀 部署說明

### 本地測試
1. 下載所有檔案到同一目錄
2. 開啟 `index.html` 開始遊戲
3. 開啟 `test.html` 進行功能測試

### 網站部署
1. 上傳所有檔案到網站根目錄或子目錄
2. 確保所有檔案路徑正確
3. 測試在不同裝置上的表現

### 自訂設定
- 修改 `game-data.js` 中的遊戲內容
- 調整 `styles.css` 中的視覺樣式
- 更新外部連結URL

## 📊 遊戲資料結構

### 回合資料格式
```javascript
{
  id: 1,
  reasonTitle: "罷免理由標題",
  reasonContent: "罷免理由內容",
  rebuttalText: "答辯內容",
  flaws: [
    { text: "話術關鍵字", isCorrect: true }
  ],
  factCheck: {
    content: "事實查核內容",
    evidenceLink: "證據連結"
  }
}
```

### 評級系統
- **80分以上**：罷免達人
- **60-79分**：監督新星  
- **40-59分**：關心市民
- **40分以下**：要小心話術

## 🎯 使用指南

### 玩家操作
1. **點擊話術破綻**：在答辯文字中找出並點擊話術關鍵字
2. **觀察血條變化**：正確點擊扣除委員誠信值，錯誤點擊扣除公民怒氣值
3. **注意時間限制**：總共3分鐘，每回合約30秒
4. **撥開煙霧彈**：第五回合需要快速點擊「拒絕模糊焦點」按鈕

### 教育者使用
1. **課堂討論**：可作為公民教育教材
2. **媒體識讀**：訓練學生識別政治話術
3. **批判思考**：培養質疑和分析能力
4. **民主參與**：鼓勵關心政治議題

## 🔧 開發與維護

### 新增回合
1. 在 `game-data.js` 的 `rounds` 陣列中新增回合資料
2. 確保包含所有必要欄位
3. 測試新回合的顯示和互動

### 修改評級
1. 調整 `resultGrades` 陣列中的分數門檻
2. 更新評級文案和表情符號
3. 測試不同分數的評級顯示

### 客製化樣式
1. 修改 `styles.css` 中的顏色和字體
2. 調整動畫效果和過場
3. 確保響應式設計不受影響

## 📈 效果追蹤

### 建議追蹤指標
- 遊戲完成率
- 平均遊戲時間
- 分享次數
- 外部連結點擊率
- 不同評級分布

### 分析工具整合
- Google Analytics 事件追蹤
- 社群媒體分享統計
- 使用者行為熱圖

## 🤝 貢獻指南

歡迎提供改進建議：
1. 遊戲內容更新
2. 使用者體驗優化
3. 技術性能改善
4. 無障礙功能增強

## 📄 授權資訊

本遊戲為開源專案，採用 MIT 授權條款。
歡迎自由使用、修改和分發，但請保留原作者資訊。

## 🎉 致謝

感謝所有參與罷免案的公民朋友，
讓民主監督成為可能。

---

**讓政治不再枯燥，讓監督變得有趣！**
**一起來識破政治話術，為民主把關！** 🎮⚖️
