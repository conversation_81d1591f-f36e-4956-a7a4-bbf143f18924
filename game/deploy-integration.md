# 遊戲整合部署指南

## 🔗 整合到現有 Astro 網站

### 方法一：獨立頁面整合

1. **複製遊戲檔案**
```bash
# 將 game 目錄複製到 Astro 專案的 public 目錄下
cp -r game/ /path/to/astro-project/public/game/
```

2. **建立 Astro 頁面**
```astro
---
// src/pages/game.astro
---

<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>罷免元兇？- 立委攻防戰</title>
    <meta name="description" content="透過有趣的遊戲體驗，了解葉元之立委的罷免理由與答辯內容">
</head>
<body>
    <iframe 
        src="/game/index.html" 
        width="100%" 
        height="100vh" 
        frameborder="0"
        style="border: none;">
    </iframe>
</body>
</html>
```

3. **添加導航連結**
```astro
<!-- 在主要導航中添加 -->
<a href="/game" class="nav-link">🎮 互動遊戲</a>
```

### 方法二：嵌入式整合

1. **建立遊戲組件**
```astro
---
// src/components/RecallGame.astro
---

<div class="game-container">
    <div class="game-header">
        <h2>🎮 罷免元兇？立委攻防戰</h2>
        <p>3分鐘體驗政治話術攻防，快速了解罷免爭議！</p>
    </div>
    
    <div class="game-embed">
        <iframe 
            src="/game/index.html"
            width="100%"
            height="600px"
            frameborder="0"
            allowfullscreen>
        </iframe>
    </div>
    
    <div class="game-footer">
        <a href="/game/index.html" target="_blank" class="btn btn-primary">
            🔗 全螢幕遊戲
        </a>
        <a href="/reasons" class="btn btn-secondary">
            📋 查看完整理由
        </a>
    </div>
</div>

<style>
.game-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin: 2rem 0;
}

.game-header {
    text-align: center;
    margin-bottom: 2rem;
}

.game-header h2 {
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.game-embed {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.game-footer {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #4ade80;
    color: white;
}

.btn-secondary {
    background: #6366f1;
    color: white;
}

@media (max-width: 768px) {
    .game-footer {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        text-align: center;
    }
}
</style>
```

2. **在頁面中使用**
```astro
---
// src/pages/index.astro 或其他頁面
import RecallGame from '../components/RecallGame.astro';
---

<Layout title="罷免網站">
    <main>
        <!-- 其他內容 -->
        
        <section class="game-section">
            <RecallGame />
        </section>
        
        <!-- 其他內容 -->
    </main>
</Layout>
```

### 方法三：完全整合

1. **將遊戲資源整合到 Astro 專案**
```bash
# 複製 CSS 到 Astro 樣式目錄
cp game/styles.css src/styles/game.css

# 複製 JS 到 public 目錄
cp game/*.js public/js/game/
```

2. **建立 Astro 遊戲頁面**
```astro
---
// src/pages/game/index.astro
import Layout from '../../layouts/Layout.astro';
---

<Layout title="罷免元兇？- 立委攻防戰">
    <link rel="stylesheet" href="/src/styles/game.css">
    
    <!-- 遊戲 HTML 內容 -->
    <div id="game-container">
        <!-- 複製 game/index.html 的 body 內容 -->
    </div>
    
    <script src="/js/game/game-data.js"></script>
    <script src="/js/game/game-engine.js"></script>
    <script src="/js/game/ui-controller.js"></script>
    <script src="/js/game/main.js"></script>
</Layout>
```

## 📱 響應式整合建議

### 桌面版整合
```css
.game-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.game-embed iframe {
    height: 700px;
}
```

### 移動版優化
```css
@media (max-width: 768px) {
    .game-section {
        padding: 1rem;
    }
    
    .game-embed iframe {
        height: 500px;
    }
    
    .game-container {
        padding: 1rem;
    }
}
```

## 🔧 自訂配置

### 修改遊戲連結
```javascript
// 在 game-data.js 中更新連結
const GAME_DATA = {
    // ...其他設定
    links: {
        petitionSite: "https://your-petition-site.com",
        fullDocument: "https://your-site.com/reasons",
        officialSite: "https://your-site.com"
    }
};
```

### 整合網站樣式
```css
/* 在 game/styles.css 中添加 */
:root {
    --primary-color: #your-brand-color;
    --secondary-color: #your-secondary-color;
    --font-family: 'Your-Font', 'Noto Sans TC', sans-serif;
}

/* 更新按鈕樣式以符合網站設計 */
.btn-primary {
    background: var(--primary-color);
}
```

## 📊 分析追蹤整合

### Google Analytics 4
```javascript
// 在 main.js 中添加
gtag('event', 'game_start', {
    event_category: 'engagement',
    event_label: 'recall_game'
});

gtag('event', 'game_complete', {
    event_category: 'engagement',
    event_label: 'recall_game',
    value: finalScore
});
```

### Facebook Pixel
```javascript
// 遊戲完成時觸發
fbq('track', 'CompleteRegistration', {
    content_name: 'Recall Game Completed',
    value: finalScore
});
```

## 🚀 性能優化

### 預載入優化
```html
<!-- 在主頁面 head 中添加 -->
<link rel="preload" href="/game/styles.css" as="style">
<link rel="preload" href="/game/game-data.js" as="script">
```

### 懶載入實作
```javascript
// 當用戶滾動到遊戲區域時才載入
const gameObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            loadGameResources();
            gameObserver.unobserve(entry.target);
        }
    });
});

gameObserver.observe(document.querySelector('.game-section'));
```

## 🔒 安全性考量

### Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline'; 
               style-src 'self' 'unsafe-inline' fonts.googleapis.com;
               font-src fonts.gstatic.com;">
```

### iframe 安全設定
```html
<iframe 
    src="/game/index.html"
    sandbox="allow-scripts allow-same-origin"
    loading="lazy">
</iframe>
```

## 📱 PWA 整合

### Service Worker 註冊
```javascript
// 在主網站的 service worker 中添加遊戲資源
const GAME_CACHE = 'recall-game-v1';
const gameResources = [
    '/game/index.html',
    '/game/styles.css',
    '/game/game-data.js',
    '/game/game-engine.js',
    '/game/ui-controller.js',
    '/game/main.js'
];

self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(GAME_CACHE)
            .then(cache => cache.addAll(gameResources))
    );
});
```

## 🎯 SEO 優化

### Meta 標籤
```html
<meta name="description" content="透過互動遊戲了解葉元之立委罷免理由，3分鐘識破政治話術">
<meta name="keywords" content="罷免,葉元之,政治遊戲,民主監督,話術識別">
<meta property="og:title" content="罷免元兇？- 立委攻防戰">
<meta property="og:description" content="3分鐘體驗政治話術攻防，快速了解罷免爭議">
<meta property="og:image" content="/game/og-image.jpg">
<meta property="og:type" content="website">
```

### 結構化資料
```json
{
    "@context": "https://schema.org",
    "@type": "Game",
    "name": "罷免元兇？",
    "description": "政治教育互動遊戲",
    "genre": "Educational",
    "playMode": "SinglePlayer",
    "applicationCategory": "Game"
}
```

## 🔄 更新維護

### 版本控制
```javascript
// 在 game-data.js 中添加版本資訊
const GAME_VERSION = '1.0.0';
const LAST_UPDATED = '2024-07-04';
```

### 自動更新檢查
```javascript
// 檢查是否有新版本
async function checkForUpdates() {
    try {
        const response = await fetch('/game/version.json');
        const data = await response.json();
        if (data.version !== GAME_VERSION) {
            showUpdateNotification();
        }
    } catch (error) {
        console.log('無法檢查更新');
    }
}
```

---

**選擇最適合您網站架構的整合方式，讓遊戲成為罷免宣傳的有力工具！** 🚀
