/* 罷免元兇遊戲樣式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Noto Sans TC", sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
  overflow-x: hidden;
}

/* 遊戲主容器 */
#game-container {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* 畫面切換 */
.screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s ease-in-out;
  padding: 20px;
}

.screen.active {
  opacity: 1;
  visibility: visible;
}

/* 開場畫面 */
#intro-screen {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #374151 100%);
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
}

/* 背景動畫效果 */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.floating-emoji {
  position: absolute;
  font-size: 2rem;
  opacity: 0.3;
  animation: floatAround 15s infinite linear;
}

.floating-emoji:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-emoji:nth-child(2) {
  top: 60%;
  right: 15%;
  animation-delay: -3s;
}

.floating-emoji:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: -6s;
}

.floating-emoji:nth-child(4) {
  top: 40%;
  right: 30%;
  animation-delay: -9s;
}

.floating-emoji:nth-child(5) {
  bottom: 20%;
  right: 10%;
  animation-delay: -12s;
}

/* 主要內容區 */
.intro-hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  z-index: 2;
}

/* 主角展示區 */
.character-showcase {
  margin-bottom: 2rem;
}

.character-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  border-radius: 24px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  max-width: 400px;
}

.character-avatar-big {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 1.5rem;
}

.avatar-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 4px solid #ef4444;
  border-radius: 50%;
  animation: ringPulse 2s ease-in-out infinite;
}

.avatar-face {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #7f1d1d, #991b1b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  border: 4px solid #dc2626;
  box-shadow: 0 10px 30px rgba(220, 38, 38, 0.4);
  animation: avatarBounce 3s ease-in-out infinite;
}

.avatar-speech-bubble {
  position: absolute;
  top: -20px;
  right: -80px;
  background: white;
  color: #374151;
  padding: 0.75rem 1rem;
  border-radius: 16px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  animation: bubbleFloat 2s ease-in-out infinite alternate;
  min-width: 160px;
}

.bubble-tail {
  position: absolute;
  bottom: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

.character-info {
  text-align: center;
  color: white;
}

.character-name {
  font-size: 1.5rem;
  font-weight: 900;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.character-title {
  font-size: 1rem;
  color: #fbbf24;
  font-weight: 600;
  opacity: 0.9;
}

.character-intro {
  margin-bottom: 2rem;
}

.character-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.avatar-face {
  font-size: 3rem;
  animation: bounce 2s infinite;
}

.avatar-gesture {
  position: absolute;
  bottom: -10px;
  right: -10px;
  font-size: 1.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.character-name {
  font-size: 1.2rem;
  font-weight: 500;
  opacity: 0.9;
}

.game-title {
  margin-bottom: 2rem;
}

.title-main {
  display: block;
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.title-subtitle {
  display: block;
  font-size: 1.5rem;
  font-weight: 500;
  opacity: 0.8;
}

.game-description {
  margin-bottom: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.9;
}

.game-info {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.info-icon {
  font-size: 1.2rem;
}

/* 按鈕樣式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  font-family: inherit;
}

.btn-primary {
  background: #4ade80;
  color: white;
  box-shadow: 0 4px 15px rgba(74, 222, 128, 0.4);
}

.btn-primary:hover {
  background: #22c55e;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 222, 128, 0.6);
}

.btn-secondary {
  background: #6366f1;
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

.btn-warning {
  background: #f59e0b;
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
  font-size: 1.2rem;
  padding: 16px 32px;
}

.btn-outline {
  background: transparent;
  color: #6366f1;
  border: 2px solid #6366f1;
}

.btn-large {
  padding: 16px 32px;
  font-size: 1.2rem;
  font-weight: 600;
}

/* 遊戲主畫面 */
#game-screen {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  flex-direction: column;
  align-items: stretch;
  padding: 20px;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.round-info {
  font-size: 1.2rem;
  font-weight: 600;
  color: #374151;
}

.round-separator {
  margin: 0 0.5rem;
  opacity: 0.5;
}

.timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #dc2626;
}

/* 血條系統 */
.health-bars {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.health-bar {
  background: white;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.health-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  font-size: 0.9rem;
}

.health-bar-container {
  position: relative;
  background: #e5e7eb;
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.health-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.5s ease;
  position: relative;
}

.citizen-fill {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.legislator-fill {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.health-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 罷免理由全螢幕模態框 */
.reason-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: modalFadeIn 0.5s ease-out;
}

.reason-modal-content {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 20px;
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  border: 2px solid #4ade80;
  position: relative;
}

.reason-modal-header {
  background: linear-gradient(135deg, #4ade80, #22c55e);
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 18px 18px 0 0;
}

.reason-badge {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
}

.reason-icon {
  font-size: 2rem;
  animation: bounce 2s infinite;
}

.reason-label {
  font-size: 1.5rem;
  font-weight: 700;
}

.round-indicator {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  backdrop-filter: blur(5px);
}

.reason-modal-body {
  padding: 3rem;
  color: white;
  text-align: center;
}

.reason-title-large {
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 2rem;
  color: #4ade80;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

.reason-content-large {
  font-size: 1.3rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.reason-modal-footer {
  padding: 2rem 3rem 3rem;
  text-align: center;
}

.btn-battle {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  font-size: 1.3rem;
  font-weight: 700;
  padding: 1rem 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
  animation: battlePulse 2s infinite;
  transition: all 0.3s ease;
}

.btn-battle:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(239, 68, 68, 0.6);
}

/* 話術防禦計 */
.bullshit-meter-container {
  background: linear-gradient(135deg, #7c2d12 0%, #991b1b 100%);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
  border: 2px solid #dc2626;
}

.meter-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  color: white;
}

.meter-icon {
  font-size: 2rem;
  animation: bounce 2s infinite;
}

.meter-title {
  font-size: 1.3rem;
  font-weight: 700;
}

.meter-subtitle {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-left: auto;
}

.meter-bar-container {
  position: relative;
  background: rgba(0, 0, 0, 0.3);
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.meter-fill {
  height: 100%;
  background: linear-gradient(90deg, #dc2626, #ef4444, #f87171);
  border-radius: 10px;
  width: 100%;
  transition: width 0.8s ease;
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);
  animation: meterPulse 2s ease-in-out infinite alternate;
}

.meter-text {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.meter-status {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
}

/* 主要遊戲區域 */
.main-game-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
}

/* 委員答辯卡片 */
.legislator-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-radius: 20px;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
  border: 3px solid #ef4444;
  overflow: hidden;
  position: relative;
}

.legislator-header {
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
}

.legislator-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #450a0a, #7f1d1d);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4px solid #ef4444;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
}

.avatar-emoji {
  font-size: 2.5rem;
  animation: avatarBob 3s ease-in-out infinite;
}

.avatar-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(239, 68, 68, 0.4), transparent);
  animation: glowPulse 2s ease-in-out infinite alternate;
}

.legislator-info {
  flex: 1;
  color: white;
}

.legislator-name {
  font-size: 1.8rem;
  font-weight: 900;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.legislator-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 500;
}

.warning-badge {
  background: rgba(239, 68, 68, 0.2);
  border: 2px solid #ef4444;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #fca5a5;
  font-weight: 600;
  animation: warningBlink 2s ease-in-out infinite;
}

.rebuttal-content {
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
}

.rebuttal-text {
  color: white;
  font-size: 1.2rem;
  line-height: 1.8;
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  user-select: none;
}

/* 遊戲指示區 */
.game-instruction {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  border: 2px solid #3b82f6;
  position: relative;
  overflow: hidden;
}

.instruction-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  color: white;
}

.instruction-icon {
  font-size: 2.5rem;
  animation: targetPulse 1.5s ease-in-out infinite;
}

.instruction-text {
  font-size: 1.5rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: textGlow 2s ease-in-out infinite alternate;
}

.instruction-hint {
  font-size: 1rem;
  opacity: 0.9;
  margin-left: 1rem;
  font-weight: 500;
}

.combo-display {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
  animation: comboAppear 0.5s ease-out;
}

.combo-label {
  font-size: 0.9rem;
  font-weight: 600;
}

.combo-count {
  font-size: 1.8rem;
  font-weight: 900;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.combo-suffix {
  font-size: 1rem;
  font-weight: 700;
}

/* 事實查核彈出框 */
.fact-check-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 3000;
  border: 3px solid #22c55e;
  animation: popupSlideIn 0.5s ease-out;
}

.popup-header {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.popup-icon {
  font-size: 1.8rem;
}

.popup-title {
  font-size: 1.3rem;
  font-weight: 700;
  flex: 1;
}

.popup-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.popup-close:hover {
  background: rgba(255, 255, 255, 0.3);
}

.popup-content {
  padding: 2rem;
}

.fact-check-media {
  margin-bottom: 1.5rem;
  text-align: center;
  border-radius: 12px;
  overflow: hidden;
  background: #f8fafc;
}

.fact-check-media img,
.fact-check-media video {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.fact-check-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #374151;
  margin-bottom: 1.5rem;
  background: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #22c55e;
}

.fact-check-source {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6b7280;
}

.source-label {
  font-weight: 600;
}

.source-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.source-link:hover {
  text-decoration: underline;
}

.popup-footer {
  padding: 1.5rem 2rem 2rem;
  text-align: center;
}

.slap-button {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
  transition: all 0.3s ease;
  animation: slapPulse 2s ease-in-out infinite;
}

.slap-button:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(245, 158, 11, 0.6);
}

.slap-emoji {
  font-size: 1.5rem;
  animation: slapWave 1s ease-in-out infinite;
}

.lightning-effect {
  position: absolute;
  top: 20%;
  left: 50%;
  width: 2px;
  height: 60%;
  background: linear-gradient(to bottom, transparent, #60a5fa, transparent);
  transform: translateX(-50%);
  animation: lightning 3s infinite;
  opacity: 0.3;
}

.energy-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #4ade80, transparent),
    radial-gradient(2px 2px at 40px 70px, #60a5fa, transparent),
    radial-gradient(1px 1px at 90px 40px, #f59e0b, transparent),
    radial-gradient(1px 1px at 130px 80px, #ef4444, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particleFloat 10s linear infinite;
  opacity: 0.4;
}

/* 戰鬥卡片樣式 */
.battle-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  position: relative;
  z-index: 2;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.reason-battle-card {
  border-color: #4ade80;
  box-shadow: 0 8px 25px rgba(74, 222, 128, 0.2);
}

.rebuttal-battle-card {
  border-color: #ef4444;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.2);
}

.battle-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.battle-card-header {
  padding: 1.5rem;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  display: flex;
  align-items: center;
  gap: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  background: linear-gradient(135deg, #374151, #4b5563);
  border: 3px solid;
}

.citizen-avatar {
  border-color: #4ade80;
  background: linear-gradient(135deg, #065f46, #047857);
}

.legislator-avatar {
  border-color: #ef4444;
  background: linear-gradient(135deg, #7f1d1d, #991b1b);
}

.avatar-glow {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  opacity: 0.6;
  animation: avatarGlow 2s ease-in-out infinite alternate;
}

.citizen-glow {
  background: radial-gradient(circle, rgba(74, 222, 128, 0.4), transparent);
}

.legislator-glow {
  background: radial-gradient(circle, rgba(239, 68, 68, 0.4), transparent);
}

.card-info {
  flex: 1;
  color: white;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.card-subtitle {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 500;
}

.card-header {
  background: linear-gradient(135deg, #4ade80, #22c55e);
  color: white;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-title {
  font-weight: 600;
  font-size: 1.1rem;
}

/* 戰鬥卡片內容 */
.battle-card-content {
  padding: 1.5rem;
  color: white;
  position: relative;
}

.battle-text {
  line-height: 1.7;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.rebuttal-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(239, 68, 68, 0.2);
  border-radius: 6px;
  border: 1px solid rgba(239, 68, 68, 0.3);
  font-size: 0.9rem;
  font-weight: 600;
  color: #fca5a5;
}

.battle-card-footer {
  padding: 1rem 1.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.power-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.power-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  min-width: 80px;
}

.power-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.power-fill {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.citizen-power {
  background: linear-gradient(90deg, #4ade80, #22c55e);
  box-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
}

.legislator-power {
  background: linear-gradient(90deg, #ef4444, #dc2626);
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

/* 中央戰鬥指示區 */
.battle-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  z-index: 3;
  position: relative;
  min-width: 200px;
}

.vs-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vs-text {
  font-size: 3rem;
  font-weight: 900;
  color: #fbbf24;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: vsGlow 2s ease-in-out infinite alternate;
}

.battle-sparks {
  position: absolute;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(251, 191, 36, 0.3), transparent);
  border-radius: 50%;
  animation: sparkRotate 3s linear infinite;
}

.battle-instruction {
  text-align: center;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(37, 99, 235, 0.2)
  );
  padding: 1.5rem;
  border-radius: 12px;
  border: 2px solid #3b82f6;
  backdrop-filter: blur(5px);
}

.instruction-pulse {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.instruction-icon {
  font-size: 2rem;
  animation: targetPulse 1.5s ease-in-out infinite;
}

.instruction-text {
  font-size: 1.3rem;
  font-weight: 700;
  color: #60a5fa;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  animation: textGlow 2s ease-in-out infinite alternate;
}

.click-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.hint-arrow {
  font-size: 1.2rem;
  animation: bounce 2s infinite;
}

.combo-counter {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
  animation: comboAppear 0.5s ease-out;
}

.combo-text {
  font-size: 0.8rem;
  font-weight: 600;
  opacity: 0.9;
}

.combo-number {
  font-size: 2rem;
  font-weight: 900;
  margin: 0 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.combo-suffix {
  font-size: 0.9rem;
  font-weight: 700;
}

.rebuttal-area {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.rebuttal-header {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.rebuttal-title {
  font-weight: 600;
  font-size: 1.1rem;
}

.instruction {
  margin-left: auto;
  font-size: 0.9rem;
  opacity: 0.9;
  animation: pulse 2s infinite;
}

.rebuttal-content {
  padding: 1.5rem;
  line-height: 1.8;
  font-size: 1.1rem;
  cursor: pointer;
  user-select: none;
}

/* 話術破綻高亮 */
.flaw-word {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  padding: 2px 6px;
  border-radius: 4px;
  border: 2px solid #f59e0b;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  margin: 0 2px;
  animation: highlight-pulse 2s infinite;
}

.flaw-word:hover {
  background: linear-gradient(135deg, #fed7aa, #fdba74);
  border-color: #ea580c;
  transform: scale(1.05);
}

.flaw-word.clicked {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  border-color: #22c55e;
  animation: none;
}

.flaw-word.wrong {
  background: linear-gradient(135deg, #fecaca, #fca5a5);
  border-color: #ef4444;
  animation: shake 0.5s ease-in-out;
}

/* 動畫效果 */
@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes highlight-pulse {
  0%,
  100% {
    box-shadow: 0 0 0 rgba(245, 158, 11, 0.4);
  }
  50% {
    box-shadow: 0 0 15px rgba(245, 158, 11, 0.8);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .main-game-area {
    gap: 1rem;
  }

  .bullshit-meter-container {
    padding: 1rem;
  }

  .meter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .meter-subtitle {
    margin-left: 0;
  }

  .legislator-header {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .legislator-avatar {
    width: 60px;
    height: 60px;
  }

  .avatar-emoji {
    font-size: 2rem;
  }

  .legislator-name {
    font-size: 1.5rem;
  }

  .warning-badge {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .rebuttal-content {
    padding: 1.5rem;
  }

  .rebuttal-text {
    font-size: 1rem;
    padding: 1rem;
  }

  .game-instruction {
    padding: 1rem;
  }

  .instruction-content {
    flex-direction: column;
    gap: 0.5rem;
  }

  .instruction-icon {
    font-size: 2rem;
  }

  .instruction-text {
    font-size: 1.2rem;
  }

  .instruction-hint {
    margin-left: 0;
    font-size: 0.9rem;
  }

  .fact-check-popup {
    width: 95%;
    max-height: 90vh;
  }

  .popup-content {
    padding: 1.5rem;
  }

  .fact-check-text {
    font-size: 1rem;
  }

  .slap-button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  .reason-modal-content {
    width: 95%;
    margin: 1rem;
  }

  .reason-modal-body {
    padding: 2rem 1.5rem;
  }

  .reason-title-large {
    font-size: 1.8rem;
  }

  .reason-content-large {
    font-size: 1.1rem;
  }

  .game-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .game-info {
    gap: 1rem;
  }

  .title-main {
    font-size: 2rem;
  }

  .combo-display {
    padding: 0.75rem 1rem;
  }

  .combo-count {
    font-size: 1.5rem;
  }

  /* 新開場畫面響應式 */
  .intro-hero {
    padding: 1rem;
  }

  .character-card {
    max-width: 300px;
    padding: 1.5rem;
  }

  .character-avatar-big {
    width: 80px;
    height: 80px;
  }

  .avatar-face {
    font-size: 2.5rem;
  }

  .avatar-speech-bubble {
    right: -60px;
    min-width: 120px;
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
  }

  .game-title-main {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .title-emoji {
    font-size: 2.5rem;
  }

  .subtitle-text {
    font-size: 1rem;
  }

  .game-features {
    gap: 0.5rem;
  }

  .feature-card {
    min-width: 80px;
    padding: 0.75rem;
  }

  .feature-icon {
    font-size: 1.5rem;
  }

  .feature-text {
    font-size: 0.8rem;
  }

  .start-button {
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }

  .button-icon {
    font-size: 1.2rem;
  }

  .game-instructions {
    gap: 0.5rem;
  }

  .instruction-item {
    padding: 0.5rem 0.75rem;
    flex-direction: column;
    text-align: center;
    gap: 0.25rem;
  }

  .instruction-text {
    font-size: 0.8rem;
  }
}

/* 煙霧彈特殊回合 */
.smokescreen-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.smokescreen-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 600px;
  width: 90%;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.smokescreen-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  opacity: 0.8;
}

.smoke-item {
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  border: 2px solid #d1d5db;
  animation: smoke-float 3s ease-in-out infinite;
}

.smoke-item:nth-child(even) {
  animation-delay: 1s;
}

.smoke-item:nth-child(3n) {
  animation-delay: 2s;
}

.smokescreen-action {
  position: relative;
}

.core-question {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 3px solid #f59e0b;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.6;
  color: #92400e;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.5s ease;
}

.core-question.visible {
  opacity: 1;
  transform: scale(1);
}

.smoke-progress {
  background: #e5e7eb;
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
  margin-top: 1rem;
}

.smoke-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b, #d97706);
  width: 0%;
  transition: width 0.3s ease;
  border-radius: 6px;
}

/* 模態框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 16px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.modal-title {
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 2rem;
  line-height: 1.6;
  font-size: 1rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  flex-wrap: wrap;
}

/* 結算畫面 */
#result-screen {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  text-align: center;
}

.result-content {
  max-width: 600px;
  width: 100%;
}

.result-character {
  margin-bottom: 2rem;
}

.result-character .character-avatar {
  width: 150px;
  height: 150px;
  margin: 0 auto 1rem;
}

.avatar-expression {
  position: absolute;
  top: -10px;
  right: -10px;
  font-size: 2rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-title {
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.result-description {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.final-score {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: #fbbf24;
}

.cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.cta-buttons .btn {
  min-width: 280px;
}

/* 新增動畫效果 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes meterPulse {
  0% {
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);
  }
  100% {
    box-shadow: 0 0 25px rgba(239, 68, 68, 0.9);
  }
}

@keyframes avatarBob {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes glowPulse {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes warningBlink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -60%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes slapPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes slapWave {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-10deg);
  }
  75% {
    transform: rotate(10deg);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes targetPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes textGlow {
  0% {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }
  100% {
    text-shadow:
      2px 2px 4px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(255, 255, 255, 0.5);
  }
}

@keyframes comboAppear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 新開場畫面樣式 */
.game-title-section {
  margin-bottom: 2rem;
  text-align: center;
}

.game-title-main {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

.title-emoji {
  font-size: 3.5rem;
  animation: emojiSpin 4s ease-in-out infinite;
}

.title-text {
  background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.game-subtitle {
  margin-bottom: 2rem;
}

.subtitle-text {
  font-size: 1.2rem;
  color: #94a3b8;
  font-weight: 500;
  animation: subtitleFade 2s ease-in-out infinite alternate;
}

.game-features {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  justify-content: center;
  flex-wrap: wrap;
}

.feature-card {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(29, 78, 216, 0.1)
  );
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  padding: 1rem;
  text-align: center;
  min-width: 100px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  animation: cardFloat 3s ease-in-out infinite;
}

.feature-card:nth-child(2) {
  animation-delay: -1s;
}

.feature-card:nth-child(3) {
  animation-delay: -2s;
}

.feature-card:hover {
  transform: translateY(-5px) scale(1.05);
  border-color: rgba(59, 130, 246, 0.6);
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.feature-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
}

.start-button-section {
  margin-bottom: 2rem;
  text-align: center;
}

.start-button {
  background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
  color: white;
  border: none;
  padding: 1.5rem 3rem;
  border-radius: 50px;
  font-size: 1.3rem;
  font-weight: 700;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(239, 68, 68, 0.4);
  animation: buttonPulse 2s ease-in-out infinite;
}

.start-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 40px rgba(239, 68, 68, 0.6);
}

.start-button:active {
  transform: translateY(-1px) scale(1.02);
}

.button-icon {
  font-size: 1.5rem;
  animation: rocketBounce 1s ease-in-out infinite alternate;
}

.button-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
  animation: glowRotate 3s linear infinite;
  pointer-events: none;
}

.start-hint {
  margin-top: 1rem;
}

.hint-text {
  font-size: 0.9rem;
  color: #94a3b8;
  font-weight: 500;
  animation: hintBlink 2s ease-in-out infinite;
}

.game-instructions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 600px;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem 1rem;
  border-radius: 12px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.instruction-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.instruction-number {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 700;
  flex-shrink: 0;
}

.instruction-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: white;
}

@keyframes battlePulse {
  0%,
  100% {
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
  }
  50% {
    box-shadow: 0 8px 35px rgba(239, 68, 68, 0.8);
  }
}

@keyframes lightning {
  0%,
  90%,
  100% {
    opacity: 0;
  }
  5%,
  10% {
    opacity: 0.3;
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100px);
  }
}

@keyframes avatarGlow {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes vsGlow {
  0% {
    color: #fbbf24;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
  100% {
    color: #f59e0b;
    text-shadow: 2px 2px 8px rgba(251, 191, 36, 0.8);
  }
}

@keyframes sparkRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes targetPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes textGlow {
  0% {
    color: #60a5fa;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }
  100% {
    color: #3b82f6;
    text-shadow: 1px 1px 8px rgba(96, 165, 250, 0.8);
  }
}

@keyframes comboAppear {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 原有動畫效果 */
@keyframes smoke-float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(2deg);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fade-in 0.5s ease-out;
}

/* 點擊效果 */
.hit-effect {
  position: absolute;
  pointer-events: none;
  color: #22c55e;
  font-weight: bold;
  font-size: 1.2rem;
  animation: hit-animation 1s ease-out forwards;
}

.miss-effect {
  position: absolute;
  pointer-events: none;
  color: #ef4444;
  font-weight: bold;
  font-size: 1.2rem;
  animation: hit-animation 1s ease-out forwards;
}

@keyframes hit-animation {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-50px) scale(1.5);
  }
}

/* 隱藏元素 */
.hidden {
  display: none !important;
}

/* 新開場畫面動畫 */
@keyframes floatAround {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
  }
  50% {
    transform: translateY(0px) rotate(180deg);
  }
  75% {
    transform: translateY(20px) rotate(270deg);
  }
  100% {
    transform: translateY(0px) rotate(360deg);
  }
}

@keyframes ringPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

@keyframes avatarBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bubbleFloat {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-5px);
  }
}

@keyframes titleGlow {
  0% {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
  }
  100% {
    text-shadow:
      3px 3px 6px rgba(0, 0, 0, 0.5),
      0 0 30px rgba(251, 191, 36, 0.5);
  }
}

@keyframes emojiSpin {
  0%,
  100% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(-10deg) scale(1.1);
  }
  50% {
    transform: rotate(0deg) scale(1);
  }
  75% {
    transform: rotate(10deg) scale(1.1);
  }
}

@keyframes subtitleFade {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

@keyframes cardFloat {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes buttonPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 10px 30px rgba(239, 68, 68, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 15px 40px rgba(239, 68, 68, 0.6);
  }
}

@keyframes rocketBounce {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-3px);
  }
}

@keyframes glowRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes hintBlink {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}
