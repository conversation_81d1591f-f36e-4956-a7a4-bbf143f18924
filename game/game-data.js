// 罷免元兇遊戲資料
const GAME_DATA = {
  character: "葉元之",
  gameTitle: "罷免元兇？",
  gameSettings: {
    totalTime: 180, // 3分鐘 = 180秒
    roundTimeLimit: 30, // 每回合30秒
    citizenMaxHealth: 100,
    legislatorMaxHealth: 100,
    damagePerCorrectClick: 20,
    damagePerWrongClick: 15,
    damagePerMissedClick: 10,
  },

  rounds: [
    {
      id: 1,
      reasonTitle: "【理由一】胡亂刪預算",
      reasonContent:
        "葉委員在預算審查時承認「沒看內容就舉手」，對重要預算案草率表決，嚴重失職。",
      rebuttalText:
        "此為不實指控，本人審預算非常認真，絕未說過『沒看內容』，這是斷章取義的政治操作，完全是惡意抹黑。",
      flaws: [
        { text: "不實指控", isCorrect: true },
        { text: "非常認真", isCorrect: true },
        { text: "斷章取義", isCorrect: true },
        { text: "政治操作", isCorrect: true },
        { text: "惡意抹黑", isCorrect: true },
      ],
      factCheck: {
        content:
          "事實查核：委員曾在節目上親口承認沒看預算就舉手，網路皆有影片存證。",
        evidenceLink: "https://example.com/evidence1",
        mediaType: "video",
        mediaUrl: "https://example.com/video1.mp4",
        mediaCaption: "葉元之委員親口承認「沒看內容就舉手」的節目片段",
        slapMessage: "💥 打臉成功！委員自己都承認了！",
        humorText: "連自己都打臉自己，這招叫做「自爆式答辯」！",
      },
    },

    {
      id: 2,
      reasonTitle: "【理由二】盲從國會擴權",
      reasonContent:
        "支持爭議性國會擴權法案，未經充分討論就表決通過，損害民主制衡機制。",
      rebuttalText:
        "這是『國會改革法案』，是為強化監督，且經歷多次討論。官員專斷傲慢，才突顯需要制衡，這是民主進步的表現。",
      flaws: [
        { text: "國會改革法案", isCorrect: true },
        { text: "多次討論", isCorrect: true },
        { text: "專斷傲慢", isCorrect: true },
        { text: "需要制衡", isCorrect: true },
        { text: "民主進步", isCorrect: true },
      ],
      factCheck: {
        content:
          "事實查核：法案未經實質討論，且已被憲法法庭初判違憲，侵害人民權益。",
        evidenceLink: "https://example.com/evidence2",
        mediaType: "image",
        mediaUrl: "https://example.com/constitutional-court-ruling.jpg",
        mediaCaption: "憲法法庭初判國會擴權法案違憲的新聞報導",
        slapMessage: "⚖️ 憲法法庭都說違憲了！",
        humorText: "連憲法法庭都看不下去了，這叫做「違憲級別的改革」！",
      },
    },

    {
      id: 3,
      reasonTitle: "【理由三】機密會議直播",
      reasonContent:
        "在應保密的國防外交委員會會議中開啟直播，洩露國家機密，嚴重違反保密義務。",
      rebuttalText:
        "因有議事攻防，藍綠均有人直播。我察覺後立即關掉，並非罷團扭曲的『故意』開直播，這是誤會一場。",
      flaws: [
        { text: "均有人直播", isCorrect: true },
        { text: "立即關掉", isCorrect: true },
        { text: "並非...故意", isCorrect: true },
        { text: "扭曲", isCorrect: true },
        { text: "誤會一場", isCorrect: true },
      ],
      factCheck: {
        content:
          "事實查核：無論動機，在應保持機密的場合進行直播，本身就是嚴重失職的行為。",
        evidenceLink: "https://example.com/evidence3",
      },
    },

    {
      id: 4,
      reasonTitle: "【理由四】刪打黃牛預算",
      reasonContent:
        "刪除打擊黃牛票預算，並要求罷免連署需附身分證，明顯與民意對作，圖利特定利益。",
      rebuttalText:
        "預算一毛未刪！是政府打黃牛機制功能不彰，花大錢的辦公室裁罰率極低，當然需要被監督，這是為民把關。",
      flaws: [
        { text: "一毛未刪", isCorrect: true },
        { text: "功能不彰", isCorrect: true },
        { text: "花大錢", isCorrect: true },
        { text: "需要被監督", isCorrect: true },
        { text: "為民把關", isCorrect: true },
      ],
      factCheck: {
        content:
          "事實查核：其答辯迴避了『要求罷免連署附身分證』意圖提高罷免門檻，與民意對作的核心指控。",
        evidenceLink: "https://example.com/evidence4",
      },
    },

    {
      id: 5,
      roundType: "smokescreen",
      reasonTitle: "【理由五】只跟黨意不顧民意",
      reasonContent:
        "在重大議題上總是跟隨黨意投票，背棄選民期待，未能真正代表民意發聲。",
      rebuttalText: "本人服務認真，積極為民服務...",
      smokescreenItems: [
        "🏗️ 會勘國小建設工程，爭取教育資源",
        "🚇 積極推動輕軌建設，改善交通",
        "🏥 關心醫療議題，爭取醫療資源",
        "🏢 推動土地活化利用，促進發展",
        "🛣️ 監督道路改善工程，便民出行",
        "🌳 推動公園綠化計畫，改善環境",
        "🏫 爭取學校設施改善，照顧學童",
        "👴 關懷長者福利，推動銀髮政策",
      ],
      smokescreenCoreQuestion:
        "這些地方服務值得肯定，但為何在國家級重大法案上，您總是選擇跟隨黨意而非民意？",
      factCheck: {
        content:
          "事實查核：地方服務是立委本職，但不能迴避在重大憲政議題上，選擇黨意而背棄競選承諾的問題。",
        evidenceLink: "https://example.com/evidence5",
      },
    },
  ],

  // 結算評級系統
  resultGrades: [
    {
      minScore: 80,
      title: "罷免達人！",
      description: "你已看穿一切話術！葉委員的辯解在你面前無所遁形！",
      expression: "😰",
      message: "真相大白，民主勝利！",
    },
    {
      minScore: 60,
      title: "監督新星！",
      description: "差一點就讓他現形！你已經具備識破政治話術的能力！",
      expression: "😅",
      message: "繼續努力，為民主把關！",
    },
    {
      minScore: 40,
      title: "關心市民",
      description: "你開始注意到政治人物的話術技巧，這是很好的開始！",
      expression: "😊",
      message: "多關注政治，為自己發聲！",
    },
    {
      minScore: 0,
      title: "要小心話術！",
      description: "政治人物的話術很高明，要多加練習才能識破！再試一次！",
      expression: "😏",
      message: "不要被話術迷惑，再來挑戰！",
    },
  ],

  // 分享文案
  shareMessages: [
    "我玩了《罷免元兇？》遊戲，葉元之的話術真的很高明！快來挑戰看看你能不能識破！#罷免葉元之 #板橋",
    "剛體驗了罷免理由對戰遊戲，原來政治人物的答辯有這麼多話術！大家一起來識破真相！#民主監督",
    "《罷免元兇？》讓我秒懂罷免爭議！3分鐘就能了解重點，推薦給關心政治的朋友！#政治教育",
  ],

  // 外部連結
  links: {
    petitionSite: "https://example.com/petition",
    fullDocument: "https://example.com/full-reasons",
    officialSite: "https://example.com/recall-campaign",
  },
};

// 遊戲狀態管理
const GAME_STATE = {
  currentRound: 1,
  citizenHealth: 100,
  legislatorHealth: 100,
  score: 0,
  timeRemaining: 180,
  roundTimeRemaining: 30,
  isGameActive: false,
  isRoundActive: false,
  correctClicks: 0,
  wrongClicks: 0,
  missedClicks: 0,
  smokescreenProgress: 0,
};

// 音效和視覺效果設定
const EFFECTS = {
  correctClick: {
    sound: "correct",
    visual: "hit-effect",
    color: "#4ade80",
  },
  wrongClick: {
    sound: "wrong",
    visual: "miss-effect",
    color: "#ef4444",
  },
  roundComplete: {
    sound: "round-complete",
    visual: "round-complete-effect",
  },
  gameComplete: {
    sound: "game-complete",
    visual: "game-complete-effect",
  },
};

// 導出資料供其他腳本使用
if (typeof module !== "undefined" && module.exports) {
  module.exports = { GAME_DATA, GAME_STATE, EFFECTS };
}
