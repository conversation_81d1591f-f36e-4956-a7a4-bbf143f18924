// 遊戲核心引擎
class GameEngine {
  constructor() {
    this.gameData = GAME_DATA;
    this.gameState = { ...GAME_STATE };
    this.timers = {
      gameTimer: null,
      roundTimer: null,
    };
    this.currentRoundData = null;
    this.flawElements = [];
    this.smokescreenClickCount = 0;
    this.smokescreenRequired = 10; // 需要點擊10次才能撥開煙霧
    this.comboCount = 0; // 連擊計數器
    this.lastClickTime = 0; // 上次點擊時間

    this.initializeGame();
  }

  initializeGame() {
    // 重置遊戲狀態
    this.gameState = {
      currentRound: 1,
      citizenHealth: this.gameData.gameSettings.citizenMaxHealth,
      legislatorHealth: this.gameData.gameSettings.legislatorMaxHealth,
      score: 0,
      timeRemaining: this.gameData.gameSettings.totalTime,
      roundTimeRemaining: this.gameData.gameSettings.roundTimeLimit,
      isGameActive: false,
      isRoundActive: false,
      correctClicks: 0,
      wrongClicks: 0,
      missedClicks: 0,
      smokescreenProgress: 0,
    };

    console.log("遊戲引擎初始化完成");
  }

  startGame() {
    this.gameState.isGameActive = true;
    this.gameState.timeRemaining = this.gameData.gameSettings.totalTime;

    // 開始總計時器
    this.startGameTimer();

    // 開始第一回合
    this.startRound(1);

    console.log("遊戲開始");
  }

  startGameTimer() {
    this.timers.gameTimer = setInterval(() => {
      this.gameState.timeRemaining--;

      // 更新UI顯示
      this.updateTimerDisplay();

      // 檢查遊戲是否結束
      if (this.gameState.timeRemaining <= 0) {
        this.endGame("timeout");
      }
    }, 1000);
  }

  startRound(roundNumber) {
    if (roundNumber > this.gameData.rounds.length) {
      this.endGame("complete");
      return;
    }

    this.gameState.currentRound = roundNumber;
    this.gameState.isRoundActive = true;
    this.gameState.roundTimeRemaining =
      this.gameData.gameSettings.roundTimeLimit;
    this.currentRoundData = this.gameData.rounds[roundNumber - 1];

    // 載入回合內容
    this.loadRoundContent();

    // 開始回合計時器
    this.startRoundTimer();

    console.log(`回合 ${roundNumber} 開始`);
  }

  startRoundTimer() {
    this.timers.roundTimer = setInterval(() => {
      this.gameState.roundTimeRemaining--;

      // 檢查回合是否超時
      if (this.gameState.roundTimeRemaining <= 0) {
        this.endRound("timeout");
      }
    }, 1000);
  }

  loadRoundContent() {
    const round = this.currentRoundData;

    // 更新回合資訊
    document.getElementById("current-round").textContent =
      `回合 ${this.gameState.currentRound}`;

    // 先顯示罷免理由模態框
    this.showReasonModal(round);
  }

  showReasonModal(round) {
    // 更新模態框內容
    document.getElementById("modal-round-number").textContent =
      `第 ${this.gameState.currentRound} 回合`;
    document.getElementById("modal-reason-title").textContent =
      round.reasonTitle;
    document.getElementById("modal-reason-content").textContent =
      round.reasonContent;

    // 顯示模態框
    const modal = document.getElementById("reason-modal");
    modal.classList.remove("hidden");

    // 綁定開始戰鬥按鈕
    const startBattleBtn = document.getElementById("start-battle-btn");
    startBattleBtn.onclick = () => {
      this.startBattlePhase();
    };
  }

  startBattlePhase() {
    // 隱藏理由模態框
    document.getElementById("reason-modal").classList.add("hidden");

    // 載入戰鬥內容
    const round = this.currentRoundData;

    // 更新戰鬥卡片內容 - 使用正確的元素 ID
    const reasonTitle = document.getElementById("modal-reason-title");
    const reasonContent = document.getElementById("modal-reason-content");

    if (reasonTitle) reasonTitle.textContent = round.reasonTitle;
    if (reasonContent) reasonContent.textContent = round.reasonContent;

    // 處理不同類型的回合
    if (round.roundType === "smokescreen") {
      this.loadSmokescreenRound();
    } else {
      this.loadStandardRound();
    }

    // 重置連擊計數器
    this.comboCount = 0;
    this.updateComboDisplay();
  }

  loadStandardRound() {
    const round = this.currentRoundData;
    const rebuttalContent = document.getElementById("rebuttal-text");

    // 清空之前的內容
    rebuttalContent.innerHTML = "";
    this.flawElements = [];

    // 解析答辯文字，標記話術破綻
    let processedText = round.rebuttalText;

    // 為每個破綻關鍵字創建可點擊元素
    round.flaws.forEach((flaw, index) => {
      const flawId = `flaw-${index}`;
      const regex = new RegExp(`(${this.escapeRegex(flaw.text)})`, "g");
      processedText = processedText.replace(
        regex,
        `<span class="flaw-word" data-flaw-id="${flawId}" data-is-correct="${flaw.isCorrect}">${flaw.text}</span>`
      );
    });

    rebuttalContent.innerHTML = processedText;

    // 綁定點擊事件
    this.bindFlawClickEvents();

    // 開始高亮提示
    setTimeout(() => this.startFlawHighlighting(), 1000);

    // 更新話術防禦計
    this.updateBullshitMeter();
  }

  // 更新話術防禦計
  updateBullshitMeter() {
    const meterBar = document.getElementById("bullshit-bar");
    const meterText = document.getElementById("bullshit-text");
    const meterStatus = document.getElementById("meter-status");

    if (meterBar && meterText && meterStatus) {
      const percentage = Math.max(0, 100 - this.gameState.correctClicks * 20);
      meterBar.style.width = `${percentage}%`;
      meterText.textContent = `${percentage}%`;

      if (percentage > 80) {
        meterStatus.innerHTML =
          '<span class="status-text">話術滿載中... 💩</span>';
      } else if (percentage > 50) {
        meterStatus.innerHTML =
          '<span class="status-text">話術減弱中... 🤔</span>';
      } else if (percentage > 20) {
        meterStatus.innerHTML =
          '<span class="status-text">防禦崩潰中... 😰</span>';
      } else {
        meterStatus.innerHTML =
          '<span class="status-text">話術破功！ 🎉</span>';
      }
    }
  }

  loadSmokescreenRound() {
    const round = this.currentRoundData;

    // 隱藏標準答辯區域
    document.querySelector(".rebuttal-area").style.display = "none";

    // 顯示煙霧彈覆蓋層
    const smokescreenOverlay = document.getElementById("smokescreen-overlay");
    smokescreenOverlay.classList.remove("hidden");

    // 載入煙霧彈內容
    const smokescreenImages = document.querySelector(".smokescreen-images");
    smokescreenImages.innerHTML = "";

    round.smokescreenItems.forEach((item) => {
      const smokeItem = document.createElement("div");
      smokeItem.className = "smoke-item";
      smokeItem.textContent = item;
      smokescreenImages.appendChild(smokeItem);
    });

    // 設置核心問題
    document.getElementById("core-question").textContent =
      round.smokescreenCoreQuestion;

    // 重置煙霧彈進度
    this.smokescreenClickCount = 0;
    this.updateSmokescreenProgress();

    // 綁定撥開煙霧按鈕事件
    this.bindSmokescreenEvents();
  }

  bindFlawClickEvents() {
    const flawWords = document.querySelectorAll(".flaw-word");

    flawWords.forEach((element) => {
      element.addEventListener("click", (e) => {
        this.handleFlawClick(e.target);
      });
    });
  }

  bindSmokescreenEvents() {
    const clearSmokeBtn = document.getElementById("clear-smoke-btn");

    clearSmokeBtn.addEventListener("click", () => {
      this.handleSmokescreenClick();
    });
  }

  handleFlawClick(element) {
    if (
      !this.gameState.isRoundActive ||
      element.classList.contains("clicked")
    ) {
      return;
    }

    const isCorrect = element.dataset.isCorrect === "true";

    // 標記為已點擊
    element.classList.add("clicked");

    if (isCorrect) {
      // 正確點擊
      element.classList.add("correct");
      this.gameState.correctClicks++;

      // 連擊系統
      const currentTime = Date.now();
      if (currentTime - this.lastClickTime < 2000) {
        // 2秒內算連擊
        this.comboCount++;
      } else {
        this.comboCount = 1;
      }
      this.lastClickTime = currentTime;

      // 連擊獎勵
      const comboDamage =
        this.gameData.gameSettings.damagePerCorrectClick +
        (this.comboCount - 1) * 5;
      const comboScore = 10 + (this.comboCount - 1) * 2;

      this.dealDamage("legislator", comboDamage);
      this.showClickEffect(element, "correct", `+${comboScore}`);
      this.updateScore(comboScore);
      this.updateComboDisplay();
      this.updateBullshitMeter();

      // 顯示事實查核彈出框
      setTimeout(() => this.showFactCheckPopup(), 500);
    } else {
      // 錯誤點擊
      element.classList.add("wrong");
      this.gameState.wrongClicks++;
      this.dealDamage(
        "citizen",
        this.gameData.gameSettings.damagePerWrongClick
      );
      this.showClickEffect(element, "wrong", "-15");

      // 重置連擊
      this.comboCount = 0;
      this.updateComboDisplay();
    }

    // 檢查是否所有破綻都被找到
    this.checkRoundCompletion();
  }

  handleSmokescreenClick() {
    if (!this.gameState.isRoundActive) return;

    this.smokescreenClickCount++;
    this.updateSmokescreenProgress();

    // 檢查是否撥開了煙霧
    if (this.smokescreenClickCount >= this.smokescreenRequired) {
      this.revealCoreQuestion();
    }
  }

  updateSmokescreenProgress() {
    const progress =
      (this.smokescreenClickCount / this.smokescreenRequired) * 100;
    document.getElementById("smoke-progress-bar").style.width = `${progress}%`;

    // 隨著進度增加，煙霧項目逐漸消失
    const smokeItems = document.querySelectorAll(".smoke-item");
    const itemsToHide = Math.floor(
      (this.smokescreenClickCount / this.smokescreenRequired) *
        smokeItems.length
    );

    smokeItems.forEach((item, index) => {
      if (index < itemsToHide) {
        item.style.opacity = "0.2";
        item.style.transform = "scale(0.8)";
      }
    });
  }

  revealCoreQuestion() {
    // 顯示核心問題
    const coreQuestion = document.getElementById("core-question");
    coreQuestion.classList.remove("hidden");
    coreQuestion.classList.add("visible");

    // 隱藏撥開煙霧按鈕
    document.getElementById("clear-smoke-btn").style.display = "none";

    // 給予分數獎勵
    this.updateScore(30);
    this.dealDamage("legislator", 30);

    // 延遲結束回合
    setTimeout(() => {
      this.endRound("complete");
    }, 3000);
  }

  dealDamage(target, amount) {
    if (target === "citizen") {
      this.gameState.citizenHealth = Math.max(
        0,
        this.gameState.citizenHealth - amount
      );
    } else if (target === "legislator") {
      this.gameState.legislatorHealth = Math.max(
        0,
        this.gameState.legislatorHealth - amount
      );
    }

    // 更新血條顯示
    this.updateHealthBars();

    // 檢查遊戲結束條件
    if (this.gameState.citizenHealth <= 0) {
      this.endGame("citizen_defeated");
    }
  }

  updateScore(points) {
    this.gameState.score += points;
  }

  showClickEffect(element, type, customText = null) {
    const rect = element.getBoundingClientRect();
    const effect = document.createElement("div");

    effect.className = type === "correct" ? "hit-effect" : "miss-effect";
    effect.textContent = customText || (type === "correct" ? "+10" : "-15");
    effect.style.left = `${rect.left + rect.width / 2}px`;
    effect.style.top = `${rect.top}px`;
    effect.style.position = "fixed";
    effect.style.zIndex = "1000";

    document.body.appendChild(effect);

    // 移除效果元素
    setTimeout(() => {
      if (document.body.contains(effect)) {
        document.body.removeChild(effect);
      }
    }, 1000);
  }

  checkRoundCompletion() {
    const totalFlaws = this.currentRoundData.flaws.length;
    const clickedFlaws = document.querySelectorAll(".flaw-word.clicked").length;

    if (clickedFlaws >= totalFlaws) {
      this.endRound("complete");
    }
  }

  endRound(reason) {
    this.gameState.isRoundActive = false;

    // 清除回合計時器
    if (this.timers.roundTimer) {
      clearInterval(this.timers.roundTimer);
      this.timers.roundTimer = null;
    }

    // 顯示 Fact Check
    this.showFactCheck();

    console.log(`回合 ${this.gameState.currentRound} 結束: ${reason}`);
  }

  showFactCheck() {
    const modal = document.getElementById("fact-check-modal");
    const content = document.getElementById("fact-check-content");
    const evidenceLink = document.getElementById("evidence-link");

    content.textContent = this.currentRoundData.factCheck.content;
    evidenceLink.href = this.currentRoundData.factCheck.evidenceLink;

    modal.classList.remove("hidden");
  }

  nextRound() {
    // 隱藏 Fact Check
    document.getElementById("fact-check-modal").classList.add("hidden");

    // 重置煙霧彈相關元素
    if (this.currentRoundData.roundType === "smokescreen") {
      document.getElementById("smokescreen-overlay").classList.add("hidden");
      document.querySelector(".rebuttal-area").style.display = "block";
    }

    // 開始下一回合
    this.startRound(this.gameState.currentRound + 1);
  }

  endGame(reason) {
    this.gameState.isGameActive = false;
    this.gameState.isRoundActive = false;

    // 清除所有計時器
    if (this.timers.gameTimer) {
      clearInterval(this.timers.gameTimer);
      this.timers.gameTimer = null;
    }
    if (this.timers.roundTimer) {
      clearInterval(this.timers.roundTimer);
      this.timers.roundTimer = null;
    }

    // 計算最終分數
    this.calculateFinalScore();

    // 顯示結算畫面
    this.showResultScreen();

    console.log(`遊戲結束: ${reason}, 最終分數: ${this.gameState.score}`);
  }

  calculateFinalScore() {
    // 基礎分數已在遊戲過程中累積
    let finalScore = this.gameState.score;

    // 時間獎勵
    const timeBonus = Math.floor(this.gameState.timeRemaining / 10);
    finalScore += timeBonus;

    // 血量獎勵
    const healthBonus = Math.floor(this.gameState.citizenHealth / 5);
    finalScore += healthBonus;

    this.gameState.score = finalScore;
  }

  showResultScreen() {
    // 切換到結算畫面
    document.getElementById("game-screen").classList.remove("active");
    document.getElementById("result-screen").classList.add("active");

    // 根據分數確定評級
    const grade = this.getGradeByScore(this.gameState.score);

    // 更新結算內容
    document.getElementById("result-title").textContent = grade.title;
    document.getElementById("result-description").textContent =
      grade.description;
    document.getElementById("score-value").textContent = this.gameState.score;
    document.getElementById("result-expression").textContent = grade.expression;
  }

  getGradeByScore(score) {
    const grades = this.gameData.resultGrades;

    for (let grade of grades) {
      if (score >= grade.minScore) {
        return grade;
      }
    }

    return grades[grades.length - 1]; // 返回最低等級
  }

  // 工具方法
  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  }

  updateTimerDisplay() {
    const minutes = Math.floor(this.gameState.timeRemaining / 60);
    const seconds = this.gameState.timeRemaining % 60;
    const display = `${minutes}:${seconds.toString().padStart(2, "0")}`;
    document.getElementById("timer-display").textContent = display;
  }

  updateHealthBars() {
    // 更新公民怒氣值
    const citizenPercent =
      (this.gameState.citizenHealth /
        this.gameData.gameSettings.citizenMaxHealth) *
      100;
    document.getElementById("citizen-health-bar").style.width =
      `${citizenPercent}%`;
    document.getElementById("citizen-health-text").textContent =
      this.gameState.citizenHealth;

    // 更新委員誠信值
    const legislatorPercent =
      (this.gameState.legislatorHealth /
        this.gameData.gameSettings.legislatorMaxHealth) *
      100;
    document.getElementById("legislator-health-bar").style.width =
      `${legislatorPercent}%`;
    document.getElementById("legislator-health-text").textContent =
      this.gameState.legislatorHealth;

    // 更新戰鬥卡片的能量條
    const citizenPower = document.querySelector(".citizen-power");
    const legislatorPower = document.querySelector(".legislator-power");

    if (citizenPower) {
      citizenPower.style.width = `${citizenPercent}%`;
    }
    if (legislatorPower) {
      legislatorPower.style.width = `${legislatorPercent}%`;
    }
  }

  updateComboDisplay() {
    const comboDisplay = document.getElementById("combo-display");
    const comboCount = document.getElementById("combo-count");

    if (this.comboCount > 1) {
      comboCount.textContent = this.comboCount;
      comboDisplay.classList.remove("hidden");

      // 添加動畫效果
      comboDisplay.style.animation = "none";
      setTimeout(() => {
        comboDisplay.style.animation = "comboAppear 0.5s ease-out";
      }, 10);
    } else {
      comboDisplay.classList.add("hidden");
    }
  }

  startFlawHighlighting() {
    // 為話術破綻添加閃爍提示效果
    const flawWords = document.querySelectorAll(".flaw-word:not(.clicked)");

    flawWords.forEach((element, index) => {
      setTimeout(() => {
        if (!element.classList.contains("clicked")) {
          element.style.animation = "highlight-pulse 2s infinite";
        }
      }, index * 500); // 錯開顯示時間
    });
  }

  restartGame() {
    // 重置遊戲狀態
    this.initializeGame();

    // 切換回開場畫面
    document.getElementById("result-screen").classList.remove("active");
    document.getElementById("intro-screen").classList.add("active");
  }

  // 顯示事實查核彈出框
  showFactCheckPopup() {
    const popup = document.getElementById("fact-check-popup");
    const factCheck = this.currentRoundData.factCheck;

    if (popup && factCheck) {
      // 設置媒體內容
      const mediaContainer = document.getElementById("fact-check-media");
      if (factCheck.mediaType === "video") {
        mediaContainer.innerHTML = `
          <video controls style="max-width: 100%; border-radius: 8px;">
            <source src="${factCheck.mediaUrl}" type="video/mp4">
            您的瀏覽器不支援影片播放。
          </video>
          <p style="margin-top: 0.5rem; font-size: 0.9rem; color: #6b7280;">${factCheck.mediaCaption}</p>
        `;
      } else if (factCheck.mediaType === "image") {
        mediaContainer.innerHTML = `
          <img src="${factCheck.mediaUrl}" alt="${factCheck.mediaCaption}" style="max-width: 100%; border-radius: 8px;">
          <p style="margin-top: 0.5rem; font-size: 0.9rem; color: #6b7280;">${factCheck.mediaCaption}</p>
        `;
      }

      // 設置事實查核文字
      document.getElementById("fact-check-text").textContent =
        factCheck.content;

      // 設置來源連結
      const sourceLink = document.getElementById("source-link");
      sourceLink.href = factCheck.evidenceLink;

      // 設置打臉按鈕文字
      const slapButton = document.getElementById("slap-button");
      slapButton.innerHTML = `
        <span class="slap-emoji">👋</span>
        <span class="slap-text">${factCheck.slapMessage}</span>
      `;

      // 顯示彈出框
      popup.classList.remove("hidden");

      // 綁定關閉事件
      this.bindFactCheckEvents();
    }
  }

  // 綁定事實查核彈出框事件
  bindFactCheckEvents() {
    const popup = document.getElementById("fact-check-popup");
    const closeBtn = document.getElementById("popup-close");
    const slapBtn = document.getElementById("slap-button");

    const closePopup = () => {
      popup.classList.add("hidden");
    };

    closeBtn.onclick = closePopup;
    slapBtn.onclick = () => {
      // 顯示幽默文字
      const factCheck = this.currentRoundData.factCheck;
      if (factCheck.humorText) {
        this.showHumorMessage(factCheck.humorText);
      }
      closePopup();
    };

    // 點擊背景關閉
    popup.onclick = (e) => {
      if (e.target === popup) {
        closePopup();
      }
    };
  }

  // 顯示幽默訊息
  showHumorMessage(message) {
    const humorDiv = document.createElement("div");
    humorDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 12px;
      box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
      z-index: 4000;
      font-weight: 600;
      max-width: 300px;
      animation: slideInRight 0.5s ease-out;
    `;
    humorDiv.textContent = message;

    document.body.appendChild(humorDiv);

    setTimeout(() => {
      humorDiv.style.animation = "slideOutRight 0.5s ease-in forwards";
      setTimeout(() => {
        document.body.removeChild(humorDiv);
      }, 500);
    }, 3000);
  }
}
