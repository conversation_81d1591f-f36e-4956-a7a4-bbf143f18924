<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>罷免元兇？- 戰鬥設計演示</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .demo-title {
            color: #1f2937;
            border-bottom: 3px solid #4ade80;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 8px;
            padding: 1.5rem;
            border-left: 4px solid #4ade80;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .demo-button {
            display: inline-block;
            background: #4ade80;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            background: #22c55e;
            transform: translateY(-2px);
        }
        
        .demo-button.secondary {
            background: #6366f1;
        }
        
        .demo-button.secondary:hover {
            background: #4f46e5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎮 罷免元兇？戰鬥設計演示</h1>
            <p>全新的戰鬥感遊戲體驗，讓政治攻防更加生動有趣！</p>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">🎯 新增功能特色</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">📋</div>
                    <h3>全螢幕罷免理由展示</h3>
                    <p>每回合開始前，以模態框形式完整展示罷免理由，讓玩家充分理解爭議核心。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚔️</div>
                    <h3>戰鬥場景設計</h3>
                    <p>左右對戰卡片設計，中央VS指示區，營造真實的攻防戰鬥氛圍。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h3>強化點擊提示</h3>
                    <p>中央醒目的「點擊話術破綻！」指示，配合動畫效果，讓玩法更清晰。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔥</div>
                    <h3>連擊系統</h3>
                    <p>連續正確點擊可獲得連擊獎勵，增加分數和傷害，提升遊戲刺激感。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>戰鬥特效</h3>
                    <p>閃電效果、能量粒子、光暈動畫，營造科幻戰鬥氛圍。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>即時能量條</h3>
                    <p>戰鬥卡片底部顯示能量條，即時反映雙方戰鬥狀態。</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">🎮 遊戲流程改進</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div style="text-align: center; padding: 1rem; background: #fef3c7; border-radius: 8px;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">1️⃣</div>
                    <h4>罷免理由展示</h4>
                    <p>全螢幕模態框顯示完整理由</p>
                </div>
                <div style="text-align: center; padding: 1rem; background: #dbeafe; border-radius: 8px;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">2️⃣</div>
                    <h4>開始攻防戰</h4>
                    <p>點擊按鈕進入戰鬥場景</p>
                </div>
                <div style="text-align: center; padding: 1rem; background: #dcfce7; border-radius: 8px;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">3️⃣</div>
                    <h4>戰鬥對決</h4>
                    <p>在戰鬥場景中點擊話術破綻</p>
                </div>
                <div style="text-align: center; padding: 1rem; background: #fce7f3; border-radius: 8px;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">4️⃣</div>
                    <h4>事實查核</h4>
                    <p>回合結束後顯示查核資訊</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">🎨 視覺設計亮點</h2>
            <ul style="font-size: 1.1rem; line-height: 1.8;">
                <li><strong>戰鬥場景背景：</strong>深色漸變背景配合閃電和粒子特效</li>
                <li><strong>角色頭像：</strong>帶光暈效果的圓形頭像，區分正義方和答辯方</li>
                <li><strong>VS指示器：</strong>中央大型VS文字配合旋轉光環效果</li>
                <li><strong>指示文字：</strong>醒目的藍色指示框，強調「點擊話術破綻」</li>
                <li><strong>連擊顯示：</strong>橙色連擊計數器，激勵玩家連續正確點擊</li>
                <li><strong>能量條：</strong>底部彩色能量條，即時顯示戰鬥狀態</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">🚀 立即體驗</h2>
            <div style="text-align: center;">
                <a href="index.html" class="demo-button">🎮 開始完整遊戲</a>
                <a href="test.html" class="demo-button secondary">🧪 功能測試頁面</a>
            </div>
            
            <div style="margin-top: 2rem; padding: 1.5rem; background: #f0f9ff; border-radius: 8px; border-left: 4px solid #0ea5e9;">
                <h4 style="color: #0c4a6e; margin-bottom: 1rem;">💡 設計理念</h4>
                <p style="color: #0c4a6e; margin: 0;">
                    透過遊戲化的戰鬥場景設計，將原本枯燥的政治攻防文字轉化為視覺化、互動性強的遊戲體驗。
                    每個設計元素都旨在增強玩家的參與感和理解度，讓複雜的政治議題變得更加親近和有趣。
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">📱 響應式設計</h2>
            <p>新的戰鬥設計完全支援響應式佈局：</p>
            <ul>
                <li><strong>桌面版：</strong>左右對戰卡片 + 中央指示區的三欄佈局</li>
                <li><strong>平板版：</strong>自動調整卡片大小和間距</li>
                <li><strong>手機版：</strong>垂直堆疊佈局，指示區置頂</li>
            </ul>
            
            <div style="margin-top: 1rem; padding: 1rem; background: #fffbeb; border-radius: 6px;">
                <strong>建議測試：</strong>使用瀏覽器開發者工具切換不同裝置尺寸，體驗響應式效果。
            </div>
        </div>
    </div>

    <script>
        // 簡單的演示互動
        document.addEventListener('DOMContentLoaded', function() {
            // 為特色卡片添加懸停效果
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });
            
            console.log('🎮 罷免元兇戰鬥設計演示頁面載入完成！');
        });
    </script>
</body>
</html>
