// UI控制器 - 處理用戶介面互動
class UIController {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.initializeEventListeners();
        this.setupModalHandlers();
        this.setupShareHandlers();
    }
    
    initializeEventListeners() {
        // 開始遊戲按鈕
        const startGameBtn = document.getElementById('start-game-btn');
        if (startGameBtn) {
            startGameBtn.addEventListener('click', () => {
                this.startGame();
            });
        }
        
        // 下一回合按鈕
        const nextRoundBtn = document.getElementById('next-round-btn');
        if (nextRoundBtn) {
            nextRoundBtn.addEventListener('click', () => {
                this.gameEngine.nextRound();
            });
        }
        
        // 重新開始按鈕
        const restartBtn = document.getElementById('restart-btn');
        if (restartBtn) {
            restartBtn.addEventListener('click', () => {
                this.restartGame();
            });
        }
        
        // 分享按鈕
        const shareBtn = document.getElementById('share-btn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.shareGame();
            });
        }
        
        // 外部連結
        this.setupExternalLinks();
        
        console.log('UI事件監聽器初始化完成');
    }
    
    setupModalHandlers() {
        // Fact Check 模態框關閉按鈕
        const closeFactCheckBtn = document.getElementById('close-fact-check');
        if (closeFactCheckBtn) {
            closeFactCheckBtn.addEventListener('click', () => {
                this.closeFactCheck();
            });
        }
        
        // 點擊模態框背景關閉
        const factCheckModal = document.getElementById('fact-check-modal');
        if (factCheckModal) {
            factCheckModal.addEventListener('click', (e) => {
                if (e.target === factCheckModal) {
                    this.closeFactCheck();
                }
            });
        }
        
        // ESC鍵關閉模態框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeFactCheck();
            }
        });
    }
    
    setupShareHandlers() {
        // 檢查是否支援 Web Share API
        this.webShareSupported = navigator.share !== undefined;
        
        // 如果不支援，準備備用分享方法
        if (!this.webShareSupported) {
            this.setupFallbackShare();
        }
    }
    
    setupExternalLinks() {
        // 連署教學連結
        const petitionLink = document.getElementById('petition-link');
        if (petitionLink) {
            petitionLink.href = GAME_DATA.links.petitionSite;
            petitionLink.addEventListener('click', () => {
                this.trackLinkClick('petition');
            });
        }
        
        // 完整理由書連結
        const fullDocumentLink = document.getElementById('full-document-link');
        if (fullDocumentLink) {
            fullDocumentLink.href = GAME_DATA.links.fullDocument;
            fullDocumentLink.addEventListener('click', () => {
                this.trackLinkClick('full_document');
            });
        }
        
        // 證據連結（動態設置）
        const evidenceLink = document.getElementById('evidence-link');
        if (evidenceLink) {
            evidenceLink.addEventListener('click', () => {
                this.trackLinkClick('evidence');
            });
        }
    }
    
    startGame() {
        // 切換到遊戲畫面
        this.switchScreen('intro-screen', 'game-screen');
        
        // 啟動遊戲引擎
        this.gameEngine.startGame();
        
        // 播放開始音效（如果有的話）
        this.playSound('game_start');
        
        console.log('遊戲開始 - UI切換完成');
    }
    
    restartGame() {
        // 重置遊戲引擎
        this.gameEngine.restartGame();
        
        // 重置UI狀態
        this.resetUIState();
        
        console.log('遊戲重新開始');
    }
    
    switchScreen(fromScreen, toScreen) {
        const from = document.getElementById(fromScreen);
        const to = document.getElementById(toScreen);
        
        if (from) {
            from.classList.remove('active');
        }
        
        if (to) {
            to.classList.add('active');
            to.classList.add('fade-in');
            
            // 移除動畫類別
            setTimeout(() => {
                to.classList.remove('fade-in');
            }, 500);
        }
    }
    
    closeFactCheck() {
        const modal = document.getElementById('fact-check-modal');
        if (modal && !modal.classList.contains('hidden')) {
            modal.classList.add('hidden');
        }
    }
    
    shareGame() {
        const shareData = this.generateShareData();
        
        if (this.webShareSupported) {
            this.webShare(shareData);
        } else {
            this.fallbackShare(shareData);
        }
    }
    
    generateShareData() {
        const score = this.gameEngine.gameState.score;
        const grade = this.gameEngine.getGradeByScore(score);
        
        // 根據分數選擇分享文案
        let shareText;
        if (score >= 80) {
            shareText = GAME_DATA.shareMessages[0];
        } else if (score >= 60) {
            shareText = GAME_DATA.shareMessages[1];
        } else {
            shareText = GAME_DATA.shareMessages[2];
        }
        
        // 添加個人化分數資訊
        shareText += `\n我的得分：${score}分 - ${grade.title}`;
        
        return {
            title: '罷免元兇？- 立委攻防戰',
            text: shareText,
            url: window.location.href
        };
    }
    
    async webShare(shareData) {
        try {
            await navigator.share(shareData);
            this.trackShareAction('web_share_success');
        } catch (error) {
            console.log('Web Share取消或失敗:', error);
            // 如果Web Share失敗，使用備用方法
            this.fallbackShare(shareData);
        }
    }
    
    fallbackShare(shareData) {
        // 複製到剪貼板
        const shareText = `${shareData.title}\n\n${shareData.text}\n\n${shareData.url}`;
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(shareText).then(() => {
                this.showShareSuccess('已複製分享內容到剪貼板！');
                this.trackShareAction('clipboard_success');
            }).catch(() => {
                this.showShareFallback(shareText);
            });
        } else {
            this.showShareFallback(shareText);
        }
    }
    
    setupFallbackShare() {
        // 為不支援現代API的瀏覽器準備備用分享方法
        this.fallbackShareMethods = {
            facebook: (text, url) => {
                const fbUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
                window.open(fbUrl, '_blank', 'width=600,height=400');
            },
            
            twitter: (text, url) => {
                const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
                window.open(twitterUrl, '_blank', 'width=600,height=400');
            },
            
            line: (text, url) => {
                const lineUrl = `https://social-plugins.line.me/lineit/share?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
                window.open(lineUrl, '_blank', 'width=600,height=400');
            }
        };
    }
    
    showShareSuccess(message) {
        // 顯示分享成功提示
        this.showToast(message, 'success');
    }
    
    showShareFallback(shareText) {
        // 顯示分享文字供用戶手動複製
        const modal = this.createShareModal(shareText);
        document.body.appendChild(modal);
        
        // 自動選中文字
        const textArea = modal.querySelector('textarea');
        textArea.select();
        textArea.setSelectionRange(0, 99999); // 移動設備
        
        this.trackShareAction('manual_copy_shown');
    }
    
    createShareModal(shareText) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <span class="modal-icon">📤</span>
                    <span class="modal-title">分享遊戲</span>
                    <button class="close-btn" onclick="this.closest('.modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <p>請複製以下內容分享給朋友：</p>
                    <textarea readonly style="width: 100%; height: 120px; margin-top: 1rem; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">${shareText}</textarea>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="this.closest('.modal').remove()">關閉</button>
                </div>
            </div>
        `;
        
        return modal;
    }
    
    showToast(message, type = 'info') {
        // 創建Toast通知
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#22c55e' : '#3b82f6'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // 自動移除
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    
    resetUIState() {
        // 重置所有UI元素到初始狀態
        
        // 清除所有話術破綻的點擊狀態
        const flawWords = document.querySelectorAll('.flaw-word');
        flawWords.forEach(element => {
            element.classList.remove('clicked', 'correct', 'wrong');
            element.style.animation = '';
        });
        
        // 重置血條
        document.getElementById('citizen-health-bar').style.width = '100%';
        document.getElementById('citizen-health-text').textContent = '100';
        document.getElementById('legislator-health-bar').style.width = '100%';
        document.getElementById('legislator-health-text').textContent = '100';
        
        // 重置計時器顯示
        document.getElementById('timer-display').textContent = '3:00';
        
        // 隱藏煙霧彈覆蓋層
        document.getElementById('smokescreen-overlay').classList.add('hidden');
        
        // 重置煙霧彈進度
        document.getElementById('smoke-progress-bar').style.width = '0%';
        document.getElementById('core-question').classList.add('hidden');
        document.getElementById('core-question').classList.remove('visible');
        document.getElementById('clear-smoke-btn').style.display = 'block';
        
        // 顯示答辯區域
        document.querySelector('.rebuttal-area').style.display = 'block';
        
        // 關閉所有模態框
        this.closeFactCheck();
    }
    
    playSound(soundType) {
        // 音效播放功能（可選實作）
        // 這裡可以添加音效播放邏輯
        console.log(`播放音效: ${soundType}`);
    }
    
    trackLinkClick(linkType) {
        // 追蹤外部連結點擊（用於分析）
        console.log(`外部連結點擊: ${linkType}`);
        
        // 這裡可以添加 Google Analytics 或其他分析工具的追蹤代碼
        if (typeof gtag !== 'undefined') {
            gtag('event', 'click', {
                event_category: 'external_link',
                event_label: linkType
            });
        }
    }
    
    trackShareAction(actionType) {
        // 追蹤分享行為
        console.log(`分享行為: ${actionType}`);
        
        if (typeof gtag !== 'undefined') {
            gtag('event', 'share', {
                event_category: 'game_share',
                event_label: actionType
            });
        }
    }
    
    // 響應式設計輔助方法
    updateLayoutForMobile() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // 移動設備特殊處理
            document.body.classList.add('mobile-layout');
        } else {
            document.body.classList.remove('mobile-layout');
        }
    }
    
    // 初始化響應式監聽
    initializeResponsiveHandlers() {
        window.addEventListener('resize', () => {
            this.updateLayoutForMobile();
        });
        
        // 初始檢查
        this.updateLayoutForMobile();
    }
}

// 添加必要的CSS動畫
const additionalStyles = `
@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.toast {
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 500;
}

.mobile-layout .round-content {
    grid-template-columns: 1fr !important;
}

.mobile-layout .health-bars {
    grid-template-columns: 1fr !important;
}

.mobile-layout .cta-buttons .btn {
    min-width: 100% !important;
}
`;

// 動態添加樣式
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
