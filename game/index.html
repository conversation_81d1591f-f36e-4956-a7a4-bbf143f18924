<!doctype html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>罷免元兇？- 立委攻防戰遊戲</title>
    <meta
      name="description"
      content="透過有趣的遊戲體驗，了解葉元之立委的罷免理由與答辯內容"
    />
    <link rel="stylesheet" href="styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;500;700;900&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- 遊戲主容器 -->
    <div id="game-container">
      <!-- 開場畫面 -->
      <div id="intro-screen" class="screen active">
        <div class="intro-hero">
          <!-- 背景動畫效果 -->
          <div class="hero-background">
            <div class="floating-emoji">💩</div>
            <div class="floating-emoji">🎯</div>
            <div class="floating-emoji">👋</div>
            <div class="floating-emoji">🔍</div>
            <div class="floating-emoji">⚖️</div>
          </div>

          <!-- 主角登場 -->
          <div class="character-showcase">
            <div class="character-card">
              <div class="character-avatar-big">
                <div class="avatar-ring"></div>
                <div class="avatar-face">🤵</div>
                <div class="avatar-speech-bubble">
                  <span class="speech-text">我沒看內容就舉手...</span>
                  <div class="bubble-tail"></div>
                </div>
              </div>
              <div class="character-info">
                <h2 class="character-name">葉元之委員</h2>
                <div class="character-title">話術大師</div>
              </div>
            </div>
          </div>

          <!-- 遊戲標題 -->
          <div class="game-title-section">
            <h1 class="game-title-main">
              <span class="title-emoji">💥</span>
              <span class="title-text">打臉話術王</span>
              <span class="title-emoji">💥</span>
            </h1>
            <div class="game-subtitle">
              <span class="subtitle-text">揭穿政治話術，還原事實真相！</span>
            </div>
          </div>

          <!-- 遊戲特色 -->
          <div class="game-features">
            <div class="feature-card">
              <div class="feature-icon">🎯</div>
              <div class="feature-text">點擊破綻</div>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🔍</div>
              <div class="feature-text">事實查核</div>
            </div>
            <div class="feature-card">
              <div class="feature-icon">👋</div>
              <div class="feature-text">幽默打臉</div>
            </div>
          </div>

          <!-- 開始按鈕 -->
          <div class="start-button-section">
            <button id="start-game-btn" class="start-button">
              <span class="button-icon">🚀</span>
              <span class="button-text">開始打臉之旅</span>
              <div class="button-glow"></div>
            </button>
            <div class="start-hint">
              <span class="hint-text">準備好揭穿話術了嗎？</span>
            </div>
          </div>

          <!-- 遊戲說明 -->
          <div class="game-instructions">
            <div class="instruction-item">
              <span class="instruction-number">1</span>
              <span class="instruction-text">閱讀委員答辯</span>
            </div>
            <div class="instruction-item">
              <span class="instruction-number">2</span>
              <span class="instruction-text">找出話術破綻</span>
            </div>
            <div class="instruction-item">
              <span class="instruction-number">3</span>
              <span class="instruction-text">查看事實證據</span>
            </div>
            <div class="instruction-item">
              <span class="instruction-number">4</span>
              <span class="instruction-text">完成幽默打臉</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 遊戲主畫面 -->
      <div id="game-screen" class="screen">
        <!-- 遊戲頂部資訊 -->
        <div class="game-header">
          <div class="round-info">
            <span id="current-round">回合 1</span>
            <span class="round-separator">/</span>
            <span class="total-rounds">5</span>
          </div>

          <div class="timer">
            <span class="timer-icon">⏱️</span>
            <span id="timer-display">3:00</span>
          </div>
        </div>

        <!-- 血條/計量條 -->
        <div class="health-meters">
          <div class="meter-group">
            <span class="meter-label">公民怒氣</span>
            <div class="health-bar">
              <div id="citizen-health-bar" class="health-fill citizen"></div>
            </div>
            <span id="citizen-health-text" class="meter-value">100</span>
          </div>
          <div class="meter-group">
            <span class="meter-label">委員誠信</span>
            <div class="health-bar">
              <div
                id="legislator-health-bar"
                class="health-fill legislator"
              ></div>
            </div>
            <span id="legislator-health-text" class="meter-value">100</span>
          </div>
        </div>

        <!-- 話術防禦計 -->
        <div class="bullshit-meter-container">
          <div class="meter-header">
            <span class="meter-icon">💩</span>
            <span class="meter-title">話術防禦計</span>
            <span class="meter-subtitle">點擊破綻讓防禦崩潰！</span>
          </div>
          <div class="meter-bar-container">
            <div id="bullshit-bar" class="meter-fill"></div>
            <span id="bullshit-text" class="meter-text">100%</span>
          </div>
          <div class="meter-status" id="meter-status">
            <span class="status-text">話術滿載中...</span>
          </div>
        </div>

        <!-- 罷免理由全螢幕模態框 -->
        <div id="reason-modal" class="reason-modal hidden">
          <div class="reason-modal-content">
            <div class="reason-modal-header">
              <div class="reason-badge">
                <span class="reason-icon">⚖️</span>
                <span class="reason-label">罷免理由</span>
              </div>
              <div class="round-indicator">
                <span id="modal-round-number">第 1 回合</span>
              </div>
            </div>

            <div class="reason-modal-body">
              <h2 id="modal-reason-title" class="reason-title-large">
                載入中...
              </h2>
              <div id="modal-reason-content" class="reason-content-large">
                載入中...
              </div>
            </div>

            <div class="reason-modal-footer">
              <button id="start-battle-btn" class="btn btn-battle">
                ⚔️ 開始攻防戰！
              </button>
            </div>
          </div>
        </div>

        <!-- 主要遊戲區域 -->
        <div class="main-game-area">
          <!-- 委員答辯卡片 -->
          <div class="legislator-card">
            <div class="legislator-header">
              <div class="legislator-avatar">
                <span class="avatar-emoji">🤵</span>
                <div class="avatar-glow"></div>
              </div>
              <div class="legislator-info">
                <h2 class="legislator-name">葉元之委員</h2>
                <div class="legislator-subtitle">委員答辯</div>
              </div>
              <div class="warning-badge">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">小心話術陷阱！</span>
              </div>
            </div>

            <div class="rebuttal-content">
              <div id="rebuttal-text" class="rebuttal-text">載入中...</div>
            </div>
          </div>

          <!-- 遊戲指示區 -->
          <div class="game-instruction">
            <div class="instruction-content">
              <span class="instruction-icon">🎯</span>
              <span class="instruction-text">點擊話術破綻！</span>
              <span class="instruction-hint">找出上方答辯中的破綻關鍵字</span>
            </div>

            <div class="combo-display hidden" id="combo-display">
              <span class="combo-label">連擊</span>
              <span class="combo-count" id="combo-count">0</span>
              <span class="combo-suffix">HIT!</span>
            </div>
          </div>

          <!-- 事實查核彈出區 -->
          <div class="fact-check-popup hidden" id="fact-check-popup">
            <div class="popup-header">
              <span class="popup-icon">🔍</span>
              <span class="popup-title">事實查核</span>
              <button class="popup-close" id="popup-close">×</button>
            </div>
            <div class="popup-content">
              <div class="fact-check-media" id="fact-check-media">
                <!-- 圖片或影片將在這裡顯示 -->
              </div>
              <div class="fact-check-text" id="fact-check-text">載入中...</div>
              <div class="fact-check-source" id="fact-check-source">
                <span class="source-label">資料來源：</span>
                <a href="#" target="_blank" id="source-link">查看完整報導</a>
              </div>
            </div>
            <div class="popup-footer">
              <button class="slap-button" id="slap-button">
                <span class="slap-emoji">👋</span>
                <span class="slap-text">打臉完成！</span>
              </button>
            </div>
          </div>

          <!-- 煙霧彈特殊回合 -->
          <div id="smokescreen-overlay" class="smokescreen-overlay hidden">
            <div class="smokescreen-content">
              <div class="smokescreen-images">
                <div class="smoke-item">🏗️ 會勘國小建設</div>
                <div class="smoke-item">🚇 爭取輕軌建設</div>
                <div class="smoke-item">🏥 關心醫療議題</div>
                <div class="smoke-item">🏢 活化土地利用</div>
                <div class="smoke-item">🛣️ 道路改善工程</div>
                <div class="smoke-item">🌳 公園綠化計畫</div>
              </div>

              <div class="smokescreen-action">
                <div class="core-question hidden" id="core-question">
                  這些地方服務值得肯定，但為何在國家級重大法案上，您總是選擇跟隨黨意而非民意？
                </div>

                <button id="clear-smoke-btn" class="btn btn-warning">
                  🌪️ 拒絕模糊焦點！
                </button>

                <div class="smoke-progress">
                  <div
                    id="smoke-progress-bar"
                    class="smoke-progress-fill"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Fact Check 卡片 -->
        <div id="fact-check-modal" class="modal hidden">
          <div class="modal-content">
            <div class="modal-header">
              <span class="modal-icon">🔍</span>
              <span class="modal-title">事實查核</span>
              <button id="close-fact-check" class="close-btn">×</button>
            </div>
            <div id="fact-check-content" class="modal-body">載入中...</div>
            <div class="modal-footer">
              <button id="next-round-btn" class="btn btn-primary">
                下一回合
              </button>
              <a
                id="evidence-link"
                href="#"
                target="_blank"
                class="btn btn-secondary"
                >查看證據</a
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 結算畫面 -->
      <div id="result-screen" class="screen">
        <div class="result-content">
          <div class="result-character">
            <div id="result-avatar" class="character-avatar">
              <div class="avatar-face">🤵</div>
              <div class="avatar-expression" id="result-expression">😅</div>
            </div>
          </div>

          <div class="result-info">
            <h2 id="result-title" class="result-title">評級結果</h2>
            <div id="result-description" class="result-description">
              載入中...
            </div>
            <div id="final-score" class="final-score">
              最終得分：<span id="score-value">0</span>
            </div>
          </div>

          <!-- 行動呼籲按鈕 -->
          <div class="cta-buttons">
            <a href="#" id="petition-link" class="btn btn-primary btn-large">
              📝 前往連署教學
            </a>

            <button id="share-btn" class="btn btn-secondary">
              📤 分享給朋友，揭穿真相
            </button>

            <a href="#" id="full-document-link" class="btn btn-outline">
              📄 查看完整罷免理由書
            </a>

            <button id="restart-btn" class="btn btn-outline">
              🔄 再玩一次
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 載入遊戲腳本 -->
    <script src="game-data.js"></script>
    <script src="game-engine.js"></script>
    <script src="ui-controller.js"></script>
    <script src="main.js"></script>
  </body>
</html>
