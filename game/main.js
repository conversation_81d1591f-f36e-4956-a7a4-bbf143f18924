// 主要遊戲啟動腳本
class RecallGame {
  constructor() {
    this.gameEngine = null;
    this.uiController = null;
    this.isInitialized = false;

    // 等待DOM載入完成
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        this.initialize();
      });
    } else {
      this.initialize();
    }
  }

  initialize() {
    try {
      console.log("罷免元兇遊戲初始化開始...");

      // 檢查必要的DOM元素
      if (!this.checkRequiredElements()) {
        console.error("缺少必要的DOM元素，無法初始化遊戲");
        return;
      }

      // 檢查必要的類別是否存在
      if (typeof GameEngine === "undefined") {
        throw new Error("GameEngine 類別未定義");
      }
      if (typeof UIController === "undefined") {
        throw new Error("UIController 類別未定義");
      }
      if (typeof GAME_DATA === "undefined") {
        throw new Error("GAME_DATA 未定義");
      }

      // 初始化遊戲引擎
      this.gameEngine = new GameEngine();

      // 初始化UI控制器
      this.uiController = new UIController(this.gameEngine);

      // 設置響應式處理
      if (this.uiController.initializeResponsiveHandlers) {
        this.uiController.initializeResponsiveHandlers();
      }

      // 設置全域錯誤處理
      this.setupErrorHandling();

      // 設置性能監控
      this.setupPerformanceMonitoring();

      // 預載入資源
      this.preloadResources();

      this.isInitialized = true;
      console.log("罷免元兇遊戲初始化完成！");

      // 顯示歡迎訊息
      this.showWelcomeMessage();
    } catch (error) {
      console.error("遊戲初始化失敗:", error);
      this.showErrorMessage("遊戲載入失敗，請重新整理頁面再試。");
    }
  }

  checkRequiredElements() {
    const requiredElements = [
      "game-container",
      "intro-screen",
      "game-screen",
      "result-screen",
      "start-game-btn",
      "current-round",
      "timer-display",
      "bullshit-bar",
      "rebuttal-text",
      "fact-check-popup",
      "smokescreen-overlay",
    ];

    const missingElements = [];

    requiredElements.forEach((elementId) => {
      const element = document.getElementById(elementId);
      if (!element) {
        missingElements.push(elementId);
      }
    });

    if (missingElements.length > 0) {
      console.error("缺少以下必要元素:", missingElements);
      return false;
    }

    return true;
  }

  setupErrorHandling() {
    // 全域錯誤處理
    window.addEventListener("error", (event) => {
      console.error("全域錯誤:", event.error);
      this.handleGameError(event.error);
    });

    // Promise 錯誤處理
    window.addEventListener("unhandledrejection", (event) => {
      console.error("未處理的Promise錯誤:", event.reason);
      this.handleGameError(event.reason);
    });
  }

  handleGameError(error) {
    // 如果遊戲正在進行中，暫停遊戲
    if (this.gameEngine && this.gameEngine.gameState.isGameActive) {
      this.gameEngine.gameState.isGameActive = false;
      this.gameEngine.gameState.isRoundActive = false;
    }

    // 顯示錯誤訊息
    this.showErrorMessage("遊戲發生錯誤，請重新整理頁面。");

    // 記錄錯誤（用於除錯）
    this.logError(error);
  }

  logError(error) {
    const errorInfo = {
      message: error && error.message ? error.message : "未知錯誤",
      stack: error && error.stack ? error.stack : "無堆疊資訊",
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      gameState: this.gameEngine ? this.gameEngine.gameState : null,
      originalError: error,
    };

    console.error("詳細錯誤資訊:", errorInfo);

    // 這裡可以添加錯誤回報到伺服器的邏輯
    // this.reportErrorToServer(errorInfo);
  }

  setupPerformanceMonitoring() {
    // 監控遊戲性能
    if ("performance" in window) {
      // 記錄載入時間
      window.addEventListener("load", () => {
        const loadTime = performance.now();
        console.log(`遊戲載入時間: ${loadTime.toFixed(2)}ms`);

        // 記錄到分析工具
        if (typeof gtag !== "undefined") {
          gtag("event", "timing_complete", {
            name: "game_load",
            value: Math.round(loadTime),
          });
        }
      });
    }

    // 監控記憶體使用（如果支援）
    if ("memory" in performance) {
      setInterval(() => {
        const memory = performance.memory;
        if (memory.usedJSHeapSize > 50 * 1024 * 1024) {
          // 50MB
          console.warn("記憶體使用量較高:", memory);
        }
      }, 30000); // 每30秒檢查一次
    }
  }

  preloadResources() {
    // 預載入可能需要的資源
    const imagesToPreload = [
      // 這裡可以添加需要預載入的圖片URL
    ];

    imagesToPreload.forEach((src) => {
      const img = new Image();
      img.src = src;
    });

    // 預載入音效（如果有的話）
    this.preloadAudio();
  }

  preloadAudio() {
    // 預載入音效文件
    const audioFiles = {
      correct: "sounds/correct.mp3",
      wrong: "sounds/wrong.mp3",
      round_complete: "sounds/round_complete.mp3",
      game_complete: "sounds/game_complete.mp3",
    };

    Object.entries(audioFiles).forEach(([key, src]) => {
      const audio = new Audio();
      audio.preload = "auto";
      audio.src = src;

      // 儲存音效引用（可選）
      if (!window.gameAudio) {
        window.gameAudio = {};
      }
      window.gameAudio[key] = audio;
    });
  }

  showWelcomeMessage() {
    // 顯示歡迎訊息（可選）
    console.log(`
🎮 歡迎來到《罷免元兇？》
📋 遊戲目標：在3分鐘內識破政治話術
⚔️ 玩法：點擊答辯中的話術破綻
💨 特殊：撥開煙霧彈，揭露真相
🎯 準備好了嗎？點擊開始遊戲！
        `);
  }

  showErrorMessage(message) {
    // 創建錯誤提示
    const errorDiv = document.createElement("div");
    errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ef4444;
            color: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            text-align: center;
            max-width: 400px;
            font-family: 'Noto Sans TC', sans-serif;
        `;

    errorDiv.innerHTML = `
            <div style="font-size: 2rem; margin-bottom: 1rem;">⚠️</div>
            <div style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem;">${message}</div>
            <button onclick="window.location.reload()" style="
                background: white;
                color: #ef4444;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 6px;
                font-weight: 600;
                cursor: pointer;
            ">重新載入</button>
        `;

    document.body.appendChild(errorDiv);
  }

  // 公開方法供外部調用
  getGameState() {
    return this.gameEngine ? this.gameEngine.gameState : null;
  }

  pauseGame() {
    if (this.gameEngine && this.gameEngine.gameState.isGameActive) {
      this.gameEngine.gameState.isGameActive = false;
      this.gameEngine.gameState.isRoundActive = false;

      // 暫停計時器
      if (this.gameEngine.timers.gameTimer) {
        clearInterval(this.gameEngine.timers.gameTimer);
      }
      if (this.gameEngine.timers.roundTimer) {
        clearInterval(this.gameEngine.timers.roundTimer);
      }
    }
  }

  resumeGame() {
    if (this.gameEngine && !this.gameEngine.gameState.isGameActive) {
      this.gameEngine.gameState.isGameActive = true;
      this.gameEngine.gameState.isRoundActive = true;

      // 恢復計時器
      this.gameEngine.startGameTimer();
      this.gameEngine.startRoundTimer();
    }
  }

  // 除錯方法
  debugInfo() {
    if (!this.isInitialized) {
      console.log("遊戲尚未初始化");
      return;
    }

    console.log("=== 遊戲除錯資訊 ===");
    console.log("遊戲狀態:", this.gameEngine.gameState);
    console.log("當前回合資料:", this.gameEngine.currentRoundData);
    console.log("計時器狀態:", {
      gameTimer: !!this.gameEngine.timers.gameTimer,
      roundTimer: !!this.gameEngine.timers.roundTimer,
    });
    console.log("==================");
  }
}

// 頁面可見性變化處理（防止背景運行時計時器繼續）
document.addEventListener("visibilitychange", () => {
  if (window.recallGame) {
    if (document.hidden) {
      window.recallGame.pauseGame();
    } else {
      window.recallGame.resumeGame();
    }
  }
});

// 防止意外離開頁面
window.addEventListener("beforeunload", (event) => {
  if (window.recallGame && window.recallGame.getGameState()?.isGameActive) {
    event.preventDefault();
    event.returnValue = "遊戲正在進行中，確定要離開嗎？";
    return event.returnValue;
  }
});

// 啟動遊戲
window.recallGame = new RecallGame();

// 開發者工具（僅在開發環境）
if (
  window.location.hostname === "localhost" ||
  window.location.hostname === "127.0.0.1"
) {
  window.debugGame = () => {
    if (window.recallGame) {
      window.recallGame.debugInfo();
    }
  };

  console.log("開發模式：輸入 debugGame() 查看遊戲狀態");
}
