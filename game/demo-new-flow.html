<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打臉話術遊戲 - 新流程演示</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 演示專用樣式 */
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            min-height: 100vh;
        }
        
        .demo-title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .demo-subtitle {
            text-align: center;
            color: #94a3b8;
            font-size: 1.2rem;
            margin-bottom: 3rem;
        }
        
        .flow-step {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .step-title {
            color: #fbbf24;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .step-description {
            color: #e2e8f0;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            background: linear-gradient(135deg, #1d4ed8, #1e40af);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🎯 打臉話術遊戲</h1>
        <p class="demo-subtitle">全新流程設計 - 更簡潔、更有趣、更有教育意義</p>
        
        <div class="flow-step">
            <div class="step-title">
                <span>1️⃣</span>
                <span>話術防禦計</span>
            </div>
            <div class="step-description">
                遊戲開始時，話術防禦計滿載 100%。玩家每找到一個破綻，防禦就會下降，直到完全破功！
            </div>
            <div class="bullshit-meter-container">
                <div class="meter-header">
                    <span class="meter-icon">💩</span>
                    <span class="meter-title">話術防禦計</span>
                    <span class="meter-subtitle">點擊破綻讓防禦崩潰！</span>
                </div>
                <div class="meter-bar-container">
                    <div class="meter-fill" style="width: 100%;"></div>
                    <span class="meter-text">100%</span>
                </div>
                <div class="meter-status">
                    <span class="status-text">話術滿載中... 💩</span>
                </div>
            </div>
        </div>
        
        <div class="flow-step">
            <div class="step-title">
                <span>2️⃣</span>
                <span>委員答辯卡片</span>
            </div>
            <div class="step-description">
                葉元之委員的答辯內容以大卡片形式呈現，破綻關鍵字會高亮顯示，玩家點擊即可觸發事實查核。
            </div>
            <div class="legislator-card">
                <div class="legislator-header">
                    <div class="legislator-avatar">
                        <span class="avatar-emoji">🤵</span>
                        <div class="avatar-glow"></div>
                    </div>
                    <div class="legislator-info">
                        <h2 class="legislator-name">葉元之委員</h2>
                        <div class="legislator-subtitle">委員答辯</div>
                    </div>
                    <div class="warning-badge">
                        <span class="warning-icon">⚠️</span>
                        <span class="warning-text">小心話術陷阱！</span>
                    </div>
                </div>
                
                <div class="rebuttal-content">
                    <div class="rebuttal-text">
                        此為 <span style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: white; padding: 2px 6px; border-radius: 4px; cursor: pointer;">斷章取義</span>，本人實際上 <span style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: white; padding: 2px 6px; border-radius: 4px; cursor: pointer;">沒看內容</span>，絕未做過 <span style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: white; padding: 2px 6px; border-radius: 4px; cursor: pointer;">沒事內容</span>，這是 <span style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: white; padding: 2px 6px; border-radius: 4px; cursor: pointer;">惡意抹黑</span> 的 <span style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: white; padding: 2px 6px; border-radius: 4px; cursor: pointer;">政治操作</span>，完全是 <span style="background: linear-gradient(135deg, #fbbf24, #f59e0b); color: white; padding: 2px 6px; border-radius: 4px; cursor: pointer;">不實指控</span>。
                    </div>
                </div>
            </div>
        </div>
        
        <div class="flow-step">
            <div class="step-title">
                <span>3️⃣</span>
                <span>遊戲指示區</span>
            </div>
            <div class="step-description">
                清楚的遊戲指示和連擊顯示，讓玩家知道該做什麼，並獲得即時回饋。
            </div>
            <div class="game-instruction">
                <div class="instruction-content">
                    <span class="instruction-icon">🎯</span>
                    <span class="instruction-text">點擊話術破綻！</span>
                    <span class="instruction-hint">找出上方答辯中的破綻關鍵字</span>
                </div>
                
                <div class="combo-display">
                    <span class="combo-label">連擊</span>
                    <span class="combo-count">3</span>
                    <span class="combo-suffix">HIT!</span>
                </div>
            </div>
        </div>
        
        <div class="flow-step">
            <div class="step-title">
                <span>4️⃣</span>
                <span>事實查核彈出框</span>
            </div>
            <div class="step-description">
                點擊破綻後，會彈出事實查核框，包含圖片/影片、查核內容、資料來源，以及幽默的「打臉」按鈕。
            </div>
            <button class="demo-button" onclick="showDemoPopup()">點擊查看事實查核彈出框</button>
        </div>
        
        <div class="flow-step">
            <div class="step-title">
                <span>5️⃣</span>
                <span>幽默打臉訊息</span>
            </div>
            <div class="step-description">
                完成打臉後，會在右上角顯示幽默的訊息，增加遊戲的娛樂性和教育效果。
            </div>
            <button class="demo-button" onclick="showDemoHumor()">點擊查看幽默訊息</button>
        </div>
    </div>
    
    <!-- 演示用的事實查核彈出框 -->
    <div class="fact-check-popup hidden" id="demo-popup">
        <div class="popup-header">
            <span class="popup-icon">🔍</span>
            <span class="popup-title">事實查核</span>
            <button class="popup-close" onclick="closeDemoPopup()">×</button>
        </div>
        <div class="popup-content">
            <div class="fact-check-media">
                <div style="background: #f8fafc; padding: 2rem; text-align: center; border-radius: 8px; color: #6b7280;">
                    📹 影片：葉元之委員親口承認「沒看內容就舉手」的節目片段
                </div>
            </div>
            <div class="fact-check-text">
                事實查核：委員曾在節目上親口承認沒看預算就舉手，網路皆有影片存證。
            </div>
            <div class="fact-check-source">
                <span class="source-label">資料來源：</span>
                <a href="#" class="source-link">查看完整報導</a>
            </div>
        </div>
        <div class="popup-footer">
            <button class="slap-button" onclick="completeDemoSlap()">
                <span class="slap-emoji">👋</span>
                <span class="slap-text">💥 打臉成功！委員自己都承認了！</span>
            </button>
        </div>
    </div>
    
    <script>
        function showDemoPopup() {
            document.getElementById('demo-popup').classList.remove('hidden');
        }
        
        function closeDemoPopup() {
            document.getElementById('demo-popup').classList.add('hidden');
        }
        
        function completeDemoSlap() {
            showDemoHumor();
            closeDemoPopup();
        }
        
        function showDemoHumor() {
            const humorDiv = document.createElement('div');
            humorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #f59e0b, #d97706);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
                z-index: 4000;
                font-weight: 600;
                max-width: 300px;
                animation: slideInRight 0.5s ease-out;
            `;
            humorDiv.textContent = '連自己都打臉自己，這招叫做「自爆式答辯」！';
            
            document.body.appendChild(humorDiv);
            
            setTimeout(() => {
                humorDiv.style.animation = 'slideOutRight 0.5s ease-in forwards';
                setTimeout(() => {
                    document.body.removeChild(humorDiv);
                }, 500);
            }, 3000);
        }
    </script>
</body>
</html>
