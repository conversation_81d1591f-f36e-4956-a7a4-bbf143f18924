<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>開始按鈕測試</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="game-container">
        <!-- 開場畫面 -->
        <div id="intro-screen" class="screen active">
            <div class="intro-hero">
                <!-- 背景動畫效果 -->
                <div class="hero-background">
                    <div class="floating-emoji">💩</div>
                    <div class="floating-emoji">🎯</div>
                    <div class="floating-emoji">👋</div>
                    <div class="floating-emoji">🔍</div>
                    <div class="floating-emoji">⚖️</div>
                </div>
                
                <!-- 主角登場 -->
                <div class="character-showcase">
                    <div class="character-card">
                        <div class="character-avatar-big">
                            <div class="avatar-ring"></div>
                            <div class="avatar-face">🤵</div>
                            <div class="avatar-speech-bubble">
                                <span class="speech-text">我沒看內容就舉手...</span>
                                <div class="bubble-tail"></div>
                            </div>
                        </div>
                        <div class="character-info">
                            <h2 class="character-name">葉元之委員</h2>
                            <div class="character-title">話術大師</div>
                        </div>
                    </div>
                </div>

                <!-- 遊戲標題 -->
                <div class="game-title-section">
                    <h1 class="game-title-main">
                        <span class="title-emoji">💥</span>
                        <span class="title-text">打臉話術王</span>
                        <span class="title-emoji">💥</span>
                    </h1>
                    <div class="game-subtitle">
                        <span class="subtitle-text">揭穿政治話術，還原事實真相！</span>
                    </div>
                </div>

                <!-- 遊戲特色 -->
                <div class="game-features">
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <div class="feature-text">點擊破綻</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔍</div>
                        <div class="feature-text">事實查核</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">👋</div>
                        <div class="feature-text">幽默打臉</div>
                    </div>
                </div>

                <!-- 開始按鈕 -->
                <div class="start-button-section">
                    <button id="start-game-btn" class="start-button">
                        <span class="button-icon">🚀</span>
                        <span class="button-text">開始打臉之旅</span>
                        <div class="button-glow"></div>
                    </button>
                    <div class="start-hint">
                        <span class="hint-text">準備好揭穿話術了嗎？</span>
                    </div>
                </div>

                <!-- 遊戲說明 -->
                <div class="game-instructions">
                    <div class="instruction-item">
                        <span class="instruction-number">1</span>
                        <span class="instruction-text">閱讀委員答辯</span>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-number">2</span>
                        <span class="instruction-text">找出話術破綻</span>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-number">3</span>
                        <span class="instruction-text">查看事實證據</span>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-number">4</span>
                        <span class="instruction-text">完成幽默打臉</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 遊戲畫面 -->
        <div id="game-screen" class="screen">
            <div style="padding: 2rem; text-align: center; color: white;">
                <h1>🎉 遊戲開始了！</h1>
                <p>按鈕功能正常，遊戲引擎已啟動</p>
                <button onclick="goBack()" style="padding: 1rem 2rem; margin-top: 1rem; background: #3b82f6; color: white; border: none; border-radius: 8px; cursor: pointer;">
                    返回開始畫面
                </button>
            </div>
        </div>

        <!-- 結果畫面 -->
        <div id="result-screen" class="screen">
            <div style="padding: 2rem; text-align: center; color: white;">
                <h1>遊戲結束</h1>
            </div>
        </div>
    </div>

    <script>
        // 簡化的測試腳本
        document.addEventListener('DOMContentLoaded', function() {
            const startBtn = document.getElementById('start-game-btn');
            const introScreen = document.getElementById('intro-screen');
            const gameScreen = document.getElementById('game-screen');
            
            if (startBtn) {
                startBtn.addEventListener('click', function() {
                    console.log('開始按鈕被點擊！');
                    
                    // 切換畫面
                    introScreen.classList.remove('active');
                    gameScreen.classList.add('active');
                    
                    // 添加一些視覺回饋
                    startBtn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        startBtn.style.transform = 'scale(1)';
                    }, 150);
                });
            } else {
                console.error('找不到開始按鈕！');
            }
        });
        
        function goBack() {
            document.getElementById('game-screen').classList.remove('active');
            document.getElementById('intro-screen').classList.add('active');
        }
        
        // 測試動畫效果
        console.log('測試頁面載入完成');
        console.log('開始按鈕元素:', document.getElementById('start-game-btn'));
    </script>
</body>
</html>
