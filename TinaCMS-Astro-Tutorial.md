# TinaCMS 與 Astro 整合教學

本專案已配置好 TinaCMS 作為內容管理系統，專門用於管理媒體內容（影片、圖片等）。以下是使用、配置和開發的指南。

## 本地開發

要在本地啟動 TinaCMS 與 Astro 的開發環境，請按照以下步驟：

1. 安裝依賴：
   ```bash
   npm install
   ```

2. 啟動開發環境：
   ```bash
   npm run dev
   ```

這個命令會同時啟動 TinaCMS 本地服務器和 Astro 開發服務器。你應該能夠看到兩個 URL：
- Astro 網站：http://localhost:4321
- TinaCMS 後台：http://localhost:4321/admin/

## TinaCMS 的登入與使用

首次設置 TinaCMS 時，你需要創建一個帳戶並獲取 Client ID 和 Token：

1. 訪問 [Tina Cloud](https://app.tina.io/) 建立帳戶
2. 創建一個新項目
3. 獲取 Client ID 和 Token
4. 在項目根目錄創建 `.env` 文件，添加以下內容：
   ```
   TINA_CLIENT_ID=你的Client_ID
   TINA_TOKEN=你的Token
   ```

對於本地開發，TinaCMS 可以在沒有這些憑證的情況下工作，但數據將只保存在本地。

## 內容結構

本專案使用 Astro 的 Content Collections 和 TinaCMS 來管理媒體內容：

- `/src/content/media/` - 存放媒體內容的 Markdown 文件
- `/public/uploads/` - 存放上傳的圖片和其他媒體文件

## 媒體內容架構

每個媒體項目包含以下字段：

- `title` - 標題
- `description` - 描述（可選）
- `date` - 日期
- `orderID` - 排序順序（可選）
- `youtubeId` - YouTube 影片 ID（可選）
- `image` - 主要圖片路徑（可選）
- `source` - 來源（可選）
- `tags` - 標籤列表（如 ["罷免歌曲", "創意改編"]）
- `groups` - 分組列表（如 ["罷免金曲", "精彩影片集錦"]）

## 訪問網站

在開發模式下，可以通過以下路徑訪問：

- 媒體列表：http://localhost:4321/media
- 單一媒體：http://localhost:4321/media/[slug]
- TinaCMS 管理界面：http://localhost:4321/admin

## 生產部署配置

對於生產環境，確保你已經設置了 TinaCMS 的環境變數：

1. 在你的部署平台（如 Vercel、Netlify）設置環境變數：
   - `TINA_CLIENT_ID`
   - `TINA_TOKEN`

2. 使用以下命令構建項目：
   ```bash
   npm run build
   ```

## 自定義擴展

如要擴展媒體內容架構：

1. 修改 `tina/config.ts` 文件，添加或修改字段
2. 更新 `src/content/config.ts` 中的 schema 定義
3. 更新相關組件，如 `MediaCard.astro` 和媒體詳情頁面

## 疑難排解

如果在運行過程中遇到問題：

1. 確保所有依賴都已安裝：`npm install`
2. 刪除 `.astro` 和 `node_modules/.vite` 文件夾，然後重新啟動
3. 檢查 TinaCMS 控制台日誌，尋找可能的錯誤

如需更多幫助，可以參考：
- [TinaCMS 文檔](https://tina.io/docs/)
- [Astro 文檔](https://docs.astro.build/)
- [Astro Content Collections 文檔](https://docs.astro.build/en/guides/content-collections/) 