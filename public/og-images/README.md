# 立委 OG 圖片檔名對照表

此目錄包含用於社群分享的 Open Graph (OG) 圖片，檔名採用 Base64 編碼格式以避免中文字元在 URL 中造成問題。

## 檔名格式

每個立委照片的檔名格式為：`legislator-{Base64編碼}.jpg`

其中 Base64 編碼是將立委名稱轉換而成，移除了尾部的 `=` 填充字元。

## 轉換腳本

可以使用專案根目錄中的 `og-image-generator.js` 腳本自動生成這些檔案：

```bash
node og-image-generator.js
```

## 手動更新特定立委照片

如需手動更新單一立委照片，可使用以下命令：

```bash
# 使用 Node.js 轉換單一檔案 (ES Module 方式)
node -e "import fs from 'fs'; import path from 'path'; import { fileURLToPath } from 'url'; const __dirname = path.dirname(fileURLToPath(import.meta.url)); const name='葉元之'; const encoded=Buffer.from(name).toString('base64').replace(/=/g,''); const srcFile=path.join(__dirname, 'public/legislators', name + '.jpg'); const destFile=path.join(__dirname, 'public/og-images', 'legislator-' + encoded + '.jpg'); fs.copyFileSync(srcFile, destFile); console.log('已更新: ' + destFile);" --input-type=module
```

## 立委名稱與檔名對照表

| 立委名稱 | Base64 編碼 | OG 圖片檔名 |
|----------|------------|------------|
| 江啟臣   | 5rGf5ZWG6Iux | legislator-5rGf5ZWG6Iux.jpg |
| 林德福   | 5p6X5b636IGM | legislator-5p6X5b636IGM.jpg |
| 洪孟楷   | 5rSq5a2i5qiC | legislator-5rSq5a2i5qiC.jpg |
| 徐巧芯   | 5b636LCO6IqS | legislator-5b636LCO6IqS.jpg |
| 馬文君   | 6ams5paH5Y2O | legislator-6ams5paH5Y2O.jpg |
| 張智倫   | 5byg5pm66LuN | legislator-5byg5pm66LuN.jpg |
| 陳玉珍   | 6Zmw546J54+g | legislator-6Zmw546J54+g.jpg |
| 葉元之   | 6JaZ5YWD5LmL | legislator-6JaZ5YWD5LmL.jpg |
| 王鴻薇   | 546L6bOk6Jap | legislator-546L6bOk6Jap.jpg |
| 羅智強   | 576O5pm66Iux | legislator-576O5pm66Iux.jpg |

## 注意事項

1. 更新立委照片時，請先更新 `public/legislators/` 目錄中的原始照片，再執行轉換腳本
2. 轉換後的檔案請勿手動重命名，以確保命名一致性
3. 如有新增立委，請執行轉換腳本後並將新的對照結果更新到本文件
4. 在 Meta 除錯工具中測試 OG 圖片時，請使用純英文檔名的版本 