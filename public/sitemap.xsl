<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0"
  xmlns:html="http://www.w3.org/TR/REC-html40"
  xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
  xmlns:sitemap="http://www.sitemaps.org/schemas/sitemap/0.9"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
  <xsl:output method="html" version="1.0" encoding="UTF-8" indent="yes"/>
  <xsl:template match="/">
    <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <title>XML Sitemap</title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <style type="text/css">
          body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            color: #333;
            margin: 0;
            padding: 2rem;
          }
          a {
            color: #2563eb;
            text-decoration: none;
          }
          a:hover {
            text-decoration: underline;
          }
          h1 {
            font-size: 1.75rem;
            margin-top: 0;
            margin-bottom: 1.5rem;
          }
          .intro {
            margin-bottom: 2rem;
            line-height: 1.6;
          }
          table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 2rem;
          }
          th {
            background-color: #f8fafc;
            text-align: left;
            padding: 0.75rem;
            font-weight: 600;
            border-bottom: 2px solid #e2e8f0;
          }
          td {
            padding: 0.75rem;
            border-bottom: 1px solid #e2e8f0;
          }
          tr:hover td {
            background-color: #f8fafc;
          }
          .footer {
            font-size: 0.875rem;
            color: #64748b;
          }
        </style>
      </head>
      <body>
        <h1>XML 站點地圖</h1>
        <div class="intro">
          <p>
            這是為搜索引擎生成的 XML 站點地圖，可用於幫助搜索引擎發現和索引本站的內容。
            <br />
            了解更多關於 <a href="https://www.sitemaps.org/" target="_blank" rel="noopener">XML 站點地圖</a>。
          </p>
        </div>
        <xsl:apply-templates/>
        <div class="footer">
          <p>由 Astro Sitemap Integration 生成。</p>
        </div>
      </body>
    </html>
  </xsl:template>
  
  <!-- sitemap:urlset -->
  <xsl:template match="sitemap:urlset">
    <table>
      <thead>
        <tr>
          <th>URL</th>
          <th>最後修改</th>
          <th>變更頻率</th>
          <th>優先級</th>
        </tr>
      </thead>
      <tbody>
        <xsl:for-each select="sitemap:url">
          <tr>
            <td>
              <a href="{sitemap:loc}"><xsl:value-of select="sitemap:loc"/></a>
            </td>
            <td>
              <xsl:if test="sitemap:lastmod">
                <xsl:value-of select="sitemap:lastmod"/>
              </xsl:if>
            </td>
            <td>
              <xsl:if test="sitemap:changefreq">
                <xsl:value-of select="sitemap:changefreq"/>
              </xsl:if>
            </td>
            <td>
              <xsl:if test="sitemap:priority">
                <xsl:value-of select="sitemap:priority"/>
              </xsl:if>
            </td>
          </tr>
        </xsl:for-each>
      </tbody>
    </table>
  </xsl:template>
  
  <!-- sitemap:sitemapindex -->
  <xsl:template match="sitemap:sitemapindex">
    <table>
      <thead>
        <tr>
          <th>站點地圖</th>
        </tr>
      </thead>
      <tbody>
        <xsl:for-each select="sitemap:sitemap">
          <tr>
            <td>
              <a href="{sitemap:loc}"><xsl:value-of select="sitemap:loc"/></a>
            </td>
          </tr>
        </xsl:for-each>
      </tbody>
    </table>
  </xsl:template>
</xsl:stylesheet> 