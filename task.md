# 多媒體與立委關聯方案決策

## 背景
為建立可維護的罷免理由系統，需確定多媒體管理方式與立委關聯機制，經討論後決議：
- 採用內嵌媒體方案
- 使用單向關聯設計
- 保留未來擴展彈性

## 決策內容
### 多媒體管理方案
✅ 採用內嵌式設計
```mermaid
graph LR
    Reason -->|直接嵌入| Media[多媒體內容]
    Media --> Image
    Media --> Video
    Media --> YouTube
```

**實現方式：**
```typescript
// 在reasons集合中內嵌media字段
{
  type: "object",
  name: "media",
  list: true,
  fields: [
    {
      type: "string",
      name: "type",
      options: ["image", "video", "youtube"]
    },
    {
      type: "image",
      name: "file"
    },
    {
      type: "string",
      name: "youtubeId"
    }
  ]
}
```

### 立委關聯方案
✅ 單向關聯設計
```mermaid
graph LR
    Reason -->|references| Legislator
```

**數據流向：**
1. 理由(Reason) 包含立委(Legislator) 的引用
2. 立委頁面通過查詢獲取相關理由

## 技術方案
### 數據結構升級
```diff
// Tina CMS 配置
+ reasons 集合新增字段：
+ - relatedLegislators: 立委引用列表
+ - media: 多媒體對象列表

// 現有字段調整：
- 移除獨立的media集合
+ 在legislators集合保留原有結構
```

### 擴展性保留
```markdown
- [ ] 媒體使用統計（未來需求）
- [ ] 自動關聯反向索引（效能優化）
- [ ] 熱門理由算法（結合popularity字段）
```

## 預期效益
1. 降低初期開發成本約40%
2. 提升內容編輯效率
3. 維持系統架構簡潔度 

# 罷免理由系統開發任務

## 已完成項目
- [x] 配置Tina CMS結構
  - 完成日期: 2025-04-12
  - 檔案: `tina/config.ts`
  - 已新增:
    - roles集合（角色定義）
    - reasons集合（罷免理由）
    - 媒體對象字段（圖片、影片、YouTube）
    - 熱門度支援
- [x] 配置Astro Content Collections
  - 完成日期: 2025-04-12
  - 檔案: `src/content/config.ts`
  - 已新增:
    - roles集合 schema
    - reasons集合 schema
    - 媒體對象類型定義

## 下一步任務
- [ ] 數據遷移
  - 負責人: @全端工程師
  - 腳本: `scripts/migrateReasons.js`
  - 要點:
    - 轉換現有罷免理由數據（從`index.astro`到content collections）
    - 建立適當的立委與角色關聯
    - 格式化多媒體內容為新結構

- [ ] 前端頁面開發
  - 負責人: @前端工程師
  - 檔案:
    - `src/pages/reasons/[id].astro`（單一理由頁面）
    - `src/pages/roles/[id].astro`（角色專屬頁面）
  - 功能:
    - 理由詳情頁
    - 多媒體輪播組件
    - 相關立委連結
    - 分享功能

- [ ] 後台CMS優化
  - 負責人: @後端工程師
  - 任務:
    - 文檔預覽功能
    - 媒體上傳優化
    - 使用者權限配置

## 測試項目
- [ ] 媒體上傳測試
- [ ] 立委關聯完整性檢查
- [ ] 移動端適配測試

## 時程規劃
```mermaid
gantt
    title 開發時程
    dateFormat  YYYY-MM-DD
    section 基礎架構
    CMS配置       :done, a1, 2025-04-12, 1d
    section 數據遷移
    資料轉換      :a2, 2025-04-13, 2d
    section 前端
    頁面開發      :a3, after a2, 5d
    section 測試
    功能測試      :a4, after a3, 2d
```

**優先級：**
1. 數據遷移（現有罷免理由格式轉換）
2. 前端頁面開發
3. 測試與優化 