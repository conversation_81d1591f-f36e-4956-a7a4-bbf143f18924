import os
import datetime
import shutil

# Get current time in Taipei timezone
now = datetime.datetime.now()
formatted_time = now.strftime("%Y-%m-%d %H:%M:%S")

# Create PRD directory if it doesn't exist
if not os.path.exists("PRD"):
    os.makedirs("PRD")

# Define file paths
source_file = "PRD/PRD-accessibility-img-alt.md"
target_file = f"PRD/PRD-{formatted_time}-圖片替代文字優化.md"

# Copy the content to the new file with timestamp
if os.path.exists(source_file):
    shutil.copy2(source_file, target_file)
    print(f"Created PRD file: {target_file}")
    
    # Optionally remove the original file
    os.remove(source_file)
    print(f"Removed temporary file: {source_file}")
else:
    print(f"Error: Source file {source_file} not found") 