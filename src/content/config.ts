import { defineCollection, z } from 'astro:content';

export const collections = {
  legislators: defineCollection({
    type: 'content',
    schema: z.object({
      name: z.string(),
      party: z.string(),
      district: z.string(),
      imageUrl: z.string().optional(),
      nicknames: z.array(z.string()).default([]),
      orderID: z.number().optional(),
    }),
  }),
  roles: defineCollection({
    type: 'content',
    schema: z.object({
      title: z.string(),
      icon: z.string().optional(),
      description: z.string().optional(),
      orderID: z.number().optional(),
    }),
  }),
  reasons: defineCollection({
    type: 'content',
    schema: z.object({
      title: z.string(),
      body: z.string().optional(),
      description: z.string().optional(),
      popularity: z.number().optional(),
      orderID: z.number().optional(),
      publishDate: z.date().optional(),
      relatedLegislators: z.array(z.string()).default([]),
      relatedRoles: z.array(z.string()).default([]),
      media: z.array(
        z.object({
          type: z.enum(['image', 'video', 'youtube']),
          url: z.string().optional(),
          youtubeId: z.string().optional(),
          caption: z.string().optional(),
        })
      ).default([]),
      tags: z.array(z.string()).default([]),
    }),
  }),
  media: defineCollection({
    type: 'content',
    schema: z.object({
      title: z.string(),
      description: z.string().optional(),
      date: z.date().optional(),
      orderID: z.number().optional(),
      youtubeId: z.string().optional(),
      image: z.string().optional(),
      source: z.string().optional(),
      tags: z.array(z.string()).default([]),
      groups: z.array(z.string()).default([]),
    }),
  }),
  memes: defineCollection({
    type: 'content',
    schema: z.object({
      title: z.string(),
      description: z.string().optional(),
      imageUrl: z.string(),
      videoUrl: z.string().optional(),
      mediaType: z.enum(['image', 'video']).optional(),
      tags: z.array(z.string()).default([]),
      legislatorNames: z.array(z.string()).default([]),
    }),
  }),
  controversies: defineCollection({
    type: 'content',
    schema: z.object({
      title: z.string(),
      content: z.string(),
      reflections: z.array(z.string()).default([]),
      tags: z.array(z.string()).default([]),
      orderID: z.number().optional(),
      legislatorName: z.string(),
    }),
  }),
}; 