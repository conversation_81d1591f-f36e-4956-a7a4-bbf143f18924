---
import '../styles/globals.css';
import SearchBox from '../components/SearchBox.astro';
import MobileSearchModal from '../components/MobileSearchModal.astro';

interface Props {
  title: string;
  description?: string;
  image?: string;
  type?: string;
}

const { 
  title, 
  description = "100個罷免的理由 - 分享35位立委的不適任理由，揭露問題，引起共鳴。",
  image = "/default-og-image.png",
  type = "website"
} = Astro.props;

// Get current URL path to determine active page
const currentPath = Astro.url.pathname;
const canonicalURL = new URL(currentPath, Astro.site || "https://recall.islandcountry.tw/").href;

// 準備 OG 圖片 URL
const ogImageURL = new URL(image, Astro.site || "https://recall.islandcountry.tw/");

// 為 OG 圖片添加防止緩存的隨機查詢參數（如果該 URL 不是 Astro 已經處理過的資產）
if (!image.includes('_astro/') && !image.includes('?v=')) {
  // 只在非 Astro 處理過的圖片並且還沒有查詢參數的情況下添加時間戳
  ogImageURL.searchParams.set('t', Date.now().toString());
}

// Function to check if a link is active
const isActive = (path: string) => {
  if (path === '/' && currentPath === '/') return true;
  if (path !== '/' && currentPath.startsWith(path)) return true;
  return false;
};

// Define active and inactive link styles
const activeLinkClass = "text-red-600 dark:text-red-500 font-medium";
const inactiveLinkClass = "text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-500";

// 設定防止緩存的 Cache-Control 標頭
Astro.response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
Astro.response.headers.set('Pragma', 'no-cache');
Astro.response.headers.set('Expires', '0');
---

<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
    
    <!-- OpenGraph 標籤 -->
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:type" content={type} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={ogImageURL.href} />
    <meta property="og:locale" content="zh_TW" />
    <meta property="og:site_name" content="100個罷免的理由" />
    
    <!-- Twitter Card 標籤 -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={ogImageURL.href} />
    
    <!-- Preload critical LCP image -->
    {currentPath === '/' && (
      <>
        <link 
          rel="preload" 
          as="image" 
          href="/images/optimized/index-roles-640.webp" 
          type="image/webp"
          fetchpriority="high"
        />
      </>
    )}
    
    <script src="/js/typeit.umd.js" is:inline></script>
  </head>
  <body class="min-h-screen bg-gray-100 dark:bg-gray-900 flex flex-col">
    <header class="bg-white dark:bg-gray-800 shadow-md fixed top-0 left-0 right-0 z-50">
      <div class="container mx-auto px-4 py-1">
        <!-- 移動版導航欄 -->
        <div class="flex flex-col md:flex-row md:items-center">
          <!-- Logo 和跑馬燈區域 -->
          <div class="flex flex-col md:flex-row md:items-center flex-grow">
            <a href="/" class="text-2xl font-bold text-red-600 dark:text-red-500 flex items-center">
              100個<span class="text-recall">罷免</span>的理由
            </a>
            <div class="mt-2 md:mt-0 md:ml-2 flex-grow max-w-xl">
              <span id="dynamic-slogan" class="inline-block text-sm font-mono font-normal text-white dark:text-gray-900 bg-black dark:bg-gray-100 px-2 py-1 rounded-sm border-l-2 border-red-500 overflow-hidden whitespace-nowrap min-h-[1.5rem]"></span>
            </div>
          </div>

          <!-- 桌面版導航選單 -->
          <nav class="hidden md:flex space-x-4">
            <SearchBox />
            <a href="/legislators" class={isActive('/legislators') ? activeLinkClass : inactiveLinkClass}>立委列表</a>
            <a href="/happy-recall" class={isActive('/happy-recall') ? activeLinkClass : inactiveLinkClass}>歡樂罷</a>
            <a href="/about" class={isActive('/about') ? activeLinkClass : inactiveLinkClass}>關於本站</a>
          </nav>

          <!-- 漢堡選單按鈕 -->
          <button id="mobile-menu-button" class="md:hidden text-gray-700 dark:text-gray-300 absolute top-3 right-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>
        </div>

        <!-- 移動版選單 (預設隱藏) -->
        <div id="mobile-menu" class="hidden md:hidden mt-4 pb-2">
          <nav class="flex flex-col space-y-2">
            <a href="/legislators" class={isActive('/legislators') ? `${activeLinkClass} py-2 px-4 rounded-lg bg-gray-100 dark:bg-gray-700` : `${inactiveLinkClass} py-2 px-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700`}>立委列表</a>
            <a href="/happy-recall" class={isActive('/happy-recall') ? `${activeLinkClass} py-2 px-4 rounded-lg bg-gray-100 dark:bg-gray-700` : `${inactiveLinkClass} py-2 px-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700`}>歡樂罷</a>
            <a href="/about" class={isActive('/about') ? `${activeLinkClass} py-2 px-4 rounded-lg bg-gray-100 dark:bg-gray-700` : `${inactiveLinkClass} py-2 px-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700`}>關於本站</a>
            <div class="py-2 px-4">
              <button id="mobile-search-toggle" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-500 bg-gray-100 dark:bg-gray-700 py-2 px-3 rounded-lg w-full justify-center transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                </svg>
                搜尋罷免理由
              </button>
            </div>
          </nav>
        </div>
      </div>
    </header>
    
    <main class="container mx-auto px-4 mt-24 md:mt-16 flex-grow">
      <slot />
    </main>

    <footer class="bg-black text-white py-8 sm:py-12 mt-auto">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-3 md:grid-cols-4 gap-8">
          
          <!-- 網站資訊 -->
          <div class="col-span-3 md:col-span-1">
            <a href="https://islandcountry.tw/" target="_blank" class="text-2xl font-bold text-red-500 inline-flex items-center">
              <img src="/logo-island-country.webp" alt="" class="w-8 mr-2 object-contain" aria-hidden="true" />
              島國物語
            </a>
            <p class="mt-3 text-gray-200 text-sm">
              分享不適任立委的問題言行，提供公民監督的平台，促進民主社會的健全發展。
            </p>
          </div>
          
          <!-- 網站導航 -->
          <div>
            <h3 class="font-semibold text-lg mb-3">網站導航</h3>
            <ul class="space-y-2 text-gray-200">
              <li><a href="/legislators" class="hover:text-red-500 transition-colors">立委列表</a></li>
              <li><a href="/happy-recall" class="hover:text-red-500 transition-colors">歡樂罷</a></li>
              <li><a href="/about" class="hover:text-red-500 transition-colors">關於本站</a></li>
            </ul>
          </div>
          
          <!-- 資源連結 -->
          <div>
            <h3 class="font-semibold text-lg mb-3">參考資源</h3>
            <ul class="space-y-2 text-gray-200">
              <li><a href="https://law.moj.gov.tw/LawClass/LawAll.aspx?pcode=D0020010" class="hover:text-red-500 transition-colors" target="_blank">選舉罷免法</a></li>
              <li><a href="https://www.ly.gov.tw/" class="hover:text-red-500 transition-colors" target="_blank">立法院</a></li>
            </ul>
          </div>
          
          <!-- 法律聲明 -->
          <div>
            <h3 class="font-semibold text-lg mb-3">法律聲明</h3>
            <ul class="space-y-2 text-gray-200">
              <li><a href="/about" class="hover:text-red-500 transition-colors">隱私權政策</a></li>
              <li><a href="/about" class="hover:text-red-500 transition-colors">免責聲明</a></li>
              <li><a href="/about" class="hover:text-red-500 transition-colors">資料來源</a></li>
            </ul>
          </div>
        </div>
        
        <!-- 分隔線 -->
        <div class="border-t border-gray-700 my-6 sm:my-8"></div>
        
        <!-- 版權與社群媒體 -->
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-300 text-sm text-center md:text-left mb-4 md:mb-0">
            <span>© 2025《100個罷免的理由》</span>
            <span class="block md:inline">沉默無法改變未來，一起為更好的民主發聲</span>
          </p>
          
          <div class="flex space-x-6">
            <a href="https://www.youtube.com/@IslandCountryTw" target="_blank" class="text-gray-200 hover:text-white transition-colors" aria-label="島國物語 YouTube 頻道">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-youtube" viewBox="0 0 16 16" aria-hidden="true">
                <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408L6.4 5.209z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </footer>
    
    <!-- 行動版搜尋模態框 -->
    <MobileSearchModal />
    
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // 跑馬燈功能
        const slogans = [
          '「這 35 位立委做了什麼事，你知道嗎？」',
          '「你不生氣，是因為你還沒看這個網站」',
          '「他們爭論的是政策，還是人氣？」',
          '「真相擺在這，你還能忍多久？」',
          '「嘴上喊民主，行動卻在幫中共鋪路」',
          '「這些不是民意代表，是戲劇演員兼滋事份子」',
          '「沒有討論不是民主！」',
          '「這不是立法院，是中共人大翻版」',
          '「休會前三天審三千案，這是審預算還是變魔術？」',
          '「藍白只是假裝在立法院開會」',
          '「全台最貴的秀場：一場 4000 萬的政客表演」',
          '「協商是演戲，法案是瞬間決定的最高機密」'
        ];
        
        function getRandomSlogan() {
          const randomIndex = Math.floor(Math.random() * slogans.length);
          return slogans[randomIndex];
        }
        
        function startTyping() {
          // @ts-ignore
          const instance = new TypeIt('#dynamic-slogan', {
            speed: 50,
            startDelay: 900,
            cursor: true,
            // @ts-ignore
            afterComplete: async (instance) => {
              await new Promise(resolve => setTimeout(resolve, 3000));
              instance.reset();
              startTyping();
            }
          })
          .type(getRandomSlogan())
          .go();
        }
        
        startTyping();

        // 漢堡選單功能
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (mobileMenuButton && mobileMenu) {
          mobileMenuButton.addEventListener('click', () => {
            // 切換選單顯示/隱藏
            mobileMenu.classList.toggle('hidden');
            
            // 更新按鈕圖標
            const svg = mobileMenuButton.querySelector('svg');
            if (svg) {
              if (mobileMenu.classList.contains('hidden')) {
                svg.innerHTML = `
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                `;
              } else {
                svg.innerHTML = `
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                `;
              }
            }
          });
          
          // 點擊頁面任意處關閉選單
          document.addEventListener('click', (event) => {
            // 如果選單是開啟的且點擊的不是選單本身和選單按鈕
            if (!mobileMenu.classList.contains('hidden') && 
                !mobileMenu.contains(event.target as Node) && 
                !mobileMenuButton.contains(event.target as Node)) {
              // 關閉選單
              mobileMenu.classList.add('hidden');
              
              // 更新按鈕圖標為漢堡圖示
              const svg = mobileMenuButton.querySelector('svg');
              if (svg) {
                svg.innerHTML = `
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                `;
              }
            }
          });
        }
        
        // 連結移動版搜尋按鈕到搜尋功能
        const mobileSearchToggleBtn = document.getElementById('mobile-search-toggle');
        if (mobileSearchToggleBtn) {
          console.log('找到移動版搜尋按鈕，即將添加點擊事件');
          
          mobileSearchToggleBtn.addEventListener('click', async (e) => {
            // 防止事件冒泡，避免同時觸發多個事件
            e.preventDefault();
            e.stopPropagation();
            
            console.log('移動版搜尋按鈕被點擊');
            
            // 關閉移動選單
            if (mobileMenu) {
              mobileMenu.classList.add('hidden');
              console.log('已關閉移動選單');
              
              // 更新漢堡按鈕圖標
              const svg = mobileMenuButton?.querySelector('svg');
              if (svg) {
                svg.innerHTML = `
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                `;
              }
            }
            
            // 使用新的行動版搜尋模態框
            const windowAny = window as any;
            console.log('檢查openMobileSearchModal函數是否存在:', typeof windowAny.openMobileSearchModal === 'function');
            
            try {
              if (typeof windowAny.openMobileSearchModal === 'function') {
                console.log('使用專用行動版搜尋模態框');
                windowAny.openMobileSearchModal();
              } else {
                console.error('找不到openMobileSearchModal函數，行動版搜尋可能無法正常工作');
                
                // 嘗試找尋函數
                console.log('全局範圍中的所有函數:', 
                  Object.keys(windowAny).filter(key => typeof windowAny[key] === 'function'));
                
                alert('搜尋功能暫時無法使用，請稍後再試');
              }
            } catch (error) {
              console.error('調用搜尋模態框時出錯:', error);
            }
          });
        } else {
          console.error('找不到移動版搜尋按鈕!');
        }
      });
    </script>
  </body>
</html> 