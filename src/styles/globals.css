@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-red-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen;
  }
  
  ::selection {
    @apply bg-red-500/20;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

@layer utilities {
  .text-gradient {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-red-700 via-red-500 to-orange-500 dark:from-red-500 dark:via-red-300 dark:to-orange-300;
  }
  
  .text-recall {
    color: #1600ac;
  }
  
  .py-8 {
    padding-top: 1rem;
    padding-bottom: 2rem;
  }
} 