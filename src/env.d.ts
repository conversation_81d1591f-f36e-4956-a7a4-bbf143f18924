/// <reference path="../.astro/types.d.ts" />
/// <reference types="astro/client" />

declare module '*.astro' {
  const component: import('astro').AstroComponent;
  export default component;
}

declare module 'astro:content' {
  interface Render {
    '.astro': Promise<{
      Content: import('astro').AstroComponent;
    }>;
  }

  type Media = {
    title: string;
    description?: string;
    date: Date;
    orderID?: number;
    youtubeId?: string;
    image?: string;
    source?: string;
    tags: string[];
    groups: string[];
  };

  interface DataEntryMap {
    'media': {
      collection: 'media';
      data: Media;
    };
  }
}

declare module 'astro' {
  export interface AstroGlobal {
    props: Record<string, any>;
  }
} 