---
interface Props {
  className?: string;
}

const { className = "" } = Astro.props;
---

<div class={`max-w-7xl mx-auto text-center ${className}`}>
  <a href="/legislators" class="inline-block px-12 py-4 text-lg font-medium text-white bg-gradient-to-r from-red-600 to-orange-500 rounded-xl shadow-lg hover:from-red-700 hover:to-orange-600 transition-all duration-300 transform hover:scale-105">
    <div class="flex flex-col sm:flex-row items-center justify-center">
      <div class="flex items-center sm:hidden">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <span>立委列表</span>
      </div>
      
      <div class="sm:hidden mt-1">
        <span>來看看，你選的那位，有多離譜？</span>
      </div>
      
      <!-- Desktop version -->
      <div class="hidden sm:flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <span class="font-bold">立委列表</span>
      </div>
      <span class="hidden sm:inline mx-1">-</span>
      <div class="hidden sm:flex">
        <span>來看看，你選的那位，有多離譜？</span>
      </div>
    </div>
  </a>
</div> 