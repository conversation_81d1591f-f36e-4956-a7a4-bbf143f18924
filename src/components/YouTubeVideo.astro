---
interface Props {
  videoId?: string;
  videoUrl?: string;
  title: string;
  description?: string;
  className?: string;
  enableModal?: boolean;
  mediaType?: 'youtube' | 'video';
}

const { 
  videoId, 
  videoUrl,
  title, 
  description, 
  className = "", 
  enableModal = true,
  mediaType = 'youtube'
} = Astro.props;

// 确定媒体类型
const isYouTube = mediaType === 'youtube' || (videoId && !videoUrl && mediaType !== 'video');
const isVideo = mediaType === 'video' || (videoUrl && videoUrl.match(/\.(mp4|webm|ogg)$/i) && mediaType !== 'youtube');
---

<div class={`video-container ${className}`} data-video-id={videoId} data-video-url={videoUrl}>
  <div class="aspect-video rounded-lg overflow-hidden shadow-lg relative">
    {isYouTube && (
      <iframe
        src={`https://www.youtube.com/embed/${videoId}`}
        title={title}
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowfullscreen
        class="w-full h-full"
        loading="lazy"
      ></iframe>
    )}
    
    {isVideo && (
      <video 
        src={videoUrl} 
        title={title}
        controls
        preload="metadata"
        class="w-full h-full object-cover"
      >
        您的瀏覽器不支援影片播放
      </video>
    )}
    
    {enableModal && (
      <div class="video-overlay absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4 cursor-pointer z-10">
        <span class="text-white px-4 py-2 rounded-full bg-gray-900/60 text-sm flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          點擊放大
        </span>
      </div>
    )}
  </div>
  {description && (
    <div class="mt-2 text-sm text-gray-600 dark:text-gray-400 min-h-[2.5rem] overflow-hidden">
      <span class="line-clamp-2">{description}</span>
    </div>
  )}
</div> 

<style>
  /* 移除阻止iframe捕獲點擊事件的樣式 */
  /* .aspect-video iframe {
    pointer-events: none;
  } */
</style>