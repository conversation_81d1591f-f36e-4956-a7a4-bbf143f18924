---
import '../styles/globals.css';

interface Props {
  id: string;
  name: string;
  district: string;
  imageUrl: string;
  topReason: string;
  nickname?: string; // Add optional nickname property
  tinaCmsImageUrl?: string; // Add optional tinaCmsImageUrl property
}

const { id, name, imageUrl, district, topReason, nickname, tinaCmsImageUrl } = Astro.props;

// 使用默認圖片替代遺失的圖片
const defaultImage = '/favicon.svg';

// Generate district label
const districtLabel = district.includes('選區') ? district : `${district}`;

// Use TinaCMS image URL if available, otherwise fall back to legislator.ts imageUrl
const displayImageUrl = tinaCmsImageUrl || imageUrl;
---

<div class="group relative overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-1 dark:bg-gray-800 dark:border-gray-700 backdrop-blur-sm">
  <a href={`/legislator/${name}`} class="block h-full">
    <!-- Square image container with overlay gradient -->
    <div class="relative aspect-square overflow-hidden bg-gradient-to-br from-blue-600 to-indigo-800 dark:from-blue-900 dark:to-indigo-950">
      <img 
        src={displayImageUrl} 
        alt={`${name}的照片`}
        class="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110 filter brightness-90 contrast-110 saturate-75"
        onerror="this.src='/favicon.svg'; this.onerror=null;"
      />
      
      <!-- District label at top -->
      <div class="absolute top-0 left-0 right-0 py-1.5 sm:py-2 px-2 sm:px-3 bg-gradient-to-b from-black/80 to-transparent">
        <span class="text-[10px] sm:text-xs font-bold text-white tracking-wide">{districtLabel}</span>
      </div>
      
      <!-- Dark overlay gradient for better text readability -->
      <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-100"></div>
      
      <!-- Legislator name and nickname at bottom of image -->
      <div class="absolute bottom-0 left-0 right-0 p-2 sm:p-3 text-white">
        <h3 class="text-lg sm:text-xl font-bold leading-tight group-hover:text-red-300 transition-colors duration-300">{name}</h3>
        {nickname && (
          <p class="text-xs sm:text-sm font-medium text-red-300 mt-0.5 sm:mt-1">「{nickname}」</p>
        )}
      </div>
    </div>
    
    <!-- Recall reason section -->
    <div class="p-3 sm:p-4">
      <div class="border-l-4 border-red-500 pl-2 sm:pl-3">
        <p class="font-medium text-xs sm:text-sm text-gray-500 dark:text-gray-400">罷免理由</p>
        <p class="mt-1 text-gray-800 dark:text-gray-200 line-clamp-3 text-xs sm:text-sm group-hover:text-red-700 dark:group-hover:text-red-300 transition-colors duration-300">{topReason}</p>
      </div>

      <div class="mt-3 sm:mt-4 flex justify-end items-center">
        <div class="inline-flex items-center px-2 sm:px-3 py-1 rounded-full bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 text-[10px] sm:text-xs font-medium group-hover:bg-red-100 dark:group-hover:bg-red-800/40 transition-colors duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          詳情
        </div>
      </div>
    </div>
  </a>
</div> 