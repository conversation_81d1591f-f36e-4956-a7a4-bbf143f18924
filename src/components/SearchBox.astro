---
// SearchBox.astro - 全站搜尋功能
---

<div class="relative md:ml-4" id="search-container">
  <div class="flex items-center">
    <!-- 搜尋框 -->
    <div class="relative flex items-center">
      <input
        type="text"
        id="search-toggle-input"
        class="w-36 h-8 pl-8 pr-14 text-sm border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 hidden md:block"
        placeholder="搜尋..."
        readonly
      />
      <!-- 搜尋圖標 -->
      <button id="search-toggle" class="absolute left-2 text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-500 focus:outline-none">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
        </svg>
        <span class="sr-only">搜尋</span>
      </button>
      <!-- 快捷鍵顯示 -->
      <div class="absolute right-2 hidden md:flex items-center text-xs text-gray-400 dark:text-gray-500">
        <kbd class="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600 mr-0.5">⌘</kbd>
        <kbd class="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600">K</kbd>
      </div>
      <!-- 只在移動設備上顯示的按鈕 -->
      <button id="search-toggle-mobile" class="md:hidden text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-500 focus:outline-none">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
        </svg>
        <span class="sr-only">搜尋</span>
      </button>
    </div>
  </div>
  
  <div id="search-modal" class="fixed inset-0 z-50 flex items-center justify-center p-4 hidden">
    <div class="absolute inset-0 bg-black bg-opacity-50" id="search-overlay"></div>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl relative z-10">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">搜尋罷免理由</h3>
        <div class="flex items-center">
          <button id="search-close" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
            <span class="sr-only">關閉</span>
          </button>
        </div>
      </div>
      <div class="p-4">
        <div class="mb-4 relative">
          <input
            type="text"
            id="search-input"
            class="w-full p-3 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-red-500 focus:border-red-500"
            placeholder="輸入關鍵字搜尋罷免理由..."
            autocomplete="off"
          />
          <div class="absolute right-3 top-3 text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
            </svg>
          </div>
        </div>
        <div id="search-results" class="max-h-[60vh] overflow-y-auto"></div>
        <div id="search-no-results" class="py-6 text-center text-gray-500 dark:text-gray-400 hidden">
          找不到符合的內容
        </div>
        <div id="search-loading" class="py-6 text-center text-gray-500 dark:text-gray-400 hidden">
          <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
          <span class="ml-2">搜尋中...</span>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  import FlexSearch from 'flexsearch';
  
  // 初始化搜尋引擎  
  let searchIndex: any = null;
  let reasonsData: any[] = [];
  
  // 搜尋歷史記錄的鍵名
  const SEARCH_HISTORY_KEY = 'recall-reasons-search-history';
  
  // 判斷是否為開發環境
  const isDevelopment = window.location.hostname === 'localhost' || 
                        window.location.hostname === '127.0.0.1';
  
  // 清除所有搜尋相關緩存
  function clearSearchCache() {
    // 清除搜尋索引
    searchIndex = null;
    reasonsData = [];
    
    // 清除歷史記錄
    try {
      localStorage.removeItem(SEARCH_HISTORY_KEY);
    } catch (error) {
      console.error('清除搜尋歷史記錄失敗:', error);
    }
    
    // 清除API緩存 (使用帶時間戳的參數強制重新獲取)
    fetch('/api/search-data.json?nocache=' + Date.now(), { 
      headers: { 'Cache-Control': 'no-cache, no-store' } 
    })
    .then(response => {
      console.log('搜尋資料已重新獲取');
    })
    .catch(error => {
      console.error('重新獲取搜尋資料失敗:', error);
    });
    
    console.log('所有搜尋緩存已清除');
    
    // 如果搜尋視窗是開啟的，則關閉再重開
    const searchModal = document.getElementById('search-modal');
    if (searchModal && !searchModal.classList.contains('hidden')) {
      closeSearchModal();
      setTimeout(() => {
        openSearchModal();
      }, 300);
    }
  }
  
  // 保存搜尋記錄
  function saveSearchHistory(query: string) {
    if (!query) return;
    
    try {
      // 保存最近5次搜尋
      const history = getSearchHistory();
      
      // 如果已經存在相同的搜尋詞，先移除
      const filteredHistory = history.filter(item => item !== query);
      
      // 將新的搜尋詞添加到開頭
      filteredHistory.unshift(query);
      
      // 只保留最近5次搜尋
      const newHistory = filteredHistory.slice(0, 5);
      
      localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory));
    } catch (error) {
      console.error('保存搜尋記錄失敗:', error);
    }
  }
  
  // 獲取搜尋記錄
  function getSearchHistory(): string[] {
    try {
      const historyJson = localStorage.getItem(SEARCH_HISTORY_KEY);
      if (historyJson) {
        return JSON.parse(historyJson);
      }
    } catch (error) {
      console.error('讀取搜尋記錄失敗:', error);
    }
    return [];
  }
  
  // 獲取最近一次的搜尋記錄
  function getLastSearchQuery(): string {
    const history = getSearchHistory();
    return history.length > 0 ? history[0] : '';
  }
  
  // 懶加載搜尋索引
  async function initializeSearch() {
    if (searchIndex) return searchIndex; // 如果已初始化，直接返回索引
    
    try {
      // 顯示 loading 狀態
      const loadingEl = document.getElementById('search-loading');
      if (loadingEl) loadingEl.classList.remove('hidden');
      
      // 取得所有罷免理由資料
      const response = await fetch('/api/search-data.json');
      reasonsData = await response.json();
      
      // 創建 FlexSearch 索引
      searchIndex = new FlexSearch.Document({
        document: {
          id: "id",
          index: ["title", "body"],
          store: ["title", "body", "slug", "excerpt"]
        },
        encode: function(str) {
          // 自定義編碼函數，處理中文分詞
          return str.split(/\s+/)
            .filter(word => word.length > 0)
            .map(word => word.toLowerCase());
        },
        tokenize: "full",
        resolution: 9,
        cache: true
      });
      
      // 增強標題搜尋
      const enhancedReasons = reasonsData.map(reason => ({
        ...reason,
        // 複製標題加到內文，增加標題關鍵詞的權重
        body: `${reason.title} ${reason.title} ${reason.body}`
      }));
      
      // 將資料加入索引
      enhancedReasons.forEach(reason => {
        searchIndex.add(reason);
      });
      
      console.log('搜尋索引初始化完成');
      return searchIndex;
      
    } catch (error) {
      console.error("搜尋初始化錯誤:", error);
      return null;
    } finally {
      // 隱藏 loading 狀態
      const loadingEl = document.getElementById('search-loading');
      if (loadingEl) loadingEl.classList.add('hidden');
    }
  }
  
  // 執行搜尋
  function performSearch(query: string) {
    if (!searchIndex) return [];
    
    // 先顯示 loading
    const loadingEl = document.getElementById('search-loading');
    const resultsEl = document.getElementById('search-results');
    const noResultsEl = document.getElementById('search-no-results');
    
    if (loadingEl) loadingEl.classList.remove('hidden');
    if (resultsEl) resultsEl.innerHTML = '';
    if (noResultsEl) noResultsEl.classList.add('hidden');
    
    // 直接使用整體搜尋，不再分開搜尋標題和內文
    const searchResults = searchIndex.search(query, { limit: 20 });
    
    // 合併所有結果並去重
    const allIds = new Set();
    const combinedResults: any[] = [];
    
    // 處理搜尋結果
    searchResults.forEach((result: any) => {
      result.result.forEach((id: string) => {
        if (!allIds.has(id)) {
          allIds.add(id);
          const reason = reasonsData.find(item => item.id === id);
          if (reason) {
            // 判斷是否匹配標題
            const isTitleMatch = reason.title && reason.title.toLowerCase().includes(query.toLowerCase());
            combinedResults.push({
              ...reason,
              matchType: isTitleMatch ? 'title' : 'body'
            });
          }
        }
      });
    });
    
    // 將標題匹配的項目排在前面
    combinedResults.sort((a, b) => {
      // 首先按匹配類型排序
      if (a.matchType === 'title' && b.matchType !== 'title') return -1;
      if (a.matchType !== 'title' && b.matchType === 'title') return 1;
      
      // 然後按熱門度排序
      return (b.popularity || 0) - (a.popularity || 0);
    });
    
    // 隱藏 loading
    if (loadingEl) loadingEl.classList.add('hidden');
    
    // 如果沒有結果，顯示無結果訊息
    if (combinedResults.length === 0 && noResultsEl) {
      noResultsEl.classList.remove('hidden');
    }
    
    return combinedResults;
  }
  
  // 渲染搜尋結果
  function renderSearchResults(results: any[]) {
    const resultsContainer = document.getElementById('search-results');
    if (!resultsContainer) return;
    
    resultsContainer.innerHTML = '';
    
    if (results.length === 0) return;
    
    const resultsList = document.createElement('ul');
    resultsList.className = 'divide-y divide-gray-200 dark:divide-gray-700';
    resultsList.setAttribute('role', 'listbox');
    resultsList.setAttribute('id', 'search-results-list');
    resultsList.setAttribute('tabindex', '-1');
    
    results.forEach((item, index) => {
      const li = document.createElement('li');
      li.className = 'py-4 hover:bg-gray-50 dark:hover:bg-gray-700 px-3 rounded transition';
      li.setAttribute('role', 'option');
      li.setAttribute('id', `search-result-${index}`);
      li.setAttribute('data-index', index.toString());
      if (index === 0) {
        li.classList.add('search-result-active');
        li.setAttribute('aria-selected', 'true');
      } else {
        li.setAttribute('aria-selected', 'false');
      }
      
      const title = document.createElement('h4');
      title.className = 'text-lg font-semibold text-gray-900 dark:text-white mb-1';
      title.textContent = item.title || '無標題';
      
      // 處理內容摘要，確保不含錯誤代碼
      let excerptText = item.excerpt || '';
      if (excerptText.includes('__vite_ssr_import') || excerptText.includes('AstroError')) {
        excerptText = item.title || '無摘要';
      }
      
      const excerpt = document.createElement('p');
      excerpt.className = 'text-sm text-gray-600 dark:text-gray-300 line-clamp-2';
      excerpt.textContent = excerptText;
      
      const link = document.createElement('a');
      link.href = `/reasons/${item.slug}`;
      link.className = 'block';
      link.appendChild(title);
      link.appendChild(excerpt);
      
      const matchBadge = document.createElement('span');
      matchBadge.className = 'inline-block px-2 py-1 text-xs rounded bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 mt-2';
      matchBadge.textContent = item.matchType === 'title' ? '標題符合' : '內容符合';
      
      li.appendChild(link);
      li.appendChild(matchBadge);
      
      // 點擊項目時直接跳轉
      li.addEventListener('click', (e) => {
        window.location.href = link.href;
      });
      
      // 鼠標懸停時設置為活動項目
      li.addEventListener('mouseenter', () => {
        setActiveResult(index);
      });
      
      resultsList.appendChild(li);
    });
    
    resultsContainer.appendChild(resultsList);
    
    // 設置初始選中項
    if (results.length > 0) {
      setActiveResult(0);
    }
  }
  
  // 追蹤當前選中的結果項索引
  let activeResultIndex = 0;
  
  // 設置活動的搜尋結果項目
  function setActiveResult(index: number) {
    const resultsList = document.getElementById('search-results-list');
    if (!resultsList) return;
    
    // 移除所有項目的活動狀態
    const items = resultsList.querySelectorAll('li[role="option"]');
    items.forEach((item) => {
      item.classList.remove('search-result-active');
      item.setAttribute('aria-selected', 'false');
    });
    
    // 設置新的活動項目
    activeResultIndex = index;
    const activeItem = document.getElementById(`search-result-${index}`);
    if (activeItem) {
      activeItem.classList.add('search-result-active');
      activeItem.setAttribute('aria-selected', 'true');
      activeItem.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
    }
  }
  
  // 移動選中的項目 (上下導航)
  function navigateResults(direction: 'up' | 'down') {
    const resultsList = document.getElementById('search-results-list');
    if (!resultsList) return;
    
    const items = resultsList.querySelectorAll('li[role="option"]');
    if (items.length === 0) return;
    
    let newIndex = activeResultIndex;
    
    if (direction === 'up') {
      newIndex = (newIndex - 1 + items.length) % items.length;
    } else {
      newIndex = (newIndex + 1) % items.length;
    }
    
    setActiveResult(newIndex);
  }
  
  // 按Enter選擇當前項目
  function selectActiveResult() {
    console.log('選擇當前項目, 索引:', activeResultIndex);
    const activeItem = document.getElementById(`search-result-${activeResultIndex}`);
    if (activeItem) {
      const link = activeItem.querySelector('a');
      if (link) {
        const href = link.getAttribute('href');
        console.log('跳轉到頁面:', href);
        if (href) {
          window.location.href = href;
        }
      }
    }
  }
  
  // 點擊搜尋按鈕開啟搜尋框
  const searchToggleBtn = document.getElementById('search-toggle');
  const searchToggleInput = document.getElementById('search-toggle-input');
  const mobileSearchToggleBtn = document.getElementById('search-toggle-mobile');
  
  async function openSearchModal() {
    console.log('openSearchModal 函數被調用');
    const searchModal = document.getElementById('search-modal');
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    
    if (searchModal) {
      console.log('顯示搜尋模態框');
      
      // 確保模態框的樣式正確
      searchModal.style.display = 'flex';
      searchModal.classList.remove('hidden');
      document.body.classList.add('overflow-hidden');
      
      // 檢查模態框的計算樣式
      const computedStyle = window.getComputedStyle(searchModal);
      console.log('搜尋模態框實際顯示狀態:', computedStyle.display, '可見性:', computedStyle.visibility, 'z-index:', computedStyle.zIndex);
      
      // 聚焦到輸入框
      if (searchInput) {
        searchInput.focus();
        console.log('已聚焦到搜尋輸入框');
      } else {
        console.error('找不到搜尋輸入框元素!');
      }
      
      try {
        // 顯示loading狀態
        const loadingEl = document.getElementById('search-loading');
        if (loadingEl) {
          loadingEl.classList.remove('hidden');
          console.log('已顯示loading狀態');
        }
        
        // 確保索引初始化完成
        const searchIndex = await initializeSearch();
        console.log('搜尋索引初始化狀態:', !!searchIndex);
        
        // 恢復上一次的搜尋記錄
        if (searchInput) {
          const lastQuery = getLastSearchQuery();
          if (lastQuery) {
            searchInput.value = lastQuery;
            console.log('已恢復上次搜尋:', lastQuery);
            
            // 確保索引已初始化後再執行搜尋
            const results = performSearch(lastQuery);
            console.log('搜尋結果筆數:', results.length);
            renderSearchResults(results);
            
            // 將游標移動到文本末尾
            searchInput.setSelectionRange(searchInput.value.length, searchInput.value.length);
          }
        }
      } catch (error) {
        console.error('開啟搜尋框時出錯:', error);
      
        // 隱藏loading狀態
        const loadingEl = document.getElementById('search-loading');
        if (loadingEl) loadingEl.classList.add('hidden');
      }
    } else {
      console.error('找不到搜尋模態框元素!');
      
      // 檢查DOM中是否有這個元素
      const allModals = document.querySelectorAll('[id]');
      console.log('所有帶ID的元素:', Array.from(allModals).map(el => el.id));
    }
  }
  
  if (searchToggleBtn) {
    searchToggleBtn.addEventListener('click', openSearchModal);
  }
  
  if (searchToggleInput) {
    searchToggleInput.addEventListener('click', openSearchModal);
  }
  
  if (mobileSearchToggleBtn) {
    mobileSearchToggleBtn.addEventListener('click', openSearchModal);
  }
  
  // 點擊關閉按鈕或遮罩關閉搜尋框
  const closeBtn = document.getElementById('search-close');
  const overlay = document.getElementById('search-overlay');
  
  if (closeBtn) closeBtn.addEventListener('click', closeSearchModal);
  if (overlay) overlay.addEventListener('click', closeSearchModal);
  
  function closeSearchModal() {
    console.log('closeSearchModal 函數被調用');
    const searchModal = document.getElementById('search-modal');
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    const resultsEl = document.getElementById('search-results');
    const noResultsEl = document.getElementById('search-no-results');
    
    if (searchModal) {
      searchModal.style.display = 'none';
      searchModal.classList.add('hidden');
      console.log('已隱藏搜尋模態框');
      document.body.classList.remove('overflow-hidden');
      if (searchInput) searchInput.value = '';
      if (resultsEl) resultsEl.innerHTML = '';
      if (noResultsEl) noResultsEl.classList.add('hidden');
    } else {
      console.error('找不到搜尋模態框元素!');
    }
  }
  
  // 全局鍵盤事件處理
  document.addEventListener('keydown', (e) => {
    const searchModal = document.getElementById('search-modal');
    const isSearchModalOpen = searchModal && !searchModal.classList.contains('hidden');
    
    // Command/Ctrl + K 開啟搜尋框
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();
      
      if (isSearchModalOpen) {
        closeSearchModal();
      } else {
        openSearchModal();
      }
      return;
    }
    
    // 只處理搜尋框開啟時的鍵盤事件
    if (isSearchModalOpen) {
      if (e.key === 'Escape') {
        closeSearchModal();
      } else if (e.key === 'ArrowDown') {
        e.preventDefault(); // 防止頁面滾動
        navigateResults('down');
      } else if (e.key === 'ArrowUp') {
        e.preventDefault(); // 防止頁面滾動
        navigateResults('up');
      } else if (e.key === 'Enter') {
        // 現在無論焦點在哪，都處理Enter鍵
        const resultsList = document.getElementById('search-results-list');
        if (resultsList && resultsList.children.length > 0) {
          e.preventDefault();
          selectActiveResult();
        }
      }
    }
  });
  
  // 輸入框監聽
  const searchInput = document.getElementById('search-input');
  if (searchInput) {
    searchInput.addEventListener('input', (e) => {
      const target = e.target as HTMLInputElement;
      const query = target.value.trim();
      
      const resultsEl = document.getElementById('search-results');
      const noResultsEl = document.getElementById('search-no-results');
      
      if (query.length < 1) {
        if (resultsEl) resultsEl.innerHTML = '';
        if (noResultsEl) noResultsEl.classList.add('hidden');
        return;
      }
      
      // 執行搜尋
      const results = performSearch(query);
      renderSearchResults(results);
      
      // 重置选中索引
      activeResultIndex = 0;
      
      // 保存搜尋記錄
      saveSearchHistory(query);
    });
    
    // 監聽搜尋輸入框的按鍵事件
    searchInput.addEventListener('keydown', (e) => {
      // 阻止上下鍵在輸入框內的默認行為（防止游標跳到行首/行尾）
      if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
        e.preventDefault();
      }
      
      // 按Enter鍵且有結果項目時，選擇當前項目
      if (e.key === 'Enter') {
        const resultsList = document.getElementById('search-results-list');
        if (resultsList && resultsList.children.length > 0) {
          e.preventDefault(); // 防止表單提交
          selectActiveResult();
        }
      }
    });
  }
  
  // 將搜尋功能立即暴露到全局範圍，不等待DOMContentLoaded
  console.log('立即暴露搜尋功能到全局範圍');
  const windowAny = window as any;
  windowAny.initializeSearch = initializeSearch;
  windowAny.performSearch = performSearch;
  windowAny.renderSearchResults = renderSearchResults;
  windowAny.getLastSearchQuery = getLastSearchQuery;
  windowAny.openSearchModal = openSearchModal;
  windowAny.closeSearchModal = closeSearchModal;
  
  // 初始化頁面時額外處理
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded - 再次確認搜尋功能已暴露');
    
    // 清除緩存功能已移除
    
    // 確保搜尋輸入框在點擊時開啟搜尋模態框
    const searchToggleInput = document.getElementById('search-toggle-input');
    if (searchToggleInput) {
      searchToggleInput.addEventListener('click', function(e) {
        e.preventDefault();
        openSearchModal();
      });
    }
  });
</script>

<style is:global>
  /* 選中結果項的樣式 */
  .search-result-active {
    background-color: rgba(239, 68, 68, 0.1); /* 淺紅色背景 */
    border-left: 3px solid rgb(239, 68, 68); /* 紅色左邊框 */
  }
  
  /* 暗模式下的樣式 */
  .dark .search-result-active {
    background-color: rgba(239, 68, 68, 0.2);
  }
  
  /* 確保搜尋模態框在行動裝置上正確顯示 */
  #search-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  #search-modal.hidden {
    display: none !important;
  }
  
  /* 確保搜尋模態框內容區域在行動裝置上完全可見 */
  #search-modal > div:last-child {
    width: 100%;
    max-width: 90vw;
    margin: 0 auto;
    max-height: 80vh;
    overflow-y: auto;
  }
  
  @media (min-width: 768px) {
    #search-modal > div:last-child {
      max-width: 640px;
    }
  }
</style> 