---
import YouTubeVideo from './YouTubeVideo.astro';

interface VideoItem {
  id: string;
  title: string;
  youtubeId?: string;
  videoUrl?: string;
  description?: string;
  date: string;
  tags: string[];
}

interface Props {
  videos: VideoItem[];
  formatDate?: (dateString: string) => string;
  carouselId?: string;
  itemsPerView?: number;
}

const { 
  videos, 
  formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('zh-TW'),
  carouselId = `carousel-${Math.random().toString(36).substring(2, 9)}`,
  itemsPerView = 2
} = Astro.props;
---

<div class="video-carousel relative" data-carousel-id={carouselId}>
  <!-- Carousel container -->
  <div class="video-carousel-container overflow-hidden">
    <div class={`video-carousel-inner flex transition-transform duration-500 ease-in-out`} id={`${carouselId}-inner`}>
      {videos.map((video: VideoItem) => (
        <div class={`video-carousel-item flex-shrink-0 px-3 carousel-item-${itemsPerView}`}>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300 h-full">
            <YouTubeVideo 
              videoId={video.youtubeId} 
              videoUrl={video.videoUrl}
              title={video.title}
              mediaType={video.youtubeId ? 'youtube' : 'video'}
            />
            <div class="p-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">{video.title}</h3>
              {video.description && (
                <p class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-3">{video.description}</p>
              )}
              <div class="flex flex-wrap gap-2 mb-3">
                {video.tags.map((tag: string) => (
                  <span class="bg-indigo-100 dark:bg-indigo-800/40 text-indigo-800 dark:text-indigo-200 px-2 py-0.5 rounded-full text-xs">
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>

  <!-- Navigation buttons -->
  {videos.length > itemsPerView && (
    <div class="carousel-navigation flex justify-between w-full absolute top-1/2 left-0 -translate-y-1/2 z-10">
      <button 
        id={`${carouselId}-prev`}
        class="carousel-prev bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-full p-2 -ml-4 shadow-lg transform transition-all duration-300 hover:scale-110 focus:outline-none disabled:opacity-30 disabled:cursor-not-allowed"
        disabled
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button 
        id={`${carouselId}-next`}
        class="carousel-next bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-full p-2 -mr-4 shadow-lg transform transition-all duration-300 hover:scale-110 focus:outline-none disabled:opacity-30 disabled:cursor-not-allowed"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  )}
</div>

<script define:vars={{ carouselId, itemsPerView }}>
  // Carousel logic
  document.addEventListener('DOMContentLoaded', () => {
    const carouselInner = document.getElementById(`${carouselId}-inner`);
    const prevButton = document.getElementById(`${carouselId}-prev`);
    const nextButton = document.getElementById(`${carouselId}-next`);
    
    if (!carouselInner || !prevButton || !nextButton) return;
    
    let currentPosition = 0;
    const items = carouselInner.querySelectorAll('.video-carousel-item');
    const totalItems = items.length;
    const itemsPerPage = window.innerWidth >= 768 ? itemsPerView : 1; // itemsPerView on desktop, 1 on mobile
    const maxPosition = Math.max(0, totalItems - itemsPerPage);
    const isMobile = window.innerWidth < 768;
    
    // Update button states
    const updateButtons = () => {
      prevButton.disabled = currentPosition <= 0;
      nextButton.disabled = currentPosition >= maxPosition;
    };
    
    // Move the carousel
    const moveCarousel = () => {
      const translateValue = currentPosition * (-100 / itemsPerPage);
      carouselInner.style.transform = `translateX(${translateValue}%)`;
      updateButtons();
    };
    
    // Navigation functions
    const goToPrev = (e) => {
      // Prevent double-firing on mobile
      if (e.type === 'mouseenter' && isMobile) return;
      
      if (currentPosition > 0) {
        currentPosition--;
        moveCarousel();
      }
    };
    
    const goToNext = (e) => {
      // Prevent double-firing on mobile
      if (e.type === 'mouseenter' && isMobile) return;
      
      if (currentPosition < maxPosition) {
        currentPosition++;
        moveCarousel();
      }
    };
    
    // Button event listeners - conditional based on device
    prevButton.addEventListener('click', goToPrev);
    nextButton.addEventListener('click', goToNext);
    
    // Only add hover events on desktop
    if (!isMobile) {
      prevButton.addEventListener('mouseenter', goToPrev);
      nextButton.addEventListener('mouseenter', goToNext);
    }
    
    // Touch swipe functionality for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    
    const handleSwipe = () => {
      const swipeThreshold = 50; // Minimum swipe distance in pixels
      const swipeDistance = touchEndX - touchStartX;
      
      if (swipeDistance > swipeThreshold) {
        // Swipe right - go to previous
        goToPrev({ type: 'swipe' });
      } else if (swipeDistance < -swipeThreshold) {
        // Swipe left - go to next
        goToNext({ type: 'swipe' });
      }
    };
    
    carouselInner.addEventListener('touchstart', (e) => {
      touchStartX = e.changedTouches[0].screenX;
    });
    
    carouselInner.addEventListener('touchend', (e) => {
      touchEndX = e.changedTouches[0].screenX;
      handleSwipe();
    });
    
    // Window resize handler
    window.addEventListener('resize', () => {
      const newItemsPerPage = window.innerWidth >= 768 ? itemsPerView : 1;
      const newIsMobile = window.innerWidth < 768;
      
      // Update event listeners if device type changes
      if (newIsMobile !== isMobile) {
        if (newIsMobile) {
          prevButton.removeEventListener('mouseenter', goToPrev);
          nextButton.removeEventListener('mouseenter', goToNext);
        } else {
          prevButton.addEventListener('mouseenter', goToPrev);
          nextButton.addEventListener('mouseenter', goToNext);
        }
      }
      
      if (newItemsPerPage !== itemsPerPage) {
        // Reset position if screen size changes dramatically
        currentPosition = 0;
        moveCarousel();
      }
    });
    
    // Initialize
    updateButtons();
  });
</script>

<style>
  .video-carousel-inner {
    display: flex;
    transition: transform 1.3s ease;
  }
  
  .video-carousel-item {
    flex: 0 0 100%;
  }
  
  @media (min-width: 768px) {
    .video-carousel-item.carousel-item-2 {
      flex: 0 0 50%;
    }
    
    .video-carousel-item.carousel-item-3 {
      flex: 0 0 33.333%;
    }
    
    .video-carousel-item.carousel-item-4 {
      flex: 0 0 25%;
    }
  }
</style> 