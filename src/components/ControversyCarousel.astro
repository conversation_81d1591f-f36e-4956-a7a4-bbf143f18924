---
import type { Controversy } from '../data/controversies/types';

interface Props {
  controversies: Controversy[];
  legislatorName?: string; // 立委名稱，用於構建新聞查詢連結
}

const { controversies, legislatorName = "" } = Astro.props;

// 為每個爭議建立搜索URL
const getSearchUrl = (title: string) => {
  const searchQuery = `${legislatorName} ${title}`.trim();
  return `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}`;
};
---

<div class="controversy-carousel relative">
  {controversies.length > 0 ? (
    <div class="carousel-container overflow-hidden relative">
      <div id="carousel-items" class="flex transition-transform duration-500">
        {controversies.map((controversy: Controversy, index: number) => (
          <div class="carousel-item w-full flex-shrink-0" data-index={index}>
            <div class="space-y-4">
              {/* 爭議事項 (左側) */}
              <div class="flex items-start controversy-left-bubble md:flex hidden" id={`controversy-content-${index}`}>
                <div class="w-10 h-10 rounded-full bg-orange-500 flex items-center justify-center text-white flex-shrink-0">
                  <span class="text-lg">⚠️</span>
                </div>
                <div class="ml-2 max-w-[80%]">
                  <div class="bg-white dark:bg-gray-700 p-4 rounded-xl rounded-tl-none shadow-sm">
                    <h4 class="text-lg font-bold text-gray-800 dark:text-gray-200 mb-2">
                      {controversy.title}
                    </h4>
                    <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line" set:html={controversy.content}></p>
                    
                    {/* 標籤顯示 */}
                    {controversy.tags && controversy.tags.length > 0 && (
                      <div class="flex flex-wrap gap-2 mt-3">
                        {controversy.tags.map((tag: string) => (
                          <span class="bg-orange-100 dark:bg-orange-800/30 text-orange-700 dark:text-orange-200 px-2 py-0.5 rounded-full text-xs">
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    {/* 移到這裡：按鈕區塊（桌面版） */}
                    <div class="mt-4 flex flex-wrap gap-2">
                      <button 
                        type="button"
                        class="news-source-btn text-blue-600 dark:text-blue-400 text-sm flex items-center hover:underline cursor-pointer bg-transparent border-0 p-1"
                        data-url={getSearchUrl(controversy.title)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                        查看相關新聞
                      </button>
                      
                      <button 
                        type="button"
                        class="share-image-btn text-green-600 dark:text-green-400 text-sm flex items-center hover:bg-green-50 dark:hover:bg-green-900/20 cursor-pointer bg-transparent rounded-full py-1 px-2 transition-colors"
                        data-controversy-index={index}
                        title="分享成圖片"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        分享圖卡
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* 爭議事項 (手機版全寬) */}
              <div class="controversy-mobile-content md:hidden block" id={`controversy-mobile-content-${index}`}>
                <div class="bg-white dark:bg-gray-700 p-4 rounded-xl shadow-sm">
                  <h4 class="text-lg font-bold text-gray-800 dark:text-gray-200 mb-2">
                    {controversy.title}
                  </h4>
                  <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line" set:html={controversy.content}></p>
                  
                  {/* 標籤顯示 */}
                  {controversy.tags && controversy.tags.length > 0 && (
                    <div class="flex flex-wrap gap-2 mt-3">
                      {controversy.tags.map((tag: string) => (
                        <span class="bg-orange-100 dark:bg-orange-800/30 text-orange-700 dark:text-orange-200 px-2 py-0.5 rounded-full text-xs">
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  {/* 移到這裡：按鈕區塊（手機版） */}
                  <div class="mt-4 flex flex-wrap gap-2">
                    <button 
                      type="button"
                      class="news-source-btn text-blue-600 dark:text-blue-400 text-sm flex items-center hover:underline cursor-pointer bg-transparent border-0 p-1"
                      data-url={getSearchUrl(controversy.title)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                      查看相關新聞
                    </button>
                    
                    <button 
                      type="button"
                      class="share-image-btn text-green-600 dark:text-green-400 text-sm flex items-center hover:bg-green-50 dark:hover:bg-green-900/20 cursor-pointer bg-transparent rounded-full py-1 px-2 transition-colors"
                      data-controversy-index={index}
                      title="分享成圖片"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      分享圖卡
                    </button>
                  </div>
                </div>
              </div>

              {/* 反諷/提問 (右側) */}
              {controversy.reflections && controversy.reflections.length > 0 && (
                controversy.reflections.map((reflection: string) => (
                  <div class="flex items-start justify-end">
                    <div class="mr-2 max-w-[80%]">
                      <div class="bg-blue-100 dark:bg-blue-800/40 p-4 rounded-xl rounded-tr-none shadow-sm">
                        <p class="text-gray-800 dark:text-gray-200" set:html={reflection}></p>
                      </div>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white flex-shrink-0">
                      <span class="text-lg">🤔</span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        ))}
      </div>
      
      <!-- 導航按鈕 -->
      <div class="carousel-navigation flex justify-between w-full absolute top-1/3 left-0 -translate-y-1/2 z-10">
        <button id="prev-btn" class="carousel-prev bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-full p-2 -ml-4 shadow-lg transform transition-all duration-300 hover:scale-110 focus:outline-none disabled:opacity-30 disabled:cursor-not-allowed">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button id="next-btn" class="carousel-next bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-full p-2 -mr-4 shadow-lg transform transition-all duration-300 hover:scale-110 focus:outline-none disabled:opacity-30 disabled:cursor-not-allowed">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <!-- 進度指示器 -->
      <div class="flex justify-center mt-4 gap-2">
        {controversies.map((_: Controversy, index: number) => (
          <button class="carousel-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors" data-index={index}></button>
        ))}
      </div>
      
      <!-- 目前項目 / 總項目 -->
      <div class="text-center text-sm text-gray-500 dark:text-gray-400 mt-2">
        <span id="current-index">1</span> / <span id="total-items">{controversies.length}</span>
      </div>
    </div>
  ) : (
    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
      此立委目前沒有爭議事項記錄
    </div>
  )}
</div>

<!-- 圖片導出模態框 -->
<div id="share-image-modal" class="fixed inset-0 bg-black/80 z-50 hidden flex items-center justify-center">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 overflow-hidden">
    <div class="p-4 bg-gradient-to-r from-orange-500 to-red-600 text-white flex justify-between items-center">
      <h3 class="text-lg font-bold">分享爭議圖卡</h3>
      <button id="close-share-modal" class="text-white hover:text-white/80 p-2 rounded-full hover:bg-white/10 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
    <div class="p-6">
      <div id="image-preview-container" class="bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden shadow-inner mb-4 flex justify-center items-center min-h-[300px]">
        <div id="image-preview" class="w-full h-full"></div>
        <div id="loading-image" class="text-center hidden">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-gray-100"></div>
          <p class="mt-2 text-gray-700 dark:text-gray-300">圖片生成中...</p>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <button id="download-image" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          下載圖片
        </button>
        <button id="copy-image" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          複製圖片
        </button>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const carouselItems = document.querySelectorAll('.carousel-item');
  const totalItems = carouselItems.length;
  const carouselContainer = document.getElementById('carousel-items');
  const prevBtn = document.getElementById('prev-btn');
  const nextBtn = document.getElementById('next-btn');
  const dots = document.querySelectorAll('.carousel-dot');
  const currentIndexElement = document.getElementById('current-index');
  
  // 分享相關元素
  const shareModal = document.getElementById('share-image-modal');
  const closeShareModalBtn = document.getElementById('close-share-modal');
  const downloadImageBtn = document.getElementById('download-image');
  const copyImageBtn = document.getElementById('copy-image');
  const imagePreviewContainer = document.getElementById('image-preview-container');
  const imagePreview = document.getElementById('image-preview');
  const loadingImage = document.getElementById('loading-image');
  
  let currentIndex = 0;
  
  // 自動輪播控制
  let autoplayInterval: number | null = null;
  let isUserInteracting = false;  // 新增標記用戶是否正在互動
  
  // 啟動自動輪播
  function startAutoplay() {
    if (!isUserInteracting && !autoplayInterval) {
      autoplayInterval = window.setInterval(goToNext, 12000);
    }
  }
  
  // 停止自動輪播
  function stopAutoplay() {
    if (autoplayInterval) {
      window.clearInterval(autoplayInterval);
      autoplayInterval = null;
    }
  }
  
  // 啟動初始輪播
  startAutoplay();
  
  // 鼠標進入輪播區域時停止輪播
  document.querySelector('.controversy-carousel')?.addEventListener('mouseenter', () => {
    isUserInteracting = true;
    stopAutoplay();
  });
  
  // 鼠標離開輪播區域時恢復輪播
  document.querySelector('.controversy-carousel')?.addEventListener('mouseleave', () => {
    isUserInteracting = false;
    startAutoplay();
  });
  
  // 新增：觸摸開始時停止輪播
  document.querySelector('.controversy-carousel')?.addEventListener('touchstart', () => {
    isUserInteracting = true;
    stopAutoplay();
  }, { passive: true });
  
  // 新增：觸摸結束後延遲恢復輪播
  document.querySelector('.controversy-carousel')?.addEventListener('touchend', () => {
    // 延遲3秒恢復輪播，給用戶足夠時間點擊
    setTimeout(() => {
      isUserInteracting = false;
      startAutoplay();
    }, 3000);
  }, { passive: true });
  
  // 初始化
  updateCarousel();
  updateActiveDot();
  updateButtons();
  
  // 事件監聽
  if (prevBtn) prevBtn.addEventListener('click', goToPrev);
  if (nextBtn) nextBtn.addEventListener('click', goToNext);
  
  // 點擊指示器切換
  dots.forEach(dot => {
    dot.addEventListener('click', (e) => {
      const target = e.currentTarget as HTMLElement;
      const dataIndex = target.getAttribute('data-index');
      const index = dataIndex ? parseInt(dataIndex) : 0;
      currentIndex = index;
      updateCarousel();
      updateActiveDot();
      updateButtons();
    });
  });
  
  // 處理新聞連結按鈕
  document.querySelectorAll('.news-source-btn').forEach((button: Element) => {
    button.addEventListener('click', function(this: HTMLButtonElement, e: Event) {
      // 阻止事件冒泡和默認行為
      e.stopPropagation();
      e.preventDefault();
      
      // 獲取URL
      const url = this.getAttribute('data-url');
      
      if (url) {
        console.log('Opening URL:', url); // 調試用
        window.open(url, '_blank', 'noopener,noreferrer');
      }
    });
  });
  
  function goToPrev() {
    if (currentIndex > 0) {
      currentIndex--;
      updateCarousel();
      updateActiveDot();
      updateButtons();
    }
  }
  
  function goToNext() {
    if (currentIndex < totalItems - 1) {
      currentIndex++;
      updateCarousel();
      updateActiveDot();
      updateButtons();
    } else {
      // 如果是自動輪播，到達最後一項後回到第一項
      if (autoplayInterval) {
        currentIndex = 0;
        updateCarousel();
        updateActiveDot();
        updateButtons();
      }
    }
  }
  
  function updateCarousel() {
    if (carouselContainer) {
      carouselContainer.style.transform = `translateX(-${currentIndex * 100}%)`;
    }
    if (currentIndexElement) {
      currentIndexElement.textContent = (currentIndex + 1).toString();
    }
  }
  
  function updateActiveDot() {
    dots.forEach((dot, index) => {
      if (index === currentIndex) {
        dot.classList.add('bg-red-500', 'dark:bg-red-600');
        dot.classList.remove('bg-gray-300', 'dark:bg-gray-600');
      } else {
        dot.classList.remove('bg-red-500', 'dark:bg-red-600');
        dot.classList.add('bg-gray-300', 'dark:bg-gray-600');
      }
    });
  }
  
  function updateButtons() {
    if (prevBtn) {
      (prevBtn as HTMLButtonElement).disabled = currentIndex <= 0;
      prevBtn.classList.toggle('opacity-30', currentIndex <= 0);
      prevBtn.classList.toggle('cursor-not-allowed', currentIndex <= 0);
    }
    
    if (nextBtn) {
      (nextBtn as HTMLButtonElement).disabled = currentIndex >= totalItems - 1;
      nextBtn.classList.toggle('opacity-30', currentIndex >= totalItems - 1);
      nextBtn.classList.toggle('cursor-not-allowed', currentIndex >= totalItems - 1);
    }
  }
  
  // 重新添加點擊事件到所有分享按鈕 - 使用更可靠的方式
  document.querySelectorAll('.share-image-btn').forEach((btn) => {
    // 移除現有監聽器並重新添加
    const newBtn = btn.cloneNode(true) as HTMLElement;
    if (btn.parentNode) {
      btn.parentNode.replaceChild(newBtn, btn);
      
      // 為所有分享按鈕添加明顯的視覺反饋類
      newBtn.classList.add('share-button-enhanced');
      
      // 添加鼠標和觸摸事件
      newBtn.addEventListener('mousedown', handleShareButtonClick);
      newBtn.addEventListener('touchstart', function(this: HTMLElement, e: TouchEvent) {
        // 停止輪播
        isUserInteracting = true;
        stopAutoplay();
        
        // 添加視覺反饋
        this.classList.add('button-active');
      }, { passive: true });
      
      newBtn.addEventListener('touchend', function(this: HTMLElement, e: TouchEvent) {
        // 移除視覺反饋
        this.classList.remove('button-active');
        
        // 觸發點擊事件
        handleShareButtonClick.call(this, e);
      });
    }
  });
  
  // 定義全局作用域內的函數
  let modalCloseFunction: () => void;
  let modalOpenFunction: () => void;
  
  // 分享按鈕點擊處理函數
  function handleShareButtonClick(this: HTMLElement, e: Event) {
    // 阻止事件冒泡和默認行為
    e.preventDefault();
    e.stopPropagation();
    
    // 停止自動輪播
    isUserInteracting = true;
    stopAutoplay();
    
    console.log('分享圖卡按鈕被點擊', this);
    
    // 獲取索引並生成圖片
    const index = this.getAttribute('data-controversy-index');
    if (index !== null) {
      // 添加視覺反饋
      this.classList.add('button-active');
      
      // 延遲生成圖片，讓用戶看到視覺反饋
      setTimeout(() => {
        this.classList.remove('button-active');
        generateAndShowImage(parseInt(index));
      }, 150);
    }
  }
  
  // 關閉模態框函數
  function closeShareModal() {
    console.log('關閉模態框');
    if (shareModal) {
      shareModal.classList.add('hidden');
      shareModal.style.display = 'none';
    }
  }
  
  // 開啟模態框函數
  function openShareModal() {
    if (shareModal) {
      shareModal.classList.remove('hidden');
      shareModal.style.display = 'flex';
    }
  }
  
  // 保存函數引用
  modalCloseFunction = closeShareModal;
  modalOpenFunction = openShareModal;
  
  // 確保關閉按鈕有事件監聽器
  if (closeShareModalBtn) {
    // 移除任何現有的事件監聽器
    const newCloseBtn = closeShareModalBtn.cloneNode(true);
    if (closeShareModalBtn.parentNode) {
      closeShareModalBtn.parentNode.replaceChild(newCloseBtn, closeShareModalBtn);
    }
    
    // 添加新的事件處理
    newCloseBtn.addEventListener('click', function(e: Event) {
      e.preventDefault();
      e.stopPropagation();
      console.log('關閉按鈕被點擊');
      closeShareModal();
    });
  }
  
  // 點擊背景關閉模態框
  if (shareModal) {
    shareModal.addEventListener('click', function(e: Event) {
      if (e.target === this) {
        closeShareModal();
      }
    });
  }
  
  // 添加ESC鍵關閉模態框
  document.addEventListener('keydown', function(e: KeyboardEvent) {
    if (e.key === 'Escape' && shareModal && !shareModal.classList.contains('hidden')) {
      closeShareModal();
    }
  });
  
  // 動態加載 html2canvas 庫
  function loadHtml2Canvas() {
    return new Promise((resolve, reject) => {
      if (window.html2canvas) {
        resolve(window.html2canvas);
        return;
      }
      
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js';
      script.onload = () => resolve(window.html2canvas);
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
  
  // 生成並顯示圖片
  async function generateAndShowImage(index: number) {
    if (!shareModal || !imagePreview || !loadingImage) return;
    
    // 顯示模態框和載入指示器
    openShareModal();
    imagePreview.innerHTML = '';
    imagePreview.style.display = 'none';
    loadingImage.classList.remove('hidden');
    
    try {
      // 動態加載 html2canvas
      const html2canvas = await loadHtml2Canvas() as any;
      
      // 選擇要轉換的元素 (根據視窗大小選擇桌面版或移動版)
      const isMobile = window.innerWidth < 768;
      const targetElement = document.getElementById(
        isMobile ? `controversy-mobile-content-${index}` : `controversy-content-${index}`
      );
      
      if (!targetElement) {
        throw new Error('找不到目標元素');
      }
      
      // 獲取立委名稱（從URL中提取）
      const legislatorName = window.location.pathname.split('/').pop();
      
      // 創建帶有樣式的克隆以便截圖
      const clone = targetElement.cloneNode(true) as HTMLElement;
      clone.style.position = 'absolute';
      clone.style.left = '-9999px';
      clone.style.background = '#fff'; // 確保背景是白色
      clone.style.width = isMobile ? '100%' : 'auto';
      clone.style.maxWidth = '600px';
      clone.style.padding = '20px';
      clone.style.borderRadius = '12px';
      clone.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      
      // 移除分享按鈕
      const shareBtn = clone.querySelector('.share-image-btn');
      if (shareBtn) {
        shareBtn.remove();
      }
      
      // 添加水印
      const watermark = document.createElement('div');
      watermark.innerHTML = `
        <div style="margin-top: 16px; padding: 8px; background-color: #f8f9fa; text-align: center; font-size: 12px; color: #666; border-radius: 4px;">
          <p>100個罷免的理由 - https://recall.islandcountry.tw/</p>
        </div>
      `;
      clone.appendChild(watermark);
      
      document.body.appendChild(clone);
      
      // 生成圖片
      const canvas = await html2canvas(clone, {
        scale: 2, // 提高解析度
        useCORS: true, // 允許跨域圖片
        backgroundColor: '#ffffff', // 白色背景
        logging: false
      });
      
      // 清理克隆的元素
      document.body.removeChild(clone);
      
      // 獲取圖片數據
      const imageData = canvas.toDataURL('image/png');
      
      // 保存圖片到伺服器並獲取分享連結
      try {
        const response = await fetch('/api/save-controversy-image', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            imageData,
            legislatorName,
            controversyIndex: index
          }),
        });
        
        const result = await response.json();
        
        if (result.success) {
          // 創建分享連結按鈕
          const shareLinkBtn = document.createElement('button');
          shareLinkBtn.className = 'bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center justify-center mt-2 w-full';
          shareLinkBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
            </svg>
            複製分享連結
          `;
          
          shareLinkBtn.addEventListener('click', () => {
            const fullUrl = `${window.location.origin}${result.url}`;
            navigator.clipboard.writeText(fullUrl)
              .then(() => {
                shareLinkBtn.innerHTML = '✓ 連結已複製';
                setTimeout(() => {
                  shareLinkBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                    </svg>
                    複製分享連結
                  `;
                }, 2000);
              })
              .catch(err => {
                console.error('複製連結失敗:', err);
                alert('複製連結失敗，請手動複製以下連結:\n' + fullUrl);
              });
          });
          
          // 將按鈕添加到下載按鈕下方的容器
          const buttonContainer = downloadImageBtn?.parentElement;
          if (buttonContainer) {
            buttonContainer.appendChild(shareLinkBtn);
          }
        }
      } catch (serverError) {
        console.error('保存圖片到伺服器失敗:', serverError);
        // 即使伺服器保存失敗，仍然顯示生成的圖片供用戶下載
      }
      
      // 顯示生成的圖片
      loadingImage.classList.add('hidden');
      imagePreview.style.display = 'block';
      imagePreview.appendChild(canvas);
      
      // 設置下載按鈕
      if (downloadImageBtn) {
        downloadImageBtn.onclick = () => {
          const link = document.createElement('a');
          link.download = `爭議事項-${index + 1}.png`;
          link.href = canvas.toDataURL('image/png');
          link.click();
        };
      }
      
      // 設置複製按鈕
      if (copyImageBtn) {
        copyImageBtn.onclick = async () => {
          try {
            canvas.toBlob(async (blob: Blob) => {
              if (blob) {
                const data = [new ClipboardItem({ 'image/png': blob })];
                await navigator.clipboard.write(data);
                alert('圖片已複製到剪貼簿');
              }
            });
          } catch (err) {
            console.error('複製圖片失敗:', err);
            alert('複製圖片失敗，請嘗試下載圖片');
          }
        };
      }
      
    } catch (error) {
      console.error('生成圖片失敗:', error);
      if (loadingImage) loadingImage.classList.add('hidden');
      if (imagePreview) {
        imagePreview.style.display = 'block';
        imagePreview.innerHTML = '<div class="text-red-500 text-center p-4">生成圖片失敗，請稍後再試</div>';
      }
    }
  }
});
</script>

<style>
.carousel-container {
  width: 100%;
}
#carousel-items {
  width: 100%;
}
.carousel-item {
  width: 100%;
}
.carousel-prev:disabled,
.carousel-next:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Make news buttons look like links but be more clickable */
.news-source-btn {
  appearance: none;
  -webkit-appearance: none;
  background: transparent;
  border: none;
  text-align: left;
  font: inherit;
  cursor: pointer;
  padding: 8px;
  margin: -8px;
  border-radius: 4px;
  position: relative;
  z-index: 100;
}

.news-source-btn:hover {
  text-decoration: underline;
  background-color: rgba(0, 0, 0, 0.03);
}

/* Share button styles */
.share-image-btn {
  appearance: none;
  -webkit-appearance: none;
  font: inherit;
  cursor: pointer;
  border: none;
  transition: all 0.15s ease;
  position: relative;
  z-index: 200; /* 提高按鈕的 z-index 確保可以點擊 */
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  min-height: 36px;  /* 確保最小高度 */
  min-width: 110px;  /* 增加最小寬度 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(16, 185, 129, 0.1); /* 淡綠色背景，增加視覺區分 */
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  -webkit-tap-highlight-color: rgba(16, 185, 129, 0.3); /* 在移動設備上點擊時顯示高亮 */
  font-weight: 500; /* 稍微加粗文字 */
  letter-spacing: 0.01em;
}

.share-image-btn:hover {
  background-color: rgba(16, 185, 129, 0.2);
  box-shadow: 0 2px 4px rgba(0,0,0,0.15);
  transform: translateY(-1px);
}

.share-image-btn:active,
.share-image-btn.button-active {
  background-color: rgba(16, 185, 129, 0.3);
  transform: translateY(1px);
  box-shadow: 0 0 2px rgba(0,0,0,0.1);
}

/* Mobile optimization */
@media (max-width: 768px) {
  .controversy-left-bubble {
    display: none;
  }
  
  /* 增加移動設備上的可點擊區域 */
  .share-image-btn {
    padding: 0.6rem 1.2rem;
    min-height: 46px; /* 進一步增加高度 */
    margin: 0.35rem 0;
    font-size: 1.05rem; /* 稍微增加字體大小 */
  }
  
  /* 特殊增強版分享按鈕 */
  .share-button-enhanced {
    position: relative;
  }
  
  .share-button-enhanced::after {
    content: "";
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border-radius: 9999px;
    /* 增加不可見的點擊區域 */
    z-index: 195;
  }
}

/* TypeScript 類型聲明添加 */
declare global {
  interface Window {
    html2canvas: any;
  }
  interface Navigator {
    clipboard: {
      write(data: any[]): Promise<void>;
    }
  }
}

/* 為關閉按鈕添加樣式 */
#close-share-modal {
  cursor: pointer;
  padding: 8px;
  border-radius: 9999px;
  transition: all 0.2s ease;
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}

#close-share-modal:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

#close-share-modal:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 確保模態框正確顯示 */
#share-image-modal {
  opacity: 0;
  transition: opacity 0.2s ease;
}

#share-image-modal:not(.hidden) {
  opacity: 1;
}
</style> 