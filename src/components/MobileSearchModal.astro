---
// MobileSearchModal.astro - 專為行動裝置優化的搜尋模態框
---

<div id="mobile-search-modal" class="mobile-search-modal-hidden">
  <div class="mobile-search-overlay"></div>
  <div class="mobile-search-content">
    <div class="mobile-search-header">
      <h3>搜尋罷免理由</h3>
      <button id="mobile-search-close">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
        </svg>
        <span class="sr-only">關閉</span>
      </button>
    </div>
    
    <div class="mobile-search-body">
      <div class="relative">
        <input
          type="text"
          id="mobile-search-input"
          placeholder="輸入關鍵字搜尋罷免理由..."
          autocomplete="off"
        />
        <div class="search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
          </svg>
        </div>
      </div>
      
      <div id="mobile-search-results"></div>
      <div id="mobile-search-no-results" class="hidden">
        找不到符合的內容
      </div>
      <div id="mobile-search-loading" class="hidden">
        <div class="loading-spinner"></div>
        <span>搜尋中...</span>
      </div>
    </div>
  </div>
</div>

<style>
  .mobile-search-modal-hidden {
    display: none !important;
  }
  
  #mobile-search-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .mobile-search-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
  }
  
  .mobile-search-content {
    position: relative;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
  }
  
  .dark .mobile-search-content {
    background-color: #1f2937;
    color: white;
  }
  
  .mobile-search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
  }
  
  .dark .mobile-search-header {
    border-bottom-color: #374151;
  }
  
  .mobile-search-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }
  
  .dark .mobile-search-header h3 {
    color: white;
  }
  
  #mobile-search-close {
    background: transparent;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
  }
  
  .dark #mobile-search-close {
    color: #9ca3af;
  }
  
  .mobile-search-body {
    padding: 16px;
    overflow-y: auto;
    max-height: calc(80vh - 70px);
    flex-grow: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
  }
  
  #mobile-search-input {
    width: 100%;
    padding: 12px 16px 12px 40px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    outline: none;
    background-color: white;
    color: #111827;
  }
  
  .dark #mobile-search-input {
    background-color: #374151;
    border-color: #4b5563;
    color: white;
  }
  
  #mobile-search-input:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  }
  
  .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
  }
  
  .dark .search-icon {
    color: #9ca3af;
  }
  
  #mobile-search-loading {
    padding: 24px 0;
    text-align: center;
    color: #6b7280;
    display: none;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  
  #mobile-search-loading.hidden {
    display: none;
  }
  
  #mobile-search-loading:not(.hidden) {
    display: flex;
  }
  
  .dark #mobile-search-loading {
    color: #9ca3af;
  }
  
  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid transparent;
    border-bottom-color: #ef4444;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  #mobile-search-no-results {
    padding: 24px 0;
    text-align: center;
    color: #6b7280;
    width: 100%;
  }
  
  .dark #mobile-search-no-results {
    color: #9ca3af;
  }
  
  /* 搜尋結果樣式 */
  #mobile-search-results {
    margin-top: 16px;
    max-width: 100%;
    flex-grow: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .mobile-search-result-item {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: background-color 0.2s, border-color 0.2s;
    width: 100%;
    word-break: break-word;
    overflow-wrap: break-word;
    border: 1px solid #e5e7eb;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .mobile-search-result-item:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
  }
  
  .dark .mobile-search-result-item {
    background-color: #1f2937;
    border-color: #374151;
    color: #f3f4f6;
  }
  
  .dark .mobile-search-result-item:hover {
    background-color: #2d3748;
  }
  
  .mobile-search-result-item.active {
    background-color: rgba(239, 68, 68, 0.1);
    border-left: 3px solid #ef4444;
  }
  
  .dark .mobile-search-result-item.active {
    background-color: rgba(239, 68, 68, 0.2);
  }
  
  .mobile-search-result-title {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 4px;
    word-break: break-word;
    overflow-wrap: break-word;
  }
  
  .dark .mobile-search-result-title {
    color: white;
  }
  
  .mobile-search-result-excerpt {
    font-size: 14px;
    color: #4b5563;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-word;
    overflow-wrap: break-word;
    margin-bottom: 8px;
  }
  
  .dark .mobile-search-result-excerpt {
    color: #9ca3af;
  }
  
  .mobile-search-result-badge {
    display: inline-block;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 100px;
    background-color: #fee2e2;
    color: #b91c1c;
    margin-top: 4px;
  }
  
  .dark .mobile-search-result-badge {
    background-color: rgba(185, 28, 28, 0.2);
    color: #fca5a5;
  }
</style>

<script>
  import FlexSearch from 'flexsearch';
  
  // 初始化搜尋引擎  
  let searchIndex: any = null;
  let reasonsData: any[] = [];
  let activeResultIndex = 0;
  
  // 搜尋歷史記錄的鍵名
  const SEARCH_HISTORY_KEY = 'recall-reasons-search-history';
  
  function getSearchHistory(): string[] {
    try {
      const historyJson = localStorage.getItem(SEARCH_HISTORY_KEY);
      if (historyJson) {
        return JSON.parse(historyJson);
      }
    } catch (error) {
      console.error('讀取搜尋記錄失敗:', error);
    }
    return [];
  }
  
  function getLastSearchQuery(): string {
    const history = getSearchHistory();
    return history.length > 0 ? history[0] : '';
  }
  
  function saveSearchHistory(query: string) {
    if (!query) return;
    
    try {
      // 保存最近5次搜尋
      const history = getSearchHistory();
      
      // 如果已經存在相同的搜尋詞，先移除
      const filteredHistory = history.filter(item => item !== query);
      
      // 將新的搜尋詞添加到開頭
      filteredHistory.unshift(query);
      
      // 只保留最近5次搜尋
      const newHistory = filteredHistory.slice(0, 5);
      
      localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory));
    } catch (error) {
      console.error('保存搜尋記錄失敗:', error);
    }
  }
  
  async function initializeSearch() {
    console.log('初始化移動版搜尋功能');
    if (searchIndex) return searchIndex;
    
    try {
      // 顯示 loading 狀態
      const loadingEl = document.getElementById('mobile-search-loading');
      if (loadingEl) loadingEl.classList.remove('hidden');
      
      // 取得所有罷免理由資料
      const response = await fetch('/api/search-data.json');
      reasonsData = await response.json();
      console.log('取得罷免理由資料筆數:', reasonsData.length);
      
      // 創建 FlexSearch 索引
      searchIndex = new FlexSearch.Document({
        document: {
          id: "id",
          index: ["title", "body"],
          store: ["title", "body", "slug", "excerpt"]
        },
        encode: function(str) {
          // 自定義編碼函數，處理中文分詞
          return str.split(/\s+/)
            .filter(word => word.length > 0)
            .map(word => word.toLowerCase());
        },
        tokenize: "full",
        resolution: 9,
        cache: true
      });
      
      // 增強標題搜尋
      const enhancedReasons = reasonsData.map(reason => ({
        ...reason,
        // 複製標題加到內文，增加標題關鍵詞的權重
        body: `${reason.title} ${reason.title} ${reason.body}`
      }));
      
      // 將資料加入索引
      enhancedReasons.forEach(reason => {
        searchIndex.add(reason);
      });
      
      console.log('移動版搜尋索引初始化完成');
      
      // 隱藏 loading 狀態
      if (loadingEl) loadingEl.classList.add('hidden');
      
      return searchIndex;
      
    } catch (error) {
      console.error("移動版搜尋初始化錯誤:", error);
      
      // 發生錯誤時也要隱藏 loading 狀態
      const loadingEl = document.getElementById('mobile-search-loading');
      if (loadingEl) loadingEl.classList.add('hidden');
      
      return null;
    }
  }
  
  function performSearch(query: string) {
    console.log('執行移動版搜尋:', query);
    if (!searchIndex) return [];
    
    // 先顯示 loading
    const loadingEl = document.getElementById('mobile-search-loading');
    const resultsEl = document.getElementById('mobile-search-results');
    const noResultsEl = document.getElementById('mobile-search-no-results');
    
    if (loadingEl) loadingEl.classList.remove('hidden');
    if (resultsEl) resultsEl.innerHTML = '';
    if (noResultsEl) noResultsEl.classList.add('hidden');
    
    try {
      // 搜尋
      const searchResults = searchIndex.search(query, { limit: 20 });
      console.log('原始搜尋結果:', searchResults);
      
      // 合併所有結果並去重
      const allIds = new Set();
      const combinedResults: any[] = [];
      
      // 處理搜尋結果
      searchResults.forEach((result: any) => {
        result.result.forEach((id: string) => {
          if (!allIds.has(id)) {
            allIds.add(id);
            const reason = reasonsData.find(item => item.id === id);
            if (reason) {
              // 判斷是否匹配標題
              const isTitleMatch = reason.title && reason.title.toLowerCase().includes(query.toLowerCase());
              combinedResults.push({
                ...reason,
                matchType: isTitleMatch ? 'title' : 'body'
              });
            }
          }
        });
      });
      
      // 將標題匹配的項目排在前面
      combinedResults.sort((a, b) => {
        // 首先按匹配類型排序
        if (a.matchType === 'title' && b.matchType !== 'title') return -1;
        if (a.matchType !== 'title' && b.matchType === 'title') return 1;
        
        // 然後按熱門度排序
        return (b.popularity || 0) - (a.popularity || 0);
      });
      
      console.log('移動版搜尋結果數:', combinedResults.length);
      
      // 如果沒有結果，顯示無結果訊息
      if (combinedResults.length === 0 && noResultsEl) {
        noResultsEl.classList.remove('hidden');
      }
      
      return combinedResults;
    } catch (error) {
      console.error("搜尋過程中發生錯誤:", error);
      return [];
    } finally {
      // 隱藏 loading (總是執行)
      if (loadingEl) loadingEl.classList.add('hidden');
    }
  }
  
  function renderSearchResults(results: any[]) {
    console.log('渲染移動版搜尋結果，數量:', results.length);
    const resultsContainer = document.getElementById('mobile-search-results');
    if (!resultsContainer) {
      console.error('找不到移動版搜尋結果容器元素');
      return;
    }
    
    // 清空現有結果
    resultsContainer.innerHTML = '';
    
    if (results.length === 0) {
      console.log('無搜尋結果可顯示');
      return;
    }
    
    // 直接將所有結果項加入容器，不要使用嵌套的列表元素
    for (let index = 0; index < results.length; index++) {
      const item = results[index];
      
      // 創建結果項目容器
      const resultItem = document.createElement('div');
      resultItem.className = 'mobile-search-result-item';
      if (index === 0) {
        resultItem.classList.add('active');
      }
      
      // 處理標題
      let title = item.title || '無標題';
      if (title.length > 100) {
        title = title.substring(0, 97) + '...';
      }
      
      const titleEl = document.createElement('div');
      titleEl.className = 'mobile-search-result-title';
      titleEl.textContent = title;
      
      // 處理內容摘要
      let excerptText = item.excerpt || '';
      if (excerptText.includes('__vite_ssr_import') || excerptText.includes('AstroError')) {
        excerptText = item.title || '無摘要';
      }
      
      // 截短過長的摘要
      if (excerptText.length > 150) {
        excerptText = excerptText.substring(0, 147) + '...';
      }
      
      const excerpt = document.createElement('div');
      excerpt.className = 'mobile-search-result-excerpt';
      excerpt.textContent = excerptText;
      
      // 添加匹配類型標籤
      const matchBadge = document.createElement('span');
      matchBadge.className = 'mobile-search-result-badge';
      matchBadge.textContent = item.matchType === 'title' ? '標題符合' : '內容符合';
      
      // 組合結果項目
      resultItem.appendChild(titleEl);
      resultItem.appendChild(excerpt);
      resultItem.appendChild(matchBadge);
      
      // 點擊項目時跳轉
      resultItem.addEventListener('click', () => {
        try {
          console.log('點擊搜尋結果，跳轉到:', `/reasons/${item.slug}`);
          window.location.href = `/reasons/${item.slug}`;
        } catch (error) {
          console.error('導航到頁面時出錯:', error);
          alert('無法導航到頁面，請稍後再試');
        }
      });
      
      // 鼠標懸停時設置為活動項目
      resultItem.addEventListener('mouseenter', () => {
        setActiveResult(index);
      });
      
      // 直接添加到結果容器
      resultsContainer.appendChild(resultItem);
    }
    
    // 設置初始選中項
    if (results.length > 0) {
      setActiveResult(0);
    }
  }
  
  function setActiveResult(index: number) {
    const items = document.querySelectorAll('.mobile-search-result-item');
    if (items.length === 0) return;
    
    // 移除所有項目的活動狀態
    items.forEach(item => {
      item.classList.remove('active');
    });
    
    // 設置新的活動項目
    activeResultIndex = index;
    const activeItem = items[index] as HTMLElement;
    if (activeItem) {
      activeItem.classList.add('active');
      activeItem.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
    }
  }
  
  function navigateResults(direction: 'up' | 'down') {
    const items = document.querySelectorAll('.mobile-search-result-item');
    if (items.length === 0) return;
    
    let newIndex = activeResultIndex;
    
    if (direction === 'up') {
      newIndex = (newIndex - 1 + items.length) % items.length;
    } else {
      newIndex = (newIndex + 1) % items.length;
    }
    
    setActiveResult(newIndex);
  }
  
  function selectActiveResult() {
    const items = document.querySelectorAll('.mobile-search-result-item');
    if (items.length === 0) return;
    
    const activeItem = items[activeResultIndex] as HTMLElement;
    if (activeItem) {
      activeItem.click();
    }
  }
  
  function openMobileSearchModal() {
    console.log('開啟移動版搜尋模態框');
    const modal = document.getElementById('mobile-search-modal');
    const input = document.getElementById('mobile-search-input') as HTMLInputElement;
    
    if (modal) {
      modal.classList.remove('mobile-search-modal-hidden');
      document.body.style.overflow = 'hidden';
      
      if (input) {
        setTimeout(() => {
          input.focus();
          console.log('已聚焦到移動版搜尋輸入框');
        }, 100);
      }
      
      // 初始化搜尋功能
      initializeSearch().then((index) => {
        // 檢查是否初始化成功
        if (!index) {
          console.error('搜尋索引初始化失敗');
          return;
        }
        
        // 恢復上一次的搜尋記錄
        if (input) {
          const lastQuery = getLastSearchQuery();
          if (lastQuery) {
            input.value = lastQuery;
            
            // 執行搜尋前先延遲一小段時間，確保加載完成
            setTimeout(() => {
              const results = performSearch(lastQuery);
              renderSearchResults(results);
            }, 200);
          }
        }
      }).catch(error => {
        console.error('初始化搜尋時發生錯誤:', error);
        
        // 隱藏 loading 狀態
        const loadingEl = document.getElementById('mobile-search-loading');
        if (loadingEl) loadingEl.classList.add('hidden');
      });
    } else {
      console.error('找不到移動版搜尋模態框元素');
    }
  }
  
  function closeMobileSearchModal() {
    console.log('關閉移動版搜尋模態框');
    const modal = document.getElementById('mobile-search-modal');
    const input = document.getElementById('mobile-search-input') as HTMLInputElement;
    const resultsEl = document.getElementById('mobile-search-results');
    
    if (modal) {
      modal.classList.add('mobile-search-modal-hidden');
      document.body.style.overflow = '';
      
      // 清空搜尋框和結果
      if (input) input.value = '';
      if (resultsEl) resultsEl.innerHTML = '';
      
      console.log('已成功關閉移動版搜尋模態框');
    } else {
      console.error('找不到移動版搜尋模態框元素');
    }
  }
  
  // 確保DOM完全加載後再綁定事件
  function initializeMobileSearch() {
    console.log('初始化移動版搜尋模態框');
    
    // 確保模態框初始是隱藏的
    const modal = document.getElementById('mobile-search-modal');
    if (modal && !modal.classList.contains('mobile-search-modal-hidden')) {
      modal.classList.add('mobile-search-modal-hidden');
    }
    
    // 關閉按鈕
    const closeBtn = document.getElementById('mobile-search-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        closeMobileSearchModal();
      });
      console.log('已添加關閉按鈕事件處理');
    } else {
      console.error('找不到關閉按鈕元素');
    }
    
    // 點擊遮罩層關閉
    const overlay = document.querySelector('.mobile-search-overlay');
    if (overlay) {
      overlay.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        closeMobileSearchModal();
      });
      console.log('已添加遮罩層事件處理');
    } else {
      console.error('找不到遮罩層元素');
    }
    
    // 輸入框事件
    const searchInput = document.getElementById('mobile-search-input');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const target = e.target as HTMLInputElement;
        const query = target.value.trim();
        
        if (query.length < 1) {
          const resultsEl = document.getElementById('mobile-search-results');
          const noResultsEl = document.getElementById('mobile-search-no-results');
          if (resultsEl) resultsEl.innerHTML = '';
          if (noResultsEl) noResultsEl.classList.add('hidden');
          return;
        }
        
        // 執行搜尋
        const results = performSearch(query);
        renderSearchResults(results);
        
        // 重置選中索引
        activeResultIndex = 0;
        
        // 保存搜尋記錄
        saveSearchHistory(query);
      });
      
      searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowDown') {
          e.preventDefault();
          navigateResults('down');
        } else if (e.key === 'ArrowUp') {
          e.preventDefault();
          navigateResults('up');
        } else if (e.key === 'Enter') {
          e.preventDefault();
          selectActiveResult();
        } else if (e.key === 'Escape') {
          e.preventDefault();
          closeMobileSearchModal();
        }
      });
      
      console.log('已添加搜尋輸入框事件處理');
    } else {
      console.error('找不到搜尋輸入框元素');
    }
    
    // 全局鍵盤事件
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const modal = document.getElementById('mobile-search-modal');
        if (modal && !modal.classList.contains('mobile-search-modal-hidden')) {
          closeMobileSearchModal();
        }
      }
    });
    
    // 暴露函數到全局範圍，供外部調用
    const windowAny = window as any;
    windowAny.openMobileSearchModal = openMobileSearchModal;
    windowAny.closeMobileSearchModal = closeMobileSearchModal;
    
    console.log('立即暴露搜尋功能到全局範圍');
    console.log('移動版搜尋模態框初始化完成');
  }
  
  // 當 DOM 加載完成後初始化
  document.addEventListener('DOMContentLoaded', () => {
    initializeMobileSearch();
    console.log('DOMContentLoaded - 再次確認搜尋功能已暴露');
  });
  
  // 如果 DOMContentLoaded 已經觸發過，則立即初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMobileSearch);
  } else {
    initializeMobileSearch();
  }
</script> 