---
interface Props {
  title: string;
  description?: string;
  date: Date;
  youtubeId?: string;
  image?: string;
  tags: string[];
  groups: string[];
  slug: string;
}

const { title, description, date, youtubeId, image, tags, groups, slug } = Astro.props;
const formattedDate = date.toLocaleDateString('zh-TW', {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
});
---

<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
  {youtubeId ? (
    <div class="aspect-video">
      <iframe 
        class="w-full h-full"
        src={`https://www.youtube.com/embed/${youtubeId}`}
        title={title}
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowfullscreen>
      </iframe>
    </div>
  ) : image ? (
    <img src={image} alt={title} class="w-full h-48 object-cover" />
  ) : (
    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
      <span class="text-gray-500">無圖片</span>
    </div>
  )}
  
  <div class="p-4">
    <h3 class="text-lg font-semibold mb-2">{title}</h3>
    {description && <p class="text-gray-600 mb-3 line-clamp-2">{description}</p>}
    
    <div class="flex flex-wrap gap-1 mb-2">
      {tags.map((tag: string) => (
        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">{tag}</span>
      ))}
    </div>
    
    <div class="flex flex-wrap gap-1 mb-3">
      {groups.map((group: string) => (
        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">{group}</span>
      ))}
    </div>
    
    <div class="flex justify-between items-center">
      <span class="text-gray-500 text-sm">{formattedDate}</span>
      <a href={`/media/${slug}`} class="text-blue-600 hover:underline text-sm">
        查看詳情
      </a>
    </div>
  </div>
</div> 