---
interface Props {
  count: number;
  reasonId: string;
  alreadyVoted?: boolean;
}

const { count, reasonId, alreadyVoted = false } = Astro.props;
---

<div class="vote-button-container" data-reason-id={reasonId} data-voted={alreadyVoted ? "true" : "false"}>
  <button 
    class={`vote-button group relative inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 ${alreadyVoted ? 'bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400' : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-red-50 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400'}`}
  >
    <span class={`transform transition-transform duration-300 ${alreadyVoted ? 'scale-110' : 'group-hover:scale-110'}`}>
      {alreadyVoted ? 
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
        </svg> :
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
        </svg>
      }
    </span>
    <span class="font-medium vote-count">{count}</span>
    <span class="absolute -top-1 -right-1 opacity-0 transition-opacity duration-300 ease-in-out vote-notification">
      <span class="flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-[10px] font-bold text-white">+1</span>
    </span>
  </button>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const voteButtons = document.querySelectorAll('.vote-button-container');
    
    voteButtons.forEach(container => {
      const button = container.querySelector('button');
      const countElement = container.querySelector('.vote-count');
      const notification = container.querySelector('.vote-notification');
      const reasonId = container.getAttribute('data-reason-id');
      let voted = container.getAttribute('data-voted') === 'true';
      
      if (!button || !countElement || !reasonId) return;
      
      button.addEventListener('click', () => {
        if (voted) return;
        
        // 更新UI
        voted = true;
        container.setAttribute('data-voted', 'true');
        
        // 更新按鈕外觀
        button.classList.remove('bg-gray-100', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300', 'hover:bg-red-50', 'dark:hover:bg-red-900/30', 'hover:text-red-600', 'dark:hover:text-red-400');
        button.classList.add('bg-red-50', 'dark:bg-red-900/30', 'text-red-600', 'dark:text-red-400');
        
        // 更新圖標
        const svgContainer = button.querySelector('span:first-child');
        if (svgContainer) {
          svgContainer.classList.add('scale-110');
          svgContainer.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
          </svg>`;
        }
        
        // 更新計數
        let count = parseInt(countElement.textContent || '0', 10);
        count += 1;
        countElement.textContent = count.toString();
        
        // 顯示通知
        if (notification) {
          notification.classList.remove('opacity-0');
          notification.classList.add('opacity-100');
          
          setTimeout(() => {
            notification.classList.remove('opacity-100');
            notification.classList.add('opacity-0');
          }, 1500);
        }
        
        // 在實際應用中，這裡會發送POST請求到伺服器
        console.log(`Voted for reason: ${reasonId}`);
        
        // 模擬API請求
        // fetch('/api/reasons/vote', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({ reasonId })
        // })
        // .then(response => response.json())
        // .then(data => console.log('Vote recorded:', data))
        // .catch(error => console.error('Error recording vote:', error));
      });
    });
  });
</script> 