---
import Layout from '../layouts/Layout.astro';
import YouTubeVideo from '../components/YouTubeVideo.astro';
import VideoCarousel from '../components/VideoCarousel.astro';
import { getCollection } from 'astro:content';

// 從 TinaCMS 獲取立委數據
const tinaCmsLegislators = await getCollection('legislators');

// 從 TinaCMS 獲取媒體內容
const allMedia = await getCollection('media');

// 從媒體中提取所有影片
const allVideos = allMedia
  .filter(item => (item.data.youtubeId || item.data.videoUrl || (typeof item.data.image === 'string' && item.data.image.match(/\.(mp4|webm|ogg)$/i))) && item.data.groups && item.data.groups.some(group => 
    // 過濾所有包含立委名字的媒體組（假設立委名字被用作媒體分組）
    tinaCmsLegislators.some(legislator => group === legislator.data.name)
  ))
  .map(item => {
    // 找出關聯的立委
    const relatedGroup = item.data.groups.find(group => 
      tinaCmsLegislators.some(legislator => group === legislator.data.name)
    ) || '';
    const relatedLegislator = tinaCmsLegislators.find(legislator => 
      relatedGroup === legislator.data.name
    );
    
    // 检查image字段是否为MP4视频
    const image = item.data.image || '';
    const isVideoImage = typeof image === 'string' && image.match(/\.(mp4|webm|ogg)$/i);
    const videoUrl = item.data.videoUrl || (isVideoImage ? image : '');
    
    return {
      id: item.id,
      title: item.data.title,
      youtubeId: item.data.youtubeId || '',
      videoUrl: videoUrl,
      description: item.data.description || '',
      date: item.data.date ? item.data.date.toISOString().split('T')[0] : '',
      tags: item.data.tags || [],
      legislatorName: relatedLegislator ? relatedLegislator.data.name : '',
      legislatorParty: relatedLegislator ? relatedLegislator.data.party : '',
      legislatorImageUrl: relatedLegislator && relatedLegislator.data.imageUrl 
        ? relatedLegislator.data.imageUrl 
        : relatedLegislator ? `/legislators/${relatedLegislator.data.name}.jpg` : ''
    };
  })
  .sort((a, b) => new Date(b.date || '').getTime() - new Date(a.date || '').getTime());

// Get TinaCMS memes content
const allMemes = await getCollection('memes');

// Get all unique tags
const allTags = Array.from(new Set(allVideos.flatMap(video => video.tags)));

// Format date
function formatDate(dateString: string | Date | undefined) {
  if (!dateString) return '';
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return new Intl.DateTimeFormat('zh-TW', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
}

// Define Meme type
type Meme = {
  id: string;
  title: string;
  imageUrl: string;
  videoUrl?: string;
  mediaType?: 'image' | 'video';
  description: string;
  legislatorNames: string[];
  tags: string[];
};

// Filter media by group
const recallSongs = allMedia
  .filter(item => item.data.groups.includes('罷免金曲'))
  .sort((a, b) => {
    // 直接按日期排序，不考慮 orderID
    const dateA = a.data.date ? new Date(a.data.date).getTime() : 0;
    const dateB = b.data.date ? new Date(b.data.date).getTime() : 0;
    // 升序排列 - 舊到新 (ASC)
    return dateA - dateB;
  })
  .map(item => {
    // 检查image字段是否为MP4视频
    const image = item.data.image || '';
    const isVideoImage = typeof image === 'string' && image.match(/\.(mp4|webm|ogg)$/i);
    const videoUrl = item.data.videoUrl || (isVideoImage ? image : '');
    
    return {
      id: item.id,
      title: item.data.title,
      youtubeId: item.data.youtubeId || '',
      videoUrl: videoUrl,
      description: item.data.description || '',
      date: item.data.date ? item.data.date.toISOString().split('T')[0] : '',
      tags: item.data.tags || [],
      orderID: item.data.orderID
    };
  })
  // 直接反轉數組，確保能看到明顯的順序變化
  .reverse();

console.log('罷免金曲排序結果 (按日期升序後完全反轉):', recallSongs.map(item => ({ 
  title: item.title, 
  date: item.date,
  youtubeId: item.youtubeId,
  videoUrl: item.videoUrl
})));

// 罷免每日一字資料
const recallWords = allMedia
  .filter(item => item.data.groups.includes('罷免每日一字'))
  .sort((a, b) => {
    // 直接按日期排序，不考慮 orderID
    const dateA = a.data.date ? new Date(a.data.date).getTime() : 0;
    const dateB = b.data.date ? new Date(b.data.date).getTime() : 0;
    // DESC
    return dateB - dateA;
  })
  .map(item => {
    // 检查image字段是否为MP4视频
    const image = item.data.image || '';
    const isVideoImage = typeof image === 'string' && image.match(/\.(mp4|webm|ogg)$/i);
    const videoUrl = item.data.videoUrl || (isVideoImage ? image : '');
    
    return {
      id: item.id,
      title: item.data.title,
      youtubeId: item.data.youtubeId || '',
      videoUrl: videoUrl,
      description: item.data.description || '',
      date: item.data.date ? item.data.date.toISOString().split('T')[0] : '',
      tags: item.data.tags || [],
      orderID: item.data.orderID
    };
  });

console.log('罷免每日一字排序結果 (按日期升序):', recallWords.map(item => ({ 
  title: item.title, 
  date: item.date 
})));

// 精彩影片集錦
const highlightVideos = allMedia
  .filter(item => item.data.groups.includes('精彩影片集錦'))
  .sort((a, b) => {
    if (a.data.orderID !== undefined && b.data.orderID !== undefined) {
      return a.data.orderID - b.data.orderID;
    }
    // 處理日期可能為 undefined 的情況
    const dateA = a.data.date ? new Date(a.data.date).getTime() : 0;
    const dateB = b.data.date ? new Date(b.data.date).getTime() : 0;
    return dateB - dateA;
  });

// 使用TinaCMS梗圖內容
const memesFromContent = allMemes.map(meme => ({
  id: meme.id,
  title: meme.data.title,
  imageUrl: meme.data.imageUrl,
  videoUrl: meme.data.videoUrl || '',
  mediaType: meme.data.mediaType || 'image',
  description: meme.data.description || '',
  legislatorNames: meme.data.legislatorNames || [],
  tags: meme.data.tags || []
}));

// 如果沒有梗圖內容，使用示例數據
const sampleMemes: Meme[] = [
  {
    id: "meme-1",
    title: "立委上班時間",
    imageUrl: "/memes/meme1.jpg",
    videoUrl: "",
    mediaType: "image",
    description: "立法院開會時間，卻只見空蕩蕩的議場",
    legislatorNames: ["葉元之", "林沛祥"],
    tags: ["出席率", "怠惰"]
  },
  {
    id: "meme-2",
    title: "預算審查現場",
    imageUrl: "/memes/meme2.jpg",
    videoUrl: "",
    mediaType: "image",
    description: "預算審查關鍵時刻，立委卻在滑手機",
    legislatorNames: ["林沛祥"],
    tags: ["預算審查", "不專心"]
  },
  {
    id: "meme-3",
    title: "質詢現場vs.臉書發文",
    imageUrl: "/memes/meme3.jpg",
    videoUrl: "",
    mediaType: "image",
    description: "質詢時說一套，臉書發文又是另一套",
    legislatorNames: ["林沛祥", "王建銘"],
    tags: ["選前選後不一", "言行不一"]
  }
];

// 使用TinaCMS梗圖或示例梗圖
const displayMemes: Meme[] = memesFromContent.length > 0 ? memesFromContent.reverse() : sampleMemes.reverse();
---

<Layout title="歡樂罷 | 100個罷免的理由">
  <div class="relative scroll-focus">
    <!-- 背景裝飾 -->
    <div class="absolute top-0 left-0 w-full h-full -z-10 overflow-hidden">
      <div class="absolute -top-24 -right-20 w-96 h-96 bg-purple-200/30 dark:bg-purple-900/10 rounded-full blur-3xl"></div>
      <div class="absolute top-1/2 -left-20 w-80 h-80 bg-pink-200/30 dark:bg-pink-900/10 rounded-full blur-3xl"></div>
    </div>
    
    <div class="container mx-auto px-4 py-8">
      <div class="mb-8">
        <div class="text-center mb-10">
          <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">歡樂罷</h1>
          <p class="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">收集了35位不適任立委的精彩「表現」，讓我們來瞧瞧他們的「金句」與「名場面」。</p>
        </div>
        
        <!-- 罷免金曲 -->
        <div class="mb-12">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-2 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
            </svg>
            罷免金曲
          </h2>
          
          <VideoCarousel 
            videos={recallSongs} 
            formatDate={formatDate}
            carouselId="recall-songs-carousel"
          />
        </div>
        
        <!-- 罷免每日一字 -->
        <div class="mb-12">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            罷免每日一字
          </h2>
          
          <VideoCarousel 
            videos={recallWords} 
            formatDate={formatDate}
            carouselId="recall-words-carousel"
            itemsPerView={3}
          />
        </div>
        
        <!-- 影片區域 -->
        <div class="mb-12">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-2 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            精彩影片集錦
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
            {highlightVideos.map(video => (
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div class="relative">
                  <YouTubeVideo 
                    videoId={video.data.youtubeId || ''} 
                    title={video.data.title} 
                  />
                </div>
                <div class="p-4">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{video.data.title}</h3>
                  {video.data.description && (
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3 min-h-[2.5rem] overflow-hidden">
                      <span class="line-clamp-2">{video.data.description}</span>
                    </p>
                  )}
                  <div class="flex flex-wrap gap-2 mb-3">
                    {video.data.tags.map((tag: string) => (
                      <span class="bg-indigo-100 dark:bg-indigo-800/40 text-indigo-800 dark:text-indigo-200 px-2 py-0.5 rounded-full text-xs">
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <!-- 梗圖區域 -->
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-2 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            大罷免梗圖
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="meme-gallery">
            {displayMemes.map((meme, index) => (
              <div class={`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 group meme-item ${index >= 6 ? 'hidden' : ''}`} data-index={index}>
                <div class="relative cursor-pointer">
                  {meme.videoUrl || (meme.imageUrl && meme.imageUrl.match(/\.(mp4|webm|ogg)$/i)) ? (
                    <video 
                      src={meme.videoUrl || meme.imageUrl} 
                      poster={meme.imageUrl && !meme.imageUrl.match(/\.(mp4|webm|ogg)$/i) ? meme.imageUrl : undefined}
                      muted
                      preload="metadata"
                      class="w-full h-auto aspect-[4/3] object-cover group-hover:scale-105 transition-transform duration-500"
                      onmouseover="this.play()"
                      onmouseout="this.pause(); this.currentTime=0;"
                      data-media-type="video"
                    >
                      您的瀏覽器不支援影片播放
                    </video>
                  ) : (
                    <img 
                      src={meme.imageUrl} 
                      alt={meme.title} 
                      class="w-full h-auto aspect-[4/3] object-cover group-hover:scale-105 transition-transform duration-500"
                      onerror="this.src='/favicon.svg'; this.onerror=null;"
                      loading="lazy"
                      data-media-type="image"
                    />
                  )}
                  <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                    <span class="text-white px-4 py-2 rounded-full bg-gray-900/60 text-sm flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      點擊放大
                    </span>
                  </div>
                </div>
                <div class="p-4">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{meme.title}</h3>
                  <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">{meme.description}</p>
                  <div class="flex flex-wrap gap-x-2 gap-y-1 mb-3">
                    {meme.legislatorNames.map(name => (
                      <a href={`/legislator/${name}`} class="text-indigo-600 dark:text-indigo-400 hover:underline text-sm">
                        #{name}
                      </a>
                    ))}
                  </div>
                  <div class="flex flex-wrap gap-2">
                    {meme.tags.map(tag => (
                      <span class="bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300 px-2 py-0.5 rounded-full text-xs">
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {displayMemes.length > 6 && (
            <div id="meme-loader" class="text-center mt-8 py-4">
              <div class="animate-spin mx-auto h-8 w-8 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
              <p class="mt-2 text-gray-600 dark:text-gray-400">載入更多中...</p>
            </div>
          )}
        </div>
        
      
        <!-- 立委列表按鈕 -->
        <div class="max-w-7xl mx-auto text-center mt-12 mb-8">
          <a href="/legislators" class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-gradient-to-r from-red-600 to-orange-500 rounded-xl shadow-lg hover:from-red-700 hover:to-orange-600 transition-all duration-300 transform hover:scale-105">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            立委列表 - 來看看你選區立委那些精彩絕倫的罷免理由吧！
          </a>
        </div>
        <!-- 用戶投稿區 -->
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-600 dark:to-purple-700 rounded-lg shadow-lg p-6 text-white">
          <h3 class="text-xl font-bold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            投稿你的發現
          </h3>
          <p class="mb-4 text-white/90">發現更多立委的有趣表現？歡迎投稿分享給更多人！</p>
          <a href="https://docs.google.com/forms/d/e/1FAIpQLSdhu6ff2T0l3zEtKdXqYDkkMWSKwI5_Os32ybatuBUGAd7amA/viewform" target="_blank" rel="noopener noreferrer" class="inline-block bg-white text-indigo-600 hover:bg-indigo-50 px-5 py-2 rounded-lg font-medium transition-colors duration-300 transform hover:-translate-y-1 hover:shadow-md">
            我要投稿
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>

<style>
  /* 影片覆蓋層樣式，確保完全覆蓋並能接收點擊事件 */
  .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    cursor: pointer;
  }
  
  /* 移除阻止iframe點擊的樣式 */
  /* .aspect-video iframe {
    pointer-events: none;
  } */
  
  /* 捲動聚焦效果 */
  :root {
    /* 控制 fade 區高度 */
    --fade-size: 20vh;
  }

  /* 使用偽元素創建固定位置的漸變遮罩，這樣滾動時遮罩效果會保持不變 */
  .scroll-focus {
    position: relative;
  }
  
  /* 頂部和底部漸變偽元素 */
  .scroll-focus::before,
  .scroll-focus::after {
    content: "";
    position: fixed;
    left: 0;
    right: 0;
    height: var(--fade-size);
    pointer-events: none;
    z-index: 10;
  }
  
  /* 頂部漸變 - 使用較溫暖的顏色 */
  .scroll-focus::before {
    top: 0;
    background: linear-gradient(to bottom, 
      rgba(255, 252, 248, 0.95) 0%, 
      rgba(255, 252, 248, 0.7) 30%,
      rgba(255, 252, 248, 0.3) 70%,
      rgba(255, 252, 248, 0) 100%
    );
  }
  
  /* 深色模式 - 頂部漸變 */
  @media (prefers-color-scheme: dark) {
    .scroll-focus::before {
      background: linear-gradient(to bottom, 
      rgba(255, 252, 248, 0.95) 0%, 
      rgba(255, 252, 248, 0.7) 30%,
      rgba(255, 252, 248, 0.3) 70%,
      rgba(255, 252, 248, 0) 100%
    );
    }
  }
  
  /* 底部漸變 - 使用較溫暖的顏色 */
  .scroll-focus::after {
    bottom: 0;
    background: linear-gradient(to top, 
      rgba(255, 252, 248, 0.95) 0%, 
      rgba(255, 252, 248, 0.7) 30%,
      rgba(255, 252, 248, 0.3) 70%,
      rgba(255, 252, 248, 0) 100%
    );
  }
  
  /* 深色模式 - 底部漸變 */
  @media (prefers-color-scheme: dark) {
    .scroll-focus::after {
      background: linear-gradient(to top, 
      rgba(255, 252, 248, 0.95) 0%, 
      rgba(255, 252, 248, 0.7) 30%,
      rgba(255, 252, 248, 0.3) 70%,
      rgba(255, 252, 248, 0) 100%
    );
    }
  }
</style>

<!-- 媒體檢視器彈窗 -->
<div id="media-modal" class="fixed inset-0 bg-black bg-opacity-80 z-50 hidden flex items-center justify-center">
  <div class="relative w-full max-w-5xl mx-auto p-4">
    <!-- 關閉按鈕 -->
    <button id="close-modal" class="absolute top-2 right-2 text-white bg-red-600 rounded-full p-2 z-10">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    
    <!-- 媒體容器 -->
    <div class="media-container bg-gray-900 rounded-lg overflow-hidden">
      <!-- 圖片/影片將在這裡顯示 -->
      <div id="modal-media-display" class="relative flex items-center justify-center min-h-[50vh]">
        <img id="modal-image" class="max-h-[80vh] max-w-full object-contain hidden" src="" alt="" />
        <div id="modal-video-container" class="hidden w-full aspect-video">
          <iframe id="modal-video" class="w-full h-full" frameborder="0" allowfullscreen></iframe>
        </div>
        <!-- 加載指示器 -->
        <div id="loading-indicator" class="text-white text-center">
          <svg class="animate-spin h-10 w-10 mx-auto mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p>載入中...</p>
        </div>
      </div>
      
      <!-- 說明文字 -->
      <div class="bg-gray-800 p-4">
        <p id="modal-caption" class="text-white text-center"></p>
      </div>
    </div>
    
    <!-- 導航按鈕 -->
    <div class="absolute inset-y-0 left-0 flex items-center">
      <button id="prev-media" class="bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
    </div>
    <div class="absolute inset-y-0 right-0 flex items-center">
      <button id="next-media" class="bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const mediaModal = document.getElementById('media-modal');
    const modalImage = document.getElementById('modal-image') as HTMLImageElement;
    const modalVideoContainer = document.getElementById('modal-video-container');
    const modalVideo = document.getElementById('modal-video') as HTMLIFrameElement;
    const modalCaption = document.getElementById('modal-caption');
    const closeModalBtn = document.getElementById('close-modal');
    const prevBtn = document.getElementById('prev-media');
    const nextBtn = document.getElementById('next-media');
    const loadingIndicator = document.getElementById('loading-indicator');
    
    // 確保模態視窗元素存在
    if (!mediaModal || !modalImage || !modalVideoContainer || !modalVideo || !modalCaption) {
      console.error('無法找到所有必要的模態視窗元素');
      return;
    }
    
    // 梗圖畫廊
    const memeGallery = document.getElementById('meme-gallery');
    const memeItems = document.querySelectorAll('.meme-item');
    const memeLoader = document.getElementById('meme-loader');
    let currentIndex = 0;
    let currentlyLoading = false;
    let itemsPerBatch = 6;
    let currentBatch = 1; // 初始載入了第一批
    
    // 計算總共有多少批次的梗圖
    const totalBatches = Math.ceil(memeItems.length / itemsPerBatch);
    
    // 懶加載實現
    if (memeGallery && memeLoader && memeItems.length > itemsPerBatch) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !currentlyLoading && currentBatch < totalBatches) {
            loadNextBatch();
          }
        });
      }, { rootMargin: '100px' });
      
      // 觀察載入指示器元素
      observer.observe(memeLoader);
      
      // 載入下一批梗圖
      function loadNextBatch() {
        if (currentlyLoading) return;
        
        currentlyLoading = true;
        const startIndex = currentBatch * itemsPerBatch;
        const endIndex = Math.min(startIndex + itemsPerBatch, memeItems.length);
        
        setTimeout(() => {
          // 顯示下一批梗圖
          for (let i = startIndex; i < endIndex; i++) {
            memeItems[i].classList.remove('hidden');
          }
          
          currentBatch++;
          currentlyLoading = false;
          
          // 如果已加載所有梗圖，隱藏載入器
          if (currentBatch >= totalBatches && memeLoader) {
            memeLoader.style.display = 'none';
          }
        }, 500); // 短暫延遲以展示載入動畫
      }
    }
    
    interface GalleryItem {
      index: number;
      type: 'image' | 'video' | 'youtube';
      src: string;
      youtubeId?: string;
      title: string;
      description: string;
      gallery: string;
    }
    
    let galleryItems: GalleryItem[] = [];

    // 從YouTube URL提取影片ID的函數
    function extractYouTubeId(url: string): string | null {
      if (!url) return null;
      
      // 如果已經是嵌入格式，直接提取ID
      if (url.includes('youtube.com/embed/')) {
        const parts = url.split('/');
        return parts[parts.length - 1].split('?')[0];
      }
      
      // 從常規URL提取視頻ID
      const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
      const match = url.match(regExp);
      
      return (match && match[2].length === 11) ? match[2] : null;
    }

    // 收集所有梗圖項目
    if (memeGallery) {
      const memeGalleryItems = Array.from(memeItems).map((item, index) => {
        const imgEl = item.querySelector('img') as HTMLImageElement;
        const videoEl = item.querySelector('video') as HTMLVideoElement;
        const title = item.querySelector('h3')?.textContent || '';
        const desc = item.querySelector('p')?.textContent || '';
        
        // 確定媒體類型和URL
        let mediaType: 'image' | 'video' = 'image';
        let mediaSrc = '';
        
        if (videoEl) {
          mediaType = 'video';
          mediaSrc = videoEl.src;
        } else if (imgEl) {
          mediaType = 'image';
          mediaSrc = imgEl.src;
        }
        
        return {
          index,
          type: mediaType,
          src: mediaSrc,
          title,
          description: desc,
          gallery: 'memes'
        } as GalleryItem;
      });
      
      galleryItems = [...galleryItems, ...memeGalleryItems];
    }
    
    // 收集所有 YouTube 影片
    const allYoutubeContainers = document.querySelectorAll('.video-container');
    
    if (allYoutubeContainers.length > 0) {
      const youtubeItems = Array.from(allYoutubeContainers).map((container, index) => {
        const videoId = container.getAttribute('data-video-id') || '';
        const videoUrl = container.getAttribute('data-video-url') || '';
        const title = container.closest('.bg-white, .bg-gray-50, .bg-gray-700')?.querySelector('h3')?.textContent || '';
        const desc = container.closest('.bg-white, .bg-gray-50, .bg-gray-700')?.querySelector('p')?.textContent || '';
        
        // 分配到對應的 gallery
        let gallery = 'videos';
        if (container.closest('#recall-songs-carousel')) {
          gallery = 'songs';
        } else if (container.closest('#recall-words-carousel')) {
          gallery = 'words';
        } else if (container.closest('.grid')) {
          gallery = 'highlights';
        }
        
        // 判断视频类型并处理同时存在youtubeId和videoUrl的情况
        let type: 'youtube' | 'video' = 'video';
        if (videoId && videoId.trim() !== '') {
          type = 'youtube';
        } else if (videoUrl && videoUrl.match(/\.(mp4|webm|ogg)$/i)) {
          type = 'video';
        }
        
        return {
          index,
          type: type,
          src: videoUrl || '',
          youtubeId: videoId || '',
          title,
          description: desc,
          gallery
        } as GalleryItem;
      });
      
      galleryItems = [...galleryItems, ...youtubeItems];
      
      // 添加所有覆蓋層的點擊事件
      document.querySelectorAll('.video-overlay').forEach((overlay) => {
        overlay.addEventListener('click', function(this: HTMLElement, e) {
          e.preventDefault();
          e.stopPropagation();
          
          // 找到容器
          const container = this.closest('.video-container');
          if (!container) return;
          
          // 獲取 videoId 或 videoUrl
          const videoId = container.getAttribute('data-video-id');
          const videoUrl = container.getAttribute('data-video-url');
          
          // 查找對應的索引
          const itemIndex = galleryItems.findIndex(item => 
            (videoId && item.youtubeId === videoId) || 
            (videoUrl && item.src === videoUrl)
          );
          
          if (itemIndex !== -1) {
            openMediaModal(itemIndex);
          }
        });
      });
      
      // 添加直接點擊容器的事件處理
      allYoutubeContainers.forEach((container) => {
        container.addEventListener('click', function(this: HTMLElement, e) {
          // 獲取 videoId 或 videoUrl
          const videoId = this.getAttribute('data-video-id');
          const videoUrl = this.getAttribute('data-video-url');
          
          // 查找對應的索引
          const itemIndex = galleryItems.findIndex(item => 
            (videoId && item.youtubeId === videoId) || 
            (videoUrl && item.src === videoUrl)
          );
          
          if (itemIndex !== -1) {
            openMediaModal(itemIndex);
          }
        });
      });
    }
    
    // 梗圖點擊事件
    memeItems.forEach((item, index) => {
      const imgContainer = item.querySelector('.relative');
      
      imgContainer?.addEventListener('click', function() {
        const actualIndex = galleryItems.findIndex(gi => 
          gi.gallery === 'memes' && gi.index === index
        );
        if (actualIndex !== -1) {
          openMediaModal(actualIndex);
        }
      });
    });
    
    // 打開媒體模態視窗
    function openMediaModal(index: number) {
      if (!mediaModal || galleryItems.length === 0) return;
      
      currentIndex = index;
      showMedia(galleryItems[currentIndex]);
      
      // 顯示模態視窗
      mediaModal.classList.remove('hidden');
      mediaModal.style.display = 'flex';
      
      // 更新導覽按鈕狀態
      updateNavigationButtons();
    }
    
    // 顯示媒體
    function showMedia(item: GalleryItem) {
      if (!modalImage || !modalVideoContainer || !modalCaption) return;
      
      console.log('Showing media:', {
        type: item.type,
        youtubeId: item.youtubeId,
        src: item.src,
        title: item.title
      });
      
      // 隱藏所有媒體容器，顯示加載中
      modalImage.classList.add('hidden');
      modalVideoContainer.classList.add('hidden');
      if (loadingIndicator) loadingIndicator.classList.remove('hidden');
      
      // 停止所有先前的媒體播放
      stopAllMedia();
      
      if (item.type === 'image') {
        // 顯示圖片
        modalImage.src = item.src;
        modalImage.onload = function() {
          modalImage.classList.remove('hidden');
          if (loadingIndicator) loadingIndicator.classList.add('hidden');
        };
        modalImage.onerror = function() {
          if (loadingIndicator) loadingIndicator.classList.add('hidden');
        };
        
        // 設置說明文字
        modalCaption.textContent = item.title + (item.description ? ' - ' + item.description : '');
      } else if (item.type === 'youtube' && item.youtubeId && item.youtubeId.trim() !== '') {
        console.log('处理YouTube视频:', item.youtubeId);
        // YouTube影片處理
        if (!modalVideo) {
          console.error('无法找到modalVideo元素');
          return;
        }
        
        try {
          const iframe = document.createElement('iframe');
          iframe.src = `https://www.youtube.com/embed/${item.youtubeId}?autoplay=1`;
          iframe.title = item.title;
          iframe.setAttribute('frameborder', '0');
          iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture');
          iframe.setAttribute('allowfullscreen', 'true');
          iframe.className = 'w-full h-full';
          
          modalVideoContainer.innerHTML = '';
          modalVideoContainer.appendChild(iframe);
          modalVideoContainer.classList.remove('hidden');
          if (loadingIndicator) loadingIndicator.classList.add('hidden');
          
          console.log('YouTube iframe已创建并添加到容器中');
        } catch (error) {
          console.error('创建YouTube iframe时出错:', error);
        }
        
        // 設置說明文字
        modalCaption.textContent = item.title + (item.description ? ' - ' + item.description : '');
      } else if (item.type === 'video' || (item.src && item.src.match(/\.(mp4|webm|ogg)$/i))) {
        console.log('处理本地视频:', item.src);
        // 本地影片檔 (.mp4, .webm 等)
        // 移除現有的video標籤 (如果有的話)
        const existingVideo = document.getElementById('modal-local-video');
        if (existingVideo) {
          existingVideo.remove();
        }
        
        try {
          // 創建新的video標籤
          const videoEl = document.createElement('video');
          videoEl.id = 'modal-local-video';
          videoEl.src = item.src;
          videoEl.controls = true;
          videoEl.autoplay = true;
          videoEl.className = 'w-full h-full max-h-[80vh] object-contain';
          
          // 清空並添加video標籤到容器中
          modalVideoContainer.innerHTML = '';
          modalVideoContainer.appendChild(videoEl);
          modalVideoContainer.classList.remove('hidden');
          if (loadingIndicator) loadingIndicator.classList.add('hidden');
          
          console.log('本地视频元素已创建并添加到容器中');
        } catch (error) {
          console.error('创建视频元素时出错:', error);
        }
        
        // 設置說明文字
        modalCaption.textContent = item.title + (item.description ? ' - ' + item.description : '');
      } else {
        console.error('未知媒体类型或缺少必要信息:', item);
        if (loadingIndicator) loadingIndicator.classList.add('hidden');
      }
    }
    
    // 停止所有媒體播放
    function stopAllMedia() {
      try {
        // 停止YouTube影片 - 移除iframe
        if (modalVideoContainer) {
          const iframe = modalVideoContainer.querySelector('iframe');
          if (iframe) {
            iframe.src = '';
            // 可选：完全移除iframe
            modalVideoContainer.innerHTML = '';
          }
        }
        
        // 停止本地影片
        const localVideo = document.getElementById('modal-local-video') as HTMLVideoElement;
        if (localVideo) {
          localVideo.pause();
          localVideo.src = '';
          localVideo.load();
        }
      } catch (error) {
        console.error('停止媒体播放时出错:', error);
      }
    }
    
    // 更新導覽按鈕狀態
    function updateNavigationButtons() {
      if (!prevBtn || !nextBtn) return;
      
      if (prevBtn instanceof HTMLButtonElement) {
        prevBtn.disabled = currentIndex <= 0;
      }
      
      if (nextBtn instanceof HTMLButtonElement) {
        nextBtn.disabled = currentIndex >= galleryItems.length - 1;
      }
      
      prevBtn.style.visibility = currentIndex <= 0 ? 'hidden' : 'visible';
      nextBtn.style.visibility = currentIndex >= galleryItems.length - 1 ? 'hidden' : 'visible';
    }
    
    // 導覽按鈕點擊事件
    if (prevBtn) {
      prevBtn.addEventListener('click', (e) => {
        e.preventDefault();
        if (currentIndex > 0) {
          // 先停止當前媒體播放
          stopAllMedia();
          
          currentIndex--;
          showMedia(galleryItems[currentIndex]);
          updateNavigationButtons();
        }
      });
    }
    
    if (nextBtn) {
      nextBtn.addEventListener('click', (e) => {
        e.preventDefault();
        if (currentIndex < galleryItems.length - 1) {
          // 先停止當前媒體播放
          stopAllMedia();
          
          currentIndex++;
          showMedia(galleryItems[currentIndex]);
          updateNavigationButtons();
        }
      });
    }
    
    // 鍵盤導覽
    document.addEventListener('keydown', function(e) {
      if (mediaModal && !mediaModal.classList.contains('hidden')) {
        if (e.key === 'ArrowLeft' && currentIndex > 0) {
          // 先停止當前媒體播放
          stopAllMedia();
          
          currentIndex--;
          showMedia(galleryItems[currentIndex]);
          updateNavigationButtons();
        } else if (e.key === 'ArrowRight' && currentIndex < galleryItems.length - 1) {
          // 先停止當前媒體播放
          stopAllMedia();
          
          currentIndex++;
          showMedia(galleryItems[currentIndex]);
          updateNavigationButtons();
        } else if (e.key === 'Escape') {
          closeModal();
        }
      }
    });
    
    // 關閉模態視窗
    function closeModal() {
      if (!mediaModal) return;
      
      mediaModal.classList.add('hidden');
      mediaModal.style.display = '';
      
      // 停止所有媒體播放
      stopAllMedia();
    }
    
    // 關閉按鈕
    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', closeModal);
    }
    
    // 點擊背景關閉
    if (mediaModal) {
      mediaModal.addEventListener('click', (e) => {
        if (e.target === mediaModal) {
          closeModal();
        }
      });
    }
    
    // 初始化
    console.log('初始化媒體瀏覽器，共收集項目：', galleryItems.length);
  });
</script> 