<script>
  // 媒體模態框功能
  function openMediaModal(items, startIndex) {
    const modal = document.getElementById('mediaModal');
    const mediaContainer = document.getElementById('mediaContainer');
    const captionText = document.getElementById('captionText');
    const closeBtn = document.querySelector('.close-button');
    
    if (!modal || !mediaContainer || !captionText || !closeBtn) return;
    
    let currentIndex = startIndex || 0;
    
    // 顯示第一個媒體
    showMedia(items[currentIndex]);
    
    // 顯示模態框
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden'; // 防止背景滾動
    
    // 關閉按鈕事件
    closeBtn.onclick = function() {
      modal.style.display = 'none';
      document.body.style.overflow = '';
      mediaContainer.innerHTML = '';
    };
    
    // 點擊模態框外部關閉
    modal.onclick = function(event) {
      if (event.target === modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
        mediaContainer.innerHTML = '';
      }
    };
    
    // 顯示媒體函數
    function showMedia(item) {
      mediaContainer.innerHTML = '';
      
      if (item.type === 'image') {
        const img = document.createElement('img');
        img.src = item.url;
        img.className = 'modal-media';
        mediaContainer.appendChild(img);
      } else if (item.type === 'video') {
        const iframe = document.createElement('iframe');
        iframe.src = item.url;
        iframe.className = 'modal-media';
        iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
        iframe.allowFullscreen = true;
        mediaContainer.appendChild(iframe);
      }
      
      captionText.textContent = item.caption || '';
    }
  }

  // ... existing code ...
  
  // 修正直接嵌入的iframe的點擊事件
  const iframes = document.querySelectorAll('iframe[src*="youtube.com/embed"]');
  iframes.forEach(iframe => {
    // 創建透明覆蓋層以捕捉點擊
    const overlay = document.createElement('div');
    overlay.style.position = 'absolute';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.cursor = 'pointer';
    overlay.style.zIndex = '10';
    
    const parent = iframe.parentElement;
    if (parent) {
      // 確保父元素有相對定位
      if (getComputedStyle(parent).position === 'static') {
        parent.style.position = 'relative';
      }
      
      parent.appendChild(overlay);
      
      // 添加點擊事件
      overlay.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const src = iframe.getAttribute('src');
        if (!src) return;
        
        // 尋找可能的標題
        let videoTitle = '';
        const parentCard = parent.closest('.video-carousel-item, .bg-white, .bg-gray-800');
        if (parentCard) {
          const titleEl = parentCard.querySelector('h3');
          if (titleEl) videoTitle = titleEl.textContent || '';
        }
        
        const mediaItem = {
          type: 'video',
          url: src,
          caption: videoTitle
        };
        
        openMediaModal([mediaItem], 0);
      });
    }
  });
</script> 