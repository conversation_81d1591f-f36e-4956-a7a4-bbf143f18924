---
import Layout from '../../../layouts/Layout.astro';
import fs from 'node:fs/promises';
import path from 'node:path';

// Define route parameter types
export async function getStaticPaths() {
  try {
    // Get all files in the controversies directory
    const imagesDirPath = path.join(process.cwd(), 'public', 'images', 'controversies');
    
    try {
      // Check if directory exists first
      await fs.access(imagesDirPath);
      
      const files = await fs.readdir(imagesDirPath);
      const imageFiles = files.filter(file => 
        file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.jpeg')
      );
    
      return imageFiles.map(filename => ({
        params: { filename },
        props: { filename }
      }));
    } catch (err) {
      // Directory doesn't exist yet, return empty array
      return [];
    }
  } catch (error) {
    console.error('Error generating static paths:', error);
    return [];
  }
}

const { filename } = Astro.params;

// Extract legislator name from filename if possible
let legislatorName = "立委";
try {
  // Format is controversy-{legislatorName}-{index}-{uniqueId}.png
  const parts = filename.split('-');
  if (parts.length > 1) {
    legislatorName = decodeURIComponent(parts[1]);
  }
} catch (e) {
  console.error('Error parsing filename:', e);
}

// Build image URL
const imageUrl = `/images/controversies/${filename}`;
const fullImageUrl = new URL(imageUrl, Astro.site || Astro.url.origin).toString();
const pageTitle = `${legislatorName}的爭議事項 | 100個罷免的理由`;
const pageDescription = `查看關於${legislatorName}的爭議事項，了解更多罷免理由。`;
---

<Layout 
  title={pageTitle}
  description={pageDescription}
  image={fullImageUrl}
  type="article"
>
  <div class="min-h-screen py-12 px-4 sm:px-6 flex flex-col items-center">
    <div class="container max-w-4xl mx-auto">
      <a href="/" class="inline-flex items-center text-red-600 hover:text-red-800 transition-colors duration-300 mb-6 group">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 transform group-hover:-translate-x-1 transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
        返回首頁
      </a>
      
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
        <div class="bg-gradient-to-r from-orange-500 to-red-600 px-6 py-3 flex justify-between items-center">
          <h1 class="text-xl font-bold text-white">立委爭議事項</h1>
          <a 
            href={`/legislators/${legislatorName}`} 
            class="text-white hover:text-white/80 text-sm flex items-center"
          >
            查看{legislatorName}的所有爭議
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>
        
        <div class="p-6">
          <div class="rounded-lg overflow-hidden shadow-lg mb-6">
            <img 
              src={imageUrl} 
              alt={`${legislatorName}的爭議事項`} 
              class="w-full h-auto"
              onerror="this.src='/favicon.svg'; this.onerror=null;"
            />
          </div>
          
          <div class="flex flex-col sm:flex-row gap-3">
            <a 
              href={imageUrl} 
              download
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              下載圖片
            </a>
            
            <button 
              id="share-button"
              class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
              </svg>
              分享此圖片
            </button>
            
            <a 
              href={`/legislators/${legislatorName}`}
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              罷免{legislatorName}
            </a>
          </div>
        </div>
      </div>
      
      <div class="mt-8 text-center">
        <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">
          在了解更多罷免理由，加入罷免行動
        </p>
        <a 
          href="/" 
          class="text-purple-600 dark:text-purple-400 hover:underline"
        >
          100個罷免的理由 - https://recall.islandcountry.tw/
        </a>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Share functionality
  document.getElementById('share-button')?.addEventListener('click', async () => {
    try {
      const shareData = {
        title: document.title,
        text: document.querySelector('meta[name="description"]')?.getAttribute('content') || '立委爭議事項',
        url: window.location.href
      };
      
      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        await navigator.clipboard.writeText(window.location.href);
        alert('連結已複製，請分享給您的朋友');
      }
    } catch (err) {
      console.error('分享失敗:', err);
    }
  });
</script> 