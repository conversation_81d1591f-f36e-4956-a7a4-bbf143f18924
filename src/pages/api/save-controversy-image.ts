import fs from 'node:fs/promises';
import path from 'node:path';
import { nanoid } from 'nanoid';

export const post = async ({ request }) => {
  try {
    // 從請求中獲取 Base64 圖片數據
    const body = await request.json();
    const { imageData, legislatorName, controversyIndex } = body;
    
    if (!imageData) {
      return new Response(
        JSON.stringify({
          success: false,
          message: '圖片數據不能為空',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    // 移除 data:image/png;base64, 前綴
    const base64Data = imageData.replace(/^data:image\/\w+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');
    
    // 生成文件名 - 使用 nanoid 確保唯一性
    const uniqueId = nanoid(10);
    const fileName = `controversy-${legislatorName || 'unknown'}-${controversyIndex || 0}-${uniqueId}.png`;
    const filePath = path.join(process.cwd(), 'public', 'images', 'controversies', fileName);
    
    // 確保目錄存在
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    
    // 寫入文件
    await fs.writeFile(filePath, buffer);
    
    // 返回文件的 URL
    const fileUrl = `/images/controversies/${fileName}`;
    
    return new Response(
      JSON.stringify({
        success: true,
        url: fileUrl,
        message: '圖片已成功保存',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('保存圖片失敗:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        message: '保存圖片失敗: ' + (error instanceof Error ? error.message : String(error)),
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}; 