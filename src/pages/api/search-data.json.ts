import { getCollection } from 'astro:content';

export async function GET() {
  try {
    // 獲取所有罷免理由文章
    const reasonEntries = await getCollection('reasons');
    
    console.log(`找到 ${reasonEntries.length} 個罷免理由文章`);
    
    // 將資料轉換為搜尋友好的格式
    const searchData = await Promise.all(
      reasonEntries.map(async (entry) => {
        try {
          // 直接從前置資料(frontmatter)提取內容
          const title = entry.data.title || '';
          const description = entry.data.description || '';
          
          // 從原始Markdown中提取內容，移除可能的錯誤代碼
          let rawBody = entry.body || '';
          
          // 移除任何可能的代碼錯誤訊息
          rawBody = rawBody
            .replace(/\(\.\.\.[^)]*\)\s*=>\s*\{[^}]*\}/g, '') // 移除箭頭函數
            .replace(/throw new[^;]*;/g, '')                  // 移除錯誤拋出語句
            .replace(/__vite_ssr_import_\d+__\.[^;]*;/g, '')   // 移除vite導入
            .replace(/AstroError[^;]*;/g, '')                 // 移除Astro錯誤
            .replace(/InvalidComponent[^;]*;/g, '')           // 移除無效組件錯誤
            .replace(/\s+/g, ' ')                            // 將多個空格合併為一個
            .trim();
          
          // 組合所有文本內容
          let bodyText = [
            title,           // 將標題加入內文以提高標題權重
            description,      // 加入描述
            rawBody           // 加入處理後的原始內容
          ].filter(Boolean).join(' ');
          
          // 生成摘要 - 優先使用描述，如果沒有就用內文的前200個字符
          const excerpt = description || bodyText.substring(0, 200);
          
          console.log(`處理 ${entry.id}: 內容長度=${bodyText.length}`);
          
          return {
            id: entry.id,
            slug: entry.slug,
            title: title,
            body: bodyText,
            excerpt: excerpt,
            publishDate: entry.data.publishDate,
            popularity: entry.data.popularity || 0
          };
        } catch (error) {
          console.error(`處理 ${entry.id} 時出錯:`, error);
          
          // 即使出錯也返回基本資料
          return {
            id: entry.id,
            slug: entry.slug,
            title: entry.data.title || '',
            body: entry.data.description || '',
            excerpt: entry.data.description || '',
            publishDate: entry.data.publishDate,
            popularity: entry.data.popularity || 0
          };
        }
      })
    );
    
    // 依照熱門程度排序
    searchData.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
    
    console.log(`總共產生 ${searchData.length} 筆搜尋資料`);
    
    return new Response(
      JSON.stringify(searchData),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'max-age=3600' // 快取 1 小時
        }
      }
    );
  } catch (error) {
    console.error('Error generating search data:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to generate search data' }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
} 