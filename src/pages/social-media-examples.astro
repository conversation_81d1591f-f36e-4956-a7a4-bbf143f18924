---
import Layout from '../layouts/Layout.astro';

// 替換成您的實際 ID
const youtubeId = 'bHHpQPkG8S8'; // 示例 YouTube ID
const facebookPostUrl = 'https://www.facebook.com/bobo52310/posts/pfbid0iwD9tAiPuSqXkCYv6V4zMh7JBu97iwFSfkKvkchUkFmLwZnVSVVQgPrGFor4m9vGl?__cft__%5B0%5D=AZXd2eXHXSEteSSqDcvNLM1xZQAMtcptFBzHCy-k0DC8DCloVfbvG5ium11rdrak5iYpknwO7OSIghTCPArNzE-bmhdJwBfssKe9mO-HQFMdS1-QtCCK_c1acYN-jy_fZmkeQxm6n7cK2q66Zz4ARWJL_E1KWbGS4TnI33Uym6EMyQ&__tn__=%252CO%252CP-R';
const instagramPostUrl = 'https://www.instagram.com/p/DHiXIg8vOL7';
// 更新為完整 Threads 網址
const threadsUrl = 'https://www.threads.net/@bobo52310/post/DH5GUglP52E';
---

<Layout title="社群媒體嵌入範例 - 100個罷免的理由">
  <div class="space-y-12 py-8">
    <section>
      <h2 class="text-2xl font-bold mb-4">YouTube 影片嵌入範例</h2>
      <div class="aspect-video w-full max-w-2xl mx-auto bg-gray-200 rounded-lg overflow-hidden">
        <iframe
          width="100%"
          height="100%"
          src={`https://www.youtube.com/embed/${youtubeId}`}
          title="YouTube video player"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen
          loading="lazy"
        ></iframe>
      </div>
      <div class="mt-4 bg-gray-100 dark:bg-gray-800 p-4 rounded-lg max-w-2xl mx-auto">
        <h3 class="font-medium mb-2">實作方式</h3>
        <div class="text-sm overflow-x-auto p-2 bg-gray-200 dark:bg-gray-700 rounded">
          <p>1. 在 Astro 頁面中直接使用 iframe 標籤：</p>
          <pre><code>{`<iframe
  width="100%"
  height="100%"
  src={\`https://www.youtube.com/embed/\${youtubeId}\`}
  title="YouTube video player"
  frameborder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowfullscreen
  loading="lazy"
></iframe>`}</code></pre>
        </div>
      </div>
    </section>

    <section>
      <h2 class="text-2xl font-bold mb-4">Facebook 貼文嵌入範例</h2>
      <div class="max-w-2xl mx-auto">
        <!-- Facebook SDK 載入 -->
        <script is:inline>
          window.fbAsyncInit = function() {
            // FB initialization - any global FB object will be available after SDK loads
            if (typeof window.FB !== 'undefined') {
              window.FB.init({
                xfbml: true,
                version: 'v19.0'
              });
            }
          };
          (function(d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) return;
            js = d.createElement(s); js.id = id;
            js.src = 'https://connect.facebook.net/zh_TW/sdk.js';
            fjs.parentNode.insertBefore(js, fjs);
          }(document, 'script', 'facebook-jssdk'));
        </script>
        
        <!-- Facebook 貼文嵌入 -->
        <div id="fb-root"></div>
        <div 
          class="fb-post" 
          data-href={facebookPostUrl}
          data-width="500" 
          data-show-text="true">
        </div>
      </div>
      <div class="mt-4 bg-gray-100 dark:bg-gray-800 p-4 rounded-lg max-w-2xl mx-auto">
        <h3 class="font-medium mb-2">實作方式</h3>
        <div class="text-sm overflow-x-auto p-2 bg-gray-200 dark:bg-gray-700 rounded">
          <p>1. 添加 Facebook SDK：</p>
          <pre><code>{`<script is:inline>
  window.fbAsyncInit = function() {
    if (typeof window.FB !== 'undefined') {
      window.FB.init({
        xfbml: true,
        version: 'v19.0'
      });
    }
  };
  (function(d, s, id) {
    var js, fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) return;
    js = d.createElement(s); js.id = id;
    js.src = 'https://connect.facebook.net/zh_TW/sdk.js';
    fjs.parentNode.insertBefore(js, fjs);
  }(document, 'script', 'facebook-jssdk'));
</script>`}</code></pre>

          <p class="mt-2">2. 添加 Facebook 貼文容器：</p>
          <pre><code>{`<div id="fb-root"></div>
<div 
  class="fb-post" 
  data-href={facebookPostUrl}
  data-width="500" 
  data-show-text="true">
</div>`}</code></pre>
        </div>
      </div>
    </section>

    <section>
      <h2 class="text-2xl font-bold mb-4">Instagram 貼文嵌入範例</h2>
      <div class="max-w-2xl mx-auto">
        <!-- Instagram 嵌入 -->
        <blockquote 
          class="instagram-media" 
          data-instgrm-permalink={instagramPostUrl}
          data-instgrm-version="14" 
          style="background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width:540px; min-width:326px; padding:0; width:99.375%; width:-webkit-calc(100% - 2px); width:calc(100% - 2px);">
        </blockquote>
        <script is:inline>
          (function(d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) return;
            js = d.createElement(s); js.id = id;
            js.src = "//www.instagram.com/embed.js";
            fjs.parentNode.insertBefore(js, fjs);
          }(document, 'script', 'instagram-embed-js'));
        </script>
      </div>
      <div class="mt-4 bg-gray-100 dark:bg-gray-800 p-4 rounded-lg max-w-2xl mx-auto">
        <h3 class="font-medium mb-2">實作方式</h3>
        <div class="text-sm overflow-x-auto p-2 bg-gray-200 dark:bg-gray-700 rounded">
          <p>1. 創建 Instagram 嵌入區塊：</p>
          <pre><code>{`<blockquote 
  class="instagram-media" 
  data-instgrm-permalink={instagramPostUrl}
  data-instgrm-version="14" 
  style="background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width:540px; min-width:326px; padding:0; width:99.375%; width:-webkit-calc(100% - 2px); width:calc(100% - 2px);">
</blockquote>`}</code></pre>

          <p class="mt-2">2. 載入 Instagram 嵌入腳本：</p>
          <pre><code>{`<script is:inline>
  (function(d, s, id) {
    var js, fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) return;
    js = d.createElement(s); js.id = id;
    js.src = "//www.instagram.com/embed.js";
    fjs.parentNode.insertBefore(js, fjs);
  }(document, 'script', 'instagram-embed-js'));
</script>`}</code></pre>
        </div>
      </div>
    </section>

    <section>
      <h2 class="text-2xl font-bold mb-4">Threads 貼文嵌入範例</h2>
      <div class="max-w-2xl mx-auto">
        <!-- Threads 替代方案：使用鏈接卡片 -->
        <div class="border rounded-lg overflow-hidden shadow-md bg-white dark:bg-gray-800 max-w-lg mx-auto">
          <div class="p-4">
            <div class="flex items-center mb-3">
              <img 
                src="https://pbs.twimg.com/profile_images/1677107042301767681/qur-h2zj_400x400.jpg" 
                alt="Threads Logo" 
                class="w-10 h-10 rounded-full mr-3" 
              />
              <div>
                <div class="font-semibold">@bobo52310</div>
                <div class="text-sm text-gray-500">在 Threads 上發佈</div>
              </div>
            </div>
            <p class="mb-3">很抱歉，Threads 不允許外部嵌入貼文。請點擊下方按鈕查看原始貼文。</p>
            <a 
              href={threadsUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              class="inline-block bg-gradient-to-r from-purple-500 to-pink-500 text-white py-2 px-4 rounded-md font-medium hover:from-purple-600 hover:to-pink-600 transition-colors"
            >
              在 Threads 上查看
            </a>
          </div>
        </div>
      </div>
      <div class="mt-4 bg-gray-100 dark:bg-gray-800 p-4 rounded-lg max-w-2xl mx-auto">
        <h3 class="font-medium mb-2">實作困難說明</h3>
        <div class="text-sm overflow-x-auto p-2 bg-gray-200 dark:bg-gray-700 rounded">
          <p class="font-medium text-red-600 dark:text-red-400">重要提示：Threads 目前不允許 iframe 嵌入！</p>
          <pre class="mt-2 p-2 bg-gray-300 dark:bg-gray-600 rounded text-xs">{`Refused to display 'https://www.threads.net/' in a frame because it set 'X-Frame-Options' to 'deny'`}</pre>
          
          <p class="mt-3">替代方案：</p>
          <ol class="list-decimal pl-5 space-y-1 mt-1">
            <li>提供直接鏈接到 Threads 貼文</li>
            <li>使用自定義卡片設計</li>
            <li>等待 Meta 提供官方嵌入功能</li>
          </ol>
          
          <p class="mt-3">嘗試過但無效的方式：</p>
          <pre><code>{`<iframe
  src={threadsUrl}
  width="100%"
  height="600"
  frameborder="0"
  scrolling="no"
  loading="lazy"
  class="w-full max-w-lg mx-auto"
></iframe>`}</code></pre>
        </div>
      </div>
    </section>

    <section>
      <h2 class="text-2xl font-bold mb-4">安全配置說明</h2>
      <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg max-w-2xl mx-auto">
        <h3 class="font-medium mb-2">CSP 安全設定</h3>
        <p class="mb-2">在 astro.config.mjs 中需要添加適當的 CSP 配置，以允許外部腳本和 iframe 的載入：</p>
        <p class="text-sm mb-2">• 針對 YouTube、Facebook、Instagram 和 Threads 添加對應的域名</p>
        <p class="text-sm mb-2">• 設定 script-src 和 frame-src 規則</p>
        <p class="text-sm mb-2">• 啟用必要的內聯腳本</p>
      </div>
    </section>
  </div>
</Layout> 