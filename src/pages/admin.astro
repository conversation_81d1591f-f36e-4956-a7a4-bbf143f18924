---
---

<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TinaCMS Admin</title>
  
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    .container {
      padding: 1rem;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    h1 {
      font-size: 2rem;
      margin-bottom: 2rem;
    }
    .button {
      display: inline-block;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      background-color: #2563eb;
      color: white;
      border: none;
      border-radius: 0.25rem;
      text-decoration: none;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .button:hover {
      background-color: #1d4ed8;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>TinaCMS 管理介面</h1>
    <a href="/admin/" class="button">進入 TinaCMS</a>
  </div>
  
  <script>
    // Redirect to TinaCMS admin if URL has /admin/ path
    if (window.location.pathname === '/admin/') {
      window.location.href = '/admin/index.html';
    }
  </script>
</body>
</html> 