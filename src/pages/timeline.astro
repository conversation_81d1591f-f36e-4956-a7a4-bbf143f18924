---
import Layout from '../layouts/Layout.astro';

const timelineEvents = [
  {
    date: '2024-04-01',
    title: '公民團體發起罷免連署',
    description: '因不滿立委表現，多個公民團體宣布發起罷免連署，要求立委對選民負責。',
    source: '自由時報，2024年4月1日',
    type: 'major'
  },
  {
    date: '2024-03-15',
    title: '立委集體缺席重要法案表決',
    description: '多位立委在重要法案表決時集體缺席，影響法案通過進程，引發公民團體抗議。',
    source: '中央通訊社，2024年3月15日',
    type: 'negative'
  },
  {
    date: '2024-03-10',
    title: '立委質詢不實內容',
    description: '多位立委在質詢中使用不實資料，誤導民眾對公共政策的理解。',
    source: '事實查核中心，2024年3月12日',
    type: 'negative'
  },
  {
    date: '2024-02-20',
    title: '立委集體護航財團利益',
    description: '在環保相關法案審議過程中，多位立委疑似為特定財團護航，提出對環境不利的修正案。',
    source: '環境報導，2024年2月20日',
    type: 'negative'
  },
  {
    date: '2024-02-15',
    title: '立委未出席地方選民服務',
    description: '多位立委當選後從未在地方舉辦選民服務，引發選民不滿。',
    source: '地方時報，2024年2月15日',
    type: 'negative'
  },
  {
    date: '2024-02-01',
    title: '立委國會出席率統計公布',
    description: '公民監督組織公布立委國會出席率統計，多位立委出席率不及格。',
    source: '公民監督網，2024年2月1日',
    type: 'info'
  },
  {
    date: '2024-01-20',
    title: '立委選舉結果公布',
    description: '中央選舉委員會公布立委選舉結果，35位立委當選。',
    source: '中央選舉委員會，2024年1月20日',
    type: 'info'
  }
];

// Sort by date (newest first)
timelineEvents.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
---

<Layout title="時間軸 | 100個罷免的理由">
  <div class="container mx-auto px-4 py-8">
    <div class="mb-12">
      <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">時間軸</h1>
      <p class="text-lg text-gray-600 dark:text-gray-400">
        按時間順序記錄與立委相關的爭議事件，讓問題一目了然。
      </p>
    </div>

    <div class="relative border-l-4 border-red-500 dark:border-red-600 ml-4 md:ml-8 pl-8 pb-8">
      {timelineEvents.map((event, index) => (
        <div class={`mb-12 relative ${index === 0 ? 'first' : ''}`}>
          <!-- Timeline dot -->
          <div class={`absolute -left-12 w-7 h-7 rounded-full flex items-center justify-center
            ${event.type === 'major' ? 'bg-red-600 dark:bg-red-500' : 
              event.type === 'negative' ? 'bg-yellow-500 dark:bg-yellow-600' : 
              'bg-blue-500 dark:bg-blue-600'}`}>
            <span class="block w-3 h-3 bg-white dark:bg-gray-800 rounded-full"></span>
          </div>
          
          <!-- Content -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="mb-3">
              <time datetime={event.date} class="text-sm text-gray-500 dark:text-gray-400 block mb-2">
                {new Date(event.date).toLocaleDateString('zh-TW')}
              </time>
              <h2 class="text-xl md:text-2xl font-bold text-gray-900 dark:text-white">{event.title}</h2>
            </div>
            
            <p class="text-gray-700 dark:text-gray-300 mb-4">{event.description}</p>
            
            <div class="text-sm text-gray-500 dark:text-gray-400 flex justify-between">
              <span>來源：{event.source}</span>
              
              <div class="flex items-center gap-4">
                <button class="inline-flex items-center text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-500">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                  </svg>
                  分享
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
</Layout> 