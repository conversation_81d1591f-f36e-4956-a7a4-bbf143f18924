---
import { getCollection, getEntry } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import CommonReasons from '../../components/CommonReasons.astro';
// 獲取URL參數
export async function getStaticPaths() {
  const roleEntries = await getCollection('roles');
  return roleEntries.map(entry => ({
    params: { id: entry.slug },
    props: { entry },
  }));
}

const { entry } = Astro.props;
const { Content } = await entry.render();

// 取得所有與此角色相關的罷免理由
const reasonEntries = await getCollection('reasons');
const relatedReasons = reasonEntries
  .filter(reason => reason.data.relatedRoles?.includes(entry.slug))
  .sort((a, b) => (b.data.popularity || 0) - (a.data.popularity || 0));

// 檢查 URL 參數是否包含來源立委資訊
const url = new URL(Astro.request.url);
const fromLegislator = url.searchParams.get('from');
---

<Layout title={`${entry.data.title}關心的罷免理由 | 100個罷免的理由`}>
  <div class="max-w-7xl mx-auto px-4 py-8">
    <div class="mb-8">
      {fromLegislator ? (
        <a href={`/legislator/${fromLegislator}`} class="inline-flex items-center text-red-600 hover:text-red-800 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          返回立委頁面
        </a>
      ) : (
        <a href="/" class="inline-flex items-center text-red-600 hover:text-red-800 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          返回首頁
        </a>
      )}
    </div>

    <!-- 角色資訊頭部 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden mb-8">
      <div class="p-6">
        <div class="flex items-center space-x-4">
          {entry.data.icon && (
            <div class="w-16 h-16 md:w-20 md:h-20 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
              {entry.data.icon === 'student' && (
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M12 14l9-5-9-5-9 5 9 5z" />
                  <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                </svg>
              )}
              {entry.data.icon === 'worker' && (
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              )}
              {entry.data.icon === 'parent' && (
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              {entry.data.icon === 'elder' && (
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              )}
              {entry.data.icon === 'kmt' && (
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                </svg>
              )}

            </div>
          )}
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              {entry.data.title}
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300">關心的罷免理由</p>
          </div>
        </div>
        
        {/* 角色描述內容 */}
        <div class="mt-6 prose dark:prose-invert max-w-none">
          <Content />
        </div>
      </div>
    </div>

    <!-- 罷免理由列表 -->
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
      共有 {relatedReasons.length} 個相關罷免理由
    </h2>

    <div class="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6">
      {relatedReasons.map(reason => (
        <div class="break-inside-avoid mb-6">
          <a href={`/reasons/${reason.slug}`} class="block hover:no-underline group cursor-pointer">
            <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 h-full group-hover:ring-2 group-hover:ring-red-500">
              {reason.data.media && reason.data.media.length > 0 && reason.data.media[0].type === 'image' && (
                <div class="relative aspect-video">
                  <img
                    src={reason.data.media[0].url}
                    alt={reason.data.title}
                    class="w-full h-full object-cover"
                  />
                  {reason.data.media.length > 1 && (
                    <div class="absolute bottom-0 right-0 bg-black bg-opacity-70 text-white px-2 py-1 text-xs rounded-tl-md">
                      <span>{reason.data.media.length} 個媒體</span>
                    </div>
                  )}
                </div>
              )}
              <div class="p-4">
                <div class="flex justify-between items-start mb-2">
                  <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                    {reason.data.title}
                  </h3>
                  <div class="flex items-center bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 px-2 py-1 rounded-full text-xs">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    {reason.data.popularity || 0}
                  </div>
                </div>
                <div class="text-gray-700 dark:text-gray-300 mb-4 line-clamp-3">
                  {reason.data.body ? (
                    <div set:html={reason.data.body.substring(0, 150) + (reason.data.body.length > 150 ? '...' : '')} />
                  ) : (
                    <p>查看詳細內容...</p>
                  )}
                </div>
                <a href={`/reasons/${reason.slug}`} class="reason-link-btn mt-2">
                  查看完整理由
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </a>
              </div>
            </div>
          </a>
        </div>
      ))}
    </div>

    {/* 若沒有找到相關理由 */}
    {relatedReasons.length === 0 && (
      <div class="bg-gray-100 dark:bg-gray-700 rounded-xl p-8 text-center">
        <h3 class="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">
          暫無相關罷免理由
        </h3>
        <p class="text-gray-500 dark:text-gray-400">
          目前還沒有與「{entry.data.title}」角色相關的罷免理由，請返回首頁查看其他內容。
        </p>
      </div>
    )}
  </div>

  <CommonReasons />
</Layout>

<style is:global>
  .reason-link-btn {
    @apply w-full flex items-center justify-end text-red-600 hover:text-red-800 font-medium transition-colors duration-300;
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
    gap: 0.5em;
  }
  .reason-link-btn svg {
    width: 1em;
    height: 1em;
    margin-left: 0.25em;
    flex-shrink: 0;
    vertical-align: middle;
  }
  .reason-link-btn:hover, .reason-link-btn:focus {
    outline: none;
    text-decoration: none;
  }
</style> 