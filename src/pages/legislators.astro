---
import Layout from '../layouts/Layout.astro';
import LegislatorCard from '../components/LegislatorCard.astro';
import { getCollection } from 'astro:content';

// Define regions
const regions = ["全區", "北北基", "桃竹苗", "台中", "彰投雲", "花東", "外島"];
const defaultRegion = "全區";

// 初始顯示立委數量
const initialDisplayCount = 12;

// Get legislators from TinaCMS
const tinaCmsLegislators = await getCollection('legislators');

// 將 TinaCMS 數據轉換成立委列表格式
const allLegislators = tinaCmsLegislators.map(legislator => {
	// 從 slug 中提取選區和地區信息
	// 先使用硬編碼的區域映射，之後可以在 TinaCMS 中加入區域欄位
	const getRegionFromDistrict = (district: string) => {
		if (district.includes('台北') || district.includes('基隆') || district.includes('新北')) return '北北基';
		if (district.includes('桃園') || district.includes('新竹') || district.includes('苗栗')) return '桃竹苗';
		if (district.includes('台中') || district.includes('臺中')) return '台中';
		if (district.includes('彰化') || district.includes('南投') || district.includes('雲林')) return '彰投雲';
		if (district.includes('花蓮') || district.includes('台東') || district.includes('臺東')) return '花東';
		if (district.includes('金門') || district.includes('連江') || district.includes('澎湖')) return '外島';
		return '全區';
	};

	return {
		id: legislator.slug,
		name: legislator.data.name,
		party: legislator.data.party,
		district: legislator.data.district,
		region: getRegionFromDistrict(legislator.data.district),
		imageUrl: legislator.data.imageUrl || `/legislators/${legislator.data.name}.jpg`,
		nickname: legislator.data.nicknames ? legislator.data.nicknames.join('\n') : '',
		// 目前罷免理由和影片是空的，稍後可以從其他集合中加入
		reasons: [] as {
			id: string;
			content: string;
			tags: string[];
			source?: string;
			date: string;
			votes: number;
		}[],
		orderID: legislator.data.orderID || 9999 // 如果有 orderID 則使用，否則給一個大數值排在後面
	};
});

// 按照 orderID 排序立委列表
allLegislators.sort((a, b) => (a.orderID || 9999) - (b.orderID || 9999));

// Create a mapping of legislator names to their TinaCMS imageUrl for later use
const tinaCmsImageMap = new Map();
tinaCmsLegislators.forEach(legislator => {
	if (legislator.data.imageUrl) {
		tinaCmsImageMap.set(legislator.data.name, legislator.data.imageUrl);
	}
});

// Create JSON string of TinaCMS image URLs for client-side use
const tinaCmsImageJson = JSON.stringify(Object.fromEntries(tinaCmsImageMap));
---

<Layout title="立委列表 - 100個罷免的理由">
	<div class="relative overflow-hidden">
		<!-- 背景裝飾 -->
		<div class="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-red-50 to-white dark:from-gray-900 dark:to-gray-950 -z-10 overflow-hidden">
			<div class="absolute -top-24 -right-20 w-96 h-96 bg-red-200 dark:bg-red-900/20 rounded-full blur-3xl"></div>
			<div class="absolute top-1/3 -left-20 w-80 h-80 bg-blue-200 dark:bg-blue-900/20 rounded-full blur-3xl"></div>
			<div class="absolute bottom-0 right-1/4 w-60 h-60 bg-yellow-200 dark:bg-yellow-900/20 rounded-full blur-3xl"></div>
		</div>
		
		<section class="relative pt-8 pb-12 px-4 sm:px-6 lg:px-8">
			<!-- 立委頁面頂部區塊 -->
			<div class="flex flex-col md:flex-row items-center gap-8 mb-12 max-w-screen-xl mx-auto">
				<!-- 標題和說明 -->
				<div class="md:w-1/2 text-left">
					<div class="flex items-center gap-4 mb-4">
						<div class="inline-block bg-red-100 dark:bg-red-900/30 p-2 rounded-lg">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
							</svg>
						</div>
						<h1 class="text-4xl md:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-red-700 via-red-500 to-orange-500 dark:from-red-500 dark:via-red-300 dark:to-orange-300 leading-tight">立委列表</h1>
					</div>
					<p class="text-base md:text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
						罷免理由百百種，你選區的最『精彩』？馬上深入了解！
					</p>
				</div>
				
				<!-- 動態顯示罷免理由區域 -->
				<div class="md:w-1/2 w-full">
					<div class="bg-black/90 dark:bg-gray-800/90 backdrop-blur-md rounded-xl p-6 shadow-2xl transform hover:translate-y-[-4px] transition-all duration-300 overflow-hidden">
						<div class="relative flex flex-col items-center">
							<!-- 理由顯示區域 -->
							<a id="legislator-link" href="/legislator/林沛祥" class="block w-full hover:bg-gray-800/50 rounded-lg transition-colors duration-300">
								<div id="reason-display" class="flex flex-col md:flex-row items-center gap-6 bg-black/60 rounded-lg p-4 md:p-6 w-full min-h-32 overflow-hidden">
									<div class="shrink-0">
										<img id="legislator-image" src="/legislators/linpeixiang.jpg" alt="立委照片" class="w-24 h-24 md:w-28 md:h-28 rounded-full border-4 border-red-500 object-cover shadow-lg" />
									</div>
									<div class="flex-1 text-left">
										<h3 id="legislator-name" class="text-xl md:text-2xl font-bold text-white mb-2">載入中...</h3>
										<div class="font-mono text-lg">
											<div class="relative text-green-400" id="scramble-text">正在載入罷免理由...</div>
										</div>
									</div>
								</div>
							</a>
							
							<div class="mt-4 flex items-center text-sm text-gray-400">
								<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
								</svg>
								<span id="reason-source" class="opacity-75">立委罷免理由</span>
							</div>
							
							<!-- 裝飾元素 -->
							<div class="absolute top-0 left-0 w-full h-full pointer-events-none">
								<div class="absolute top-0 left-1/4 w-1 h-1 bg-green-500 animate-ping"></div>
								<div class="absolute top-1/3 right-1/4 w-1 h-1 bg-green-500 animate-ping" style="animation-delay: 0.5s"></div>
								<div class="absolute bottom-1/3 left-1/2 w-1 h-1 bg-green-500 animate-ping" style="animation-delay: 1s"></div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div id="legislators" class="w-full max-w-screen-xl mx-auto">                
				<!-- 地區分類頁籤 -->
				<div class="mb-10 mt-4 bg-white/70 dark:bg-gray-800/70 rounded-xl shadow-lg p-4 sm:p-6 backdrop-blur-md overflow-x-auto">
					<div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-7 gap-2 sm:gap-3 min-w-[300px]">
						{regions.map((region, index) => (
							<button 
								class={`region-tab whitespace-nowrap px-2 py-2 sm:py-3 rounded-lg transition-all duration-300 font-medium border-2 relative overflow-hidden shadow-sm hover:shadow-md text-sm sm:text-base ${region === defaultRegion ? 'bg-gradient-to-r from-red-500 to-red-600 text-white border-red-500' : 'bg-white/90 dark:bg-gray-900/90 text-gray-800 dark:text-gray-200 border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/80'}`} 
								data-region={region}
								onclick="window.filterRegion(this.getAttribute('data-region'))"
							>
								{region === defaultRegion && (
									<span class="absolute inset-0 bg-red-500/20 dark:bg-red-500/10 animate-pulse-slow pointer-events-none"></span>
								)}
								<span class="relative z-10">
									{region}
								</span>
							</button>
						))}
					</div>
				</div>
				
				<!-- 立委卡片 Masonry 布局 -->
				<div id="legislators-container" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6 md:gap-8 px-2 sm:px-0">
					{allLegislators.map((legislator, index) => (
						<div class="legislator-card" data-region={legislator.region} style={index >= initialDisplayCount ? 'display: none;' : ''}>
							<LegislatorCard
								id={legislator.name}
								name={legislator.name}
								district={legislator.district}
								imageUrl={legislator.imageUrl}
								topReason={legislator.reasons.length > 0 ? legislator.reasons[0].content : ''}
								nickname={legislator.nickname}
								tinaCmsImageUrl={tinaCmsImageMap.get(legislator.name)}
							/>
						</div>
					))}
				</div>
				
				<!-- 無限滾動載入指示器 -->
				<div id="loading-indicator" class="flex justify-center items-center py-8 mt-6">
					<div class="animate-spin h-8 w-8 border-4 border-red-500 rounded-full border-t-transparent"></div>
				</div>
			</div>
		</section>
	</div>
</Layout>

<script>
	// 擴展 Window 接口
	declare global {
		interface Window {
			filterRegion: (region: string) => void;
		}
	}

	// 創建全局函數，用於區域按鈕點擊時調用
	window.filterRegion = function(region: string) {
		console.log("Filter region called:", region);
		
		// 首先更新按鈕樣式
		const allTabs = document.querySelectorAll('.region-tab');
		allTabs.forEach(tab => {
			// 重置所有按鈕樣式
			tab.classList.remove('bg-gradient-to-r', 'from-red-500', 'to-red-600', 'text-white', 'border-red-500');
			tab.classList.add('bg-white/90', 'dark:bg-gray-900/90', 'text-gray-800', 'dark:text-gray-200', 'border-gray-300', 'dark:border-gray-700', 'hover:bg-gray-50', 'dark:hover:bg-gray-800/80');
			
			// 移除脈衝動畫
			const pulseElement = tab.querySelector('.absolute');
			if (pulseElement) {
				pulseElement.remove();
			}
		});
		
		// 找到當前選中的按鈕並更新樣式
		const currentTab = Array.from(allTabs).find(tab => tab.getAttribute('data-region') === region);
		if (currentTab) {
			currentTab.classList.remove('bg-white/90', 'dark:bg-gray-900/90', 'text-gray-800', 'dark:text-gray-200', 'border-gray-300', 'dark:border-gray-700', 'hover:bg-gray-50', 'dark:hover:bg-gray-800/80');
			currentTab.classList.add('bg-gradient-to-r', 'from-red-500', 'to-red-600', 'text-white', 'border-red-500');
			
			// 添加脈衝動畫
			const contentSpan = currentTab.querySelector('.relative.z-10');
			if (contentSpan && !currentTab.querySelector('.absolute')) {
				const pulseSpan = document.createElement('span');
				pulseSpan.className = 'absolute inset-0 bg-red-500/20 dark:bg-red-500/10 animate-pulse-slow pointer-events-none';
				currentTab.insertBefore(pulseSpan, contentSpan);
			}
		}
		
		// 過濾立委卡片
		const cards = document.querySelectorAll('.legislator-card');
		cards.forEach(card => {
			const cardRegion = card.getAttribute('data-region') || "全區";
			if (region === "全區" || cardRegion === region) {
				(card as HTMLElement).style.display = '';
			} else {
				(card as HTMLElement).style.display = 'none';
			}
		});
	};
</script>

<script is:inline>
	// 全局變量，在文檔完全載入前就定義，確保立即可用
	window.legislators = {
		selectedRegion: "全區", // 預設顯示全部
		currentDisplayCount: 12, // 初始顯示數量
		incrementCount: 12, // 每次增加的數量
		isLoading: false, // 是否正在加載中
		
		// 初始化函數，將在 DOMContentLoaded 時調用
		init: function() {
			console.log('[INIT] initializing legislators module...');
			this.setupEventListeners();
			this.updateDisplay();
		},
		
		// 設置事件監聽器
		setupEventListeners: function() {
			// 設置滾動事件
			window.addEventListener('scroll', () => {
				const scrollPosition = window.innerHeight + window.scrollY;
				const documentHeight = document.body.offsetHeight;
				const scrollPercentage = (scrollPosition / documentHeight) * 100;
				
				if (scrollPercentage > 75 && !this.isLoading) {
					console.log('[SCROLL] Scroll threshold reached:', scrollPercentage.toFixed(2) + '%');
					this.loadMore();
				}
			});
		},
		
		// 過濾區域
		filterRegion: function(region) {
			console.log("[FILTER] Filter region called:", region);
			this.selectedRegion = region;
			this.currentDisplayCount = 12;
			this.isLoading = false;
			this.updateDisplay();
		},
		
		// 加載更多立委
		loadMore: async function() {
			const legislatorCards = document.querySelectorAll('.legislator-card');
			const matchingCards = Array.from(legislatorCards).filter(card => {
				const cardRegion = card.getAttribute('data-region') || "全區";
				return this.selectedRegion === "全區" || cardRegion === this.selectedRegion;
			});
			
			// 如果已經顯示了所有卡片或正在加載中，則返回
			if (this.currentDisplayCount >= matchingCards.length || this.isLoading) {
				console.log('[LOAD] No more legislators to load or already loading');
				return;
			}
			
			// 開始加載
			this.isLoading = true;
			console.log('[LOAD] Loading more legislators:', this.currentDisplayCount, '->', this.currentDisplayCount + this.incrementCount);
			
			// 模擬網絡延遲
			await new Promise(resolve => setTimeout(resolve, 500));
			
			// 增加顯示數量
			this.currentDisplayCount += this.incrementCount;
			
			// 更新顯示
			this.updateDisplay();
			
			// 完成加載
			this.isLoading = false;
		},
		
		// 更新顯示
		updateDisplay: function() {
			const legislatorCards = document.querySelectorAll('.legislator-card');
			const loadingIndicator = document.getElementById('loading-indicator');
			
			// 先隱藏所有卡片
			legislatorCards.forEach(card => {
				card.style.display = 'none';
			});
			
			// 獲取符合區域的所有卡片
			const matchingCards = Array.from(legislatorCards).filter(card => {
				const cardRegion = card.getAttribute('data-region') || "全區";
				return this.selectedRegion === "全區" || cardRegion === this.selectedRegion;
			});
			
			console.log(`[UPDATE] Found ${matchingCards.length} cards matching region ${this.selectedRegion}`);
			
			// 顯示符合條件的前 currentDisplayCount 個卡片
			matchingCards.slice(0, this.currentDisplayCount).forEach(card => {
				card.style.display = '';
			});
			
			// 檢查是否已加載所有卡片
			const allLoaded = this.currentDisplayCount >= matchingCards.length;
			
			// 根據是否已全部加載來顯示或隱藏加載指示器
			if (loadingIndicator) {
				loadingIndicator.style.display = allLoaded ? 'none' : 'flex';
			}
			
			console.log(`[STATUS] Region: ${this.selectedRegion}, Showing: ${Math.min(this.currentDisplayCount, matchingCards.length)}/${matchingCards.length}`);
		}
	};
	
	// 全局過濾區域函數，供按鈕調用
	window.filterRegion = function(region) {
		window.legislators.filterRegion(region);
	};
	
	// 在文檔結構加載完成後初始化
	document.addEventListener('DOMContentLoaded', function() {
		window.legislators.init();
	});
</script>

<script type="module" define:vars={{ tinaCmsImageJson, allLegislators }}>
	// Parse TinaCMS image URLs from server-side data
	const tinaCmsImageMap = new Map(Object.entries(JSON.parse(tinaCmsImageJson)));
	
	// 預設罷免理由列表
	const predefinedReasons = [
		"修法弱化民防配合中共降低開戰的代價",
		"藍白立委282次程序封殺國安法修正案！",
		"立三大惡法👉剝奪人民罷免及選舉權 毁憲滅國",
		"杯葛大法官提名、癱瘓憲法法庭"
	];
	
	// 隨機選擇一個立委
	function getRandomLegislator() {
		// 使用直接從伺服器傳遞的立委數據
		if (!allLegislators || allLegislators.length === 0) {
			return {
				name: '林沛祥',
				imageUrl: '/legislators/linpeixiang.jpg'
			};
		}
		
		const randomIndex = Math.floor(Math.random() * allLegislators.length);
		return allLegislators[randomIndex];
	}
	
	// 獲取隨機罷免理由
	function getRandomReason() {
		const randomIndex = Math.floor(Math.random() * predefinedReasons.length);
		return predefinedReasons[randomIndex];
	}
	
	// 生成隨機文字
	function generateScrambledText(length) {
		const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		let result = '';
		for (let i = 0; i < length; i++) {
			result += chars.charAt(Math.floor(Math.random() * chars.length));
		}
		return result;
	}
	
	// 顯示罷免理由動畫效果
	function displayReasonWithAnimation(reasonText, sourceText) {
		const reasonElement = document.getElementById('scramble-text');
		const reasonSourceElement = document.getElementById('reason-source');
		
		if (!reasonElement || !reasonSourceElement) return;
		
		// 清空現有內容
		reasonElement.textContent = '';
		reasonSourceElement.textContent = '';
		
		// 設置初始文字
		let scrambledText = generateScrambledText(reasonText.length);
		reasonElement.textContent = scrambledText;
		
		// 逐漸顯示正確文字
		let counter = 0;
		const interval = setInterval(() => {
			counter += 1;
			const newText = reasonText.substring(0, counter) + 
						   scrambledText.substring(counter);
			reasonElement.textContent = newText;
			
			if (counter >= reasonText.length) {
				clearInterval(interval);
				reasonSourceElement.textContent = sourceText;
			}
		}, 50);
	}
	
	// 顯示隨機立委與理由
	function displayRandomLegislatorAndReason() {
		// 獲取DOM元素
		const legislatorImage = document.getElementById('legislator-image');
		const legislatorName = document.getElementById('legislator-name');
		const legislatorLink = document.getElementById('legislator-link');
		
		if (!legislatorImage || !legislatorName || !legislatorLink) return;
		
		// 隨機選擇立委
		const randomLegislator = getRandomLegislator();
		
		// 更新立委資訊
		legislatorName.textContent = randomLegislator.name;
		legislatorImage.src = tinaCmsImageMap.get(randomLegislator.name) || randomLegislator.imageUrl;
		legislatorImage.alt = `${randomLegislator.name} 照片`;
		legislatorLink.href = `/legislator/${randomLegislator.name}`;
		
		// 隨機選擇罷免理由並顯示
		const randomReason = getRandomReason();
		displayReasonWithAnimation(randomReason, '公民提交');
	}
	
	// 網頁加載完成後即執行
	document.addEventListener('DOMContentLoaded', function() {
		// 馬上顯示第一個隨機立委和理由
		displayRandomLegislatorAndReason();
		
		// 每10秒更新一次
		setInterval(displayRandomLegislatorAndReason, 10000);
	});
	
	// 如果DOM已經加載完畢，立即執行
	if (document.readyState === 'complete' || document.readyState === 'interactive') {
		displayRandomLegislatorAndReason();
	}
</script> 