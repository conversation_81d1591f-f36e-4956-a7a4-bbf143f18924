---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import VoteButton from '../../components/VoteButton.astro';
import YouTubeVideo from '../../components/YouTubeVideo.astro';
import VideoCarousel from '../../components/VideoCarousel.astro';
import ControversyCarousel from '../../components/ControversyCarousel.astro';
import { getRecallTeam } from '../../utils/recallTeams';
import { getControversies } from '../../utils/getControversies';
import { marked } from 'marked';
import CommonReasons from '../../components/CommonReasons.astro';
// 引入圖片資產處理
import { getImage } from 'astro:assets';
// 用於日期時間處理
import * as fs from 'node:fs';
import * as path from 'node:path';
// Define Controversy type locally to avoid the import error
interface Controversy {
  title: string;
  content: string;
  reflections?: string[];
  tags?: string[];
}

// Define Legislator type
interface Legislator {
  name: string;
  party: string;
  district: string;
  imageUrl?: string;
  nicknames?: string[];
  orderID: number;
  reasons?: any[];
}

// 動態獲取立委圖片的函數 - 不使用動態 import，避免 build 問題
async function getLegislatorImage(name: string) {
  try {
    // 檢查檔案是否存在
    const imagePath = path.join(process.cwd(), 'src/assets/legislators', `${name}.jpg`);
    
    if (fs.existsSync(imagePath)) {
      // 不使用帶有中文的檔名，而是使用一個固定格式的英文檔名
      // 由於 Meta 調試器可能無法處理URL中的中文字元和查詢參數
      
      // 使用 Base64 編碼建立一個穩定的英文檔名 (移除 padding = 符號)
      const encodedName = Buffer.from(name).toString('base64').replace(/=/g, '');
      
      // 返回格式為 /og-images/legislator-{base64編碼}.jpg 的路徑
      // 這是一個純英文字元的路徑，不含查詢參數，對爬蟲更友好
      return `/og-images/legislator-${encodedName}.jpg`;
    }
    return null;
  } catch (error) {
    console.error(`無法載入 ${name} 的照片:`, error);
    return null;
  }
}

export async function getStaticPaths() {
  const legislators = await getCollection('legislators');
  
  return legislators.map(entry => {
    const legislator: Legislator = {
      name: entry.data.name,
      party: entry.data.party,
      district: entry.data.district,
      imageUrl: entry.data.imageUrl,
      nicknames: entry.data.nicknames || [],
      orderID: entry.data.orderID || 999,
      reasons: [] // Initialize with empty reasons
    };
    
    return {
      params: { name: legislator.name },
      props: { legislator },
    };
  });
}

interface Props {
  legislator: Legislator;
}

const { legislator } = Astro.props;

// Get recall team info
const recallTeam = getRecallTeam(legislator.name);

// Load controversies from individual file
let controversies: Controversy[] = [];
try {
  const result = await getControversies(legislator.name);
  controversies = result;
} catch (error) {
  console.error(`無法載入 ${legislator.name} 的爭議事項：`, error);
}

// Get TinaCMS media content related to this legislator
const allMedia = await getCollection('media');
const legislatorVideos = allMedia
  .filter(item => item.data.groups.includes(legislator.name))
  .sort((a, b) => {
    if (a.data.orderID !== undefined && b.data.orderID !== undefined) {
      return b.data.orderID - a.data.orderID;
    }
    // 處理日期可能為 undefined 的情況
    const dateA = a.data.date ? new Date(a.data.date).getTime() : 0;
    const dateB = b.data.date ? new Date(b.data.date).getTime() : 0;
    return dateB - dateA;
  })
  .map(item => ({
    id: item.id,
    title: item.data.title,
    youtubeId: item.data.youtubeId || '',
    description: item.data.description || '',
    date: item.data.date ? item.data.date.toISOString().split('T')[0] : '',
    tags: item.data.tags
  }))
  // 直接反轉數組，確保順序與之前相反
  .reverse();

// Get TinaCMS reasons related to this legislator
const allReasons = await getCollection('reasons');
const legislatorReasons = allReasons
  .filter(item => item.data.relatedLegislators && 
    (item.data.relatedLegislators.includes(legislator.name) || 
     item.data.relatedLegislators.includes('all')))
  .sort((a, b) => (b.data.popularity || 0) - (a.data.popularity || 0))
  .map(reason => {
    // 列印原始數據以進行診斷
    console.log(`Processing reason: ${reason.slug}`);
    console.log(`Body content: ${reason.body && reason.body.length > 0 ? 'Present' : 'Empty'}`);
    
    // 使用 marked 將 Markdown 轉換為 HTML
    const htmlBody = marked(reason.body || reason.data.body || '');
    
    return {
      id: reason.slug,
      title: reason.data.title,
      body: reason.body || '', // 直接使用 reason.body
      htmlBody: htmlBody, // 新增 htmlBody 欄位存儲轉換後的 HTML
      description: reason.data.description || '',
      media: reason.data.media || [],
      popularity: reason.data.popularity || 0,
      publishDate: reason.data.publishDate ? reason.data.publishDate.toISOString().split('T')[0] : '',
      tags: reason.data.tags || []
    };
  });

// Get legislator memes from CMS
const allMemes = await getCollection('memes');
const legislatorMemes = allMemes
  .filter(item => item.data.legislatorNames && item.data.legislatorNames.includes(legislator.name))
  .map(meme => ({
    id: meme.id,
    title: meme.data.title,
    imageUrl: meme.data.imageUrl,
    videoUrl: meme.data.videoUrl,
    mediaType: meme.data.mediaType,
    description: meme.data.description || '',
    tags: meme.data.tags || []
  }))
  .reverse();

// Get legislator content from CMS if available
let nicknames: string[] = [];
let legislatorImageUrl: string = legislator.imageUrl; // Default to the imageUrl from legislators.ts
let ogImageWithHash = ''; // 新增: 存儲包含版本 hash 的 OG 圖片 URL
let legislatorBody: string = ''; // 新增: 存儲立委的 body 內容

try {
  const legislatorsContent = await getCollection('legislators');
  const legislatorContent = legislatorsContent.find(item => item.data.name === legislator.name);
  
  // 嘗試獲取版本化的圖片 URL
  const versionedImageUrl = await getLegislatorImage(legislator.name);
  
  if (versionedImageUrl) {
    ogImageWithHash = versionedImageUrl; // 使用版本化的圖片 URL 作為 OG 圖片
  }
  
  if (legislatorContent) {
    if (legislatorContent.data.nicknames) {
      nicknames = legislatorContent.data.nicknames;
    }
    // Use imageUrl from MDX if available (僅用於頁面上顯示，不用於 OG)
    if (legislatorContent.data.imageUrl) {
      legislatorImageUrl = legislatorContent.data.imageUrl;
    }
    // 獲取 body 內容
    if (legislatorContent.body) {
      // 使用與首頁相同的方法處理 Markdown
      try {
        console.log('Original body content:', legislatorContent.body);
        console.log('Body content length:', legislatorContent.body.length);
        console.log('Body content contains **:', legislatorContent.body.includes('**'));
        console.log('Body content contains 凹之:', legislatorContent.body.includes('凹之'));
        
        const bodyContent = marked(legislatorContent.body);
        console.log('Parsed body content:', bodyContent);
        const bodyContentStr = String(bodyContent);
        console.log('Parsed content contains <strong>:', bodyContentStr.includes('<strong>'));
        legislatorBody = bodyContentStr;
      } catch (error) {
        console.error('Markdown parsing error:', error);
        // 如果 marked 解析失敗，直接使用原始內容
        legislatorBody = legislatorContent.body;
      }
    }
  }
} catch (error) {
  console.error(`無法載入 ${legislator.name} 的 CMS 資料：`, error);
}

// Sort reasons if they exist
const sortedReasons = legislator.reasons ? [...legislator.reasons].sort((a, b) => b.votes - a.votes) : [];

// Get all unique tags
const allTags = legislator.reasons ? Array.from(new Set(legislator.reasons.flatMap((reason: Reason) => reason.tags))) : [];

// 使用默認圖片替代遺失的圖片
const defaultImage = '/images/index-各種角色思考罷免的理由.webp';

// 準備 OpenGraph 資訊
// 優先使用經過 Astro 資產處理的 versioned URL
const ogImage = ogImageWithHash || legislatorImageUrl || defaultImage;
const description = `${legislator.name} - 立法委員｜${legislator.party || ''} ${legislator.district || ''}｜了解${legislator.name}的問題言行與罷免理由`;

// Format date
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('zh-TW', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
}

// TypeScript type for reason and video
type Reason = {
  id: string;
  content: string;
  tags: string[];
  source?: string;
  date: string;
  votes: number;
};

type Video = {
  id: string;
  title: string;
  youtubeId: string;
  description?: string;
  date: string;
  tags: string[];
};

// TypeScript type for meme
type Meme = {
  id: string;
  title: string;
  imageUrl: string;
  videoUrl?: string;
  mediaType?: 'image' | 'video';
  description?: string;
  tags: string[];
};
---

<Layout 
  title={`${legislator.name} - 罷免理由 | 100個罷免的理由`}
  description={description}
  image={ogImage}
  type="profile"
>
  <div class="relative">
    <!-- 背景裝飾 -->
    <div class="absolute top-0 left-0 w-full h-full -z-10 overflow-hidden">
      <div class="absolute -top-24 -right-20 w-96 h-96 bg-red-200/30 dark:bg-red-900/10 rounded-full blur-3xl"></div>
      <div class="absolute top-1/2 -left-20 w-80 h-80 bg-blue-200/30 dark:bg-blue-900/10 rounded-full blur-3xl"></div>
    </div>
    
    <div class="container mx-auto px-0 md:px-4 py-8">
      <div class="mb-12">
        <div class="flex justify-between items-center mb-6">
          <a href="/legislators" class="inline-flex items-center text-red-600 hover:text-red-800 transition-colors duration-300 group">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 transform group-hover:-translate-x-1 transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            返回立委列表
          </a>
          
          <div class="flex gap-2">
            <a href='https://docs.google.com/forms/d/e/1FAIpQLSdhu6ff2T0l3zEtKdXqYDkkMWSKwI5_Os32ybatuBUGAd7amA/viewform' target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-purple-600 hover:text-purple-800 transition-all duration-300 group bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 px-3 py-1.5 rounded-full shadow-md hover:shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              新增罷免理由
            </a>
          </div>
        </div>
        
        <div class="flex flex-col md:flex-row gap-8">
          <div class="w-full md:w-1/3 lg:w-1/4 px-4 md:px-0">
            <div class="space-y-6">
              {/* 立委照片 - 正常流動，不固定 */}
              <div class="relative rounded-lg overflow-hidden bg-white dark:bg-gray-800 shadow-lg group">
                <img 
                  src={legislatorImageUrl} 
                  alt={`${legislator.name}的照片`} 
                  class="w-full h-auto aspect-[3/4] object-cover transition-transform duration-500 group-hover:scale-105"
                  onload="this.onload=null; this.onerror=null;"
                  onerror="this.src='/favicon.svg'; this.onerror=null;"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <div class="absolute bottom-0 left-0 w-full p-4 bg-gradient-to-t from-black/80 to-transparent">
                  <h1 class="text-2xl font-bold text-white mb-1">{legislator.name}</h1>
                  <div class="flex items-center gap-2">
                    <span class={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-bold border-2 backdrop-blur-sm
                      ${legislator.party === '國民黨' ? 'bg-blue-100/80 text-blue-800 border-blue-500' : 
                        'bg-gray-100/80 text-gray-800 border-gray-500'}`}>
                      {legislator.party}
                    </span>
                    <span class="text-sm text-white/90">{legislator.district}</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 固定區塊容器 - 僅包含江湖稱號和罷免團體資訊 */}
            <div class="md:sticky md:top-12 md:z-10 space-y-4 mt-6">
              {/* 江湖稱號 - 桌面版固定顯示 */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  江湖稱號
                </h2>
                <div class="space-y-3">
                  {nicknames.length > 0 ? (
                    <div class="space-y-2">
                      {nicknames.map((nickname) => (
                        <div class="bg-purple-100 dark:bg-purple-900/30 rounded-lg p-2 flex items-center">
                          <span class="text-purple-700 dark:text-purple-300 font-medium">{nickname}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div class="text-gray-500 dark:text-gray-400 italic">
                      尚未有江湖稱號...
                    </div>
                  )}
                </div>
              </div>

              {/* 罷免團體資訊 - 桌面版固定顯示 */}
              {recallTeam && (
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
                  <div class="p-4 border-l-4 border-red-500">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      罷免團體資訊
                    </h2>
                    <a href={recallTeam.url} target="_blank" rel="noopener noreferrer" 
                       class="block w-full text-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium rounded-lg transition-all duration-300 hover:translate-x-1">
                      {recallTeam.name}
                      <svg class="w-4 h-4 inline-block ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          <div class="w-full md:w-2/3 lg:w-3/4">
        
              {/* 立委專屬文章內容區塊 */}
              {legislatorBody && (
                <div class="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                  <div class="bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-3">
                    <h3 class="text-xl font-bold text-white flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      關於{legislator.name}
                    </h3>
                  </div>
                  <div class="p-6">
                    <div class="prose prose-lg dark:prose-invert max-w-none" set:html={legislatorBody}></div>
                  </div>
                </div>
              )}
              
              {/* 爭議事項區塊 */}
              {controversies && controversies.length > 0 && (
                <div class="mb-8 bg-white dark:bg-gray-800 rounded-lg border border-orange-200 dark:border-orange-800/30 shadow-lg overflow-hidden">
                  <div class="bg-gradient-to-r from-orange-500 to-red-600 px-6 py-3">
                    <h3 class="text-xl font-bold text-white flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      爭議事項
                    </h3>
                  </div>
                  <div class="p-6 bg-gray-50 dark:bg-gray-900/30">
                    <div class="space-y-8">
                      <ControversyCarousel controversies={controversies} legislatorName={legislator.name} />
                    </div>
                  </div>
                </div>
              )}
              
                             <!-- YouTube 影片區塊 -->
            {(legislatorVideos.length > 0 || (legislator.videos && legislator.videos.length > 0)) && (
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  相關影片
                </h3>
                <VideoCarousel 
                  videos={legislatorVideos.length > 0 ? legislatorVideos : legislator.videos} 
                  formatDate={formatDate} 
                  carouselId={`${legislator.name}-videos-carousel`} 
                />
              </div>
            )}
            
            <!-- 罷免理由列表 -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
              <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                罷免理由
              </h3>
              
              {legislatorReasons.length === 0 ? (
                <div class="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
                  <div class="inline-flex justify-center items-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <p class="text-gray-600 dark:text-gray-400 mb-3">目前還沒有罷免理由</p>
                  <a href={`/legislator-profile-v2/${legislator.name}`} class="inline-flex items-center bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    新增罷免理由
                  </a>
                </div>
              ) : (
                <div class="grid grid-cols-1 gap-6">
                  {legislatorReasons.map((reason, index) => (
                    <div class="reason-item bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 flex flex-col">
                      <div class="flex flex-col md:flex-row">
                        {/* 媒體顯示區 - 只顯示第一個媒體項目 */}
                        {reason.media && reason.media.length > 0 && (
                          <div class="md:w-1/3 h-48 md:h-auto relative overflow-hidden" style="max-height: 250px;">
                            <div class="media-gallery h-full w-full" data-reason-id={reason.id}>
                              {reason.media[0].type === 'image' ? (
                                <img
                                  src={reason.media[0].url}
                                  alt={reason.media[0].caption || reason.title}
                                  class="w-full h-full object-cover cursor-pointer media-trigger"
                                  style="aspect-ratio: 4/3; max-width: 100%;"
                                  onerror="this.src='/favicon.svg'; this.onerror=null;"
                                />
                              ) : reason.media[0].type === 'video' && (
                                <div class="w-full h-full bg-gray-900 flex items-center justify-center cursor-pointer media-trigger">
                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-white/70" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                  </svg>
                                </div>
                              )}
                              {reason.media.length > 1 && (
                                <div class="absolute bottom-2 right-2 bg-black/70 text-white text-xs font-medium px-2 py-1 rounded-full backdrop-blur-sm flex items-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                  </svg>
                                  {reason.media.length}
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                        
                        {/* 文字內容區 */}
                        <div class={`p-5 ${reason.media && reason.media.length > 0 ? 'md:w-2/3' : 'w-full'} flex flex-col h-full`}>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 reason-title line-clamp-2">{reason.title}</h3>
                          
                          <div class="text-sm text-gray-700 dark:text-gray-300 mb-3 reason-body reason-description prose prose-sm dark:prose-invert max-w-none line-clamp-3 [&>ul]:list-disc [&>ul]:ml-5 [&>ol]:list-decimal [&>ol]:ml-5" set:html={reason.htmlBody}></div>
                          
                          <div class="flex flex-wrap gap-2 mb-3 mt-auto">
                            {reason.tags && reason.tags.slice(0, 5).map(tag => (
                              <span class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs">
                                #{tag}
                              </span>
                            ))}
                            {reason.tags && reason.tags.length > 5 && (
                              <span class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs">
                                +{reason.tags.length - 5}
                              </span>
                            )}
                          </div>
                          
                          <a href={`/reasons/${reason.id}`} class="mt-3 text-red-600 hover:text-red-800 flex items-center justify-end font-medium transition-colors duration-300 group">
                            查看完整理由
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </a>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <!-- 共同的罷免理由 -->
            <CommonReasons />

            <!-- 梗圖區塊 -->
            {legislatorMemes.length > 0 && (
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-pink-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  給你更多{legislator.name}
                </h3>
                
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" style="columns: 3 200px; column-gap: 1rem;" id="meme-gallery">
                  {legislatorMemes.map((meme, index) => (
                    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-all duration-300 mb-4 break-inside-avoid meme-item" data-index={index}>
                      <div class="relative cursor-pointer group">
                        {meme.videoUrl || (meme.imageUrl && meme.imageUrl.match(/\.(mp4|webm|ogg)$/i)) ? (
                          <video 
                            src={meme.videoUrl || meme.imageUrl} 
                            poster={meme.imageUrl && !meme.imageUrl.match(/\.(mp4|webm|ogg)$/i) ? meme.imageUrl : undefined}
                            muted
                            preload="metadata"
                            class="w-full h-auto object-cover group-hover:scale-105 transition-transform duration-500"
                            onmouseover="this.play()"
                            onmouseout="this.pause(); this.currentTime=0;"
                            data-media-type="video"
                          >
                            您的瀏覽器不支援影片播放
                          </video>
                        ) : (
                          <img 
                            src={meme.imageUrl} 
                            alt={meme.title} 
                            class="w-full h-auto object-cover group-hover:scale-105 transition-transform duration-500"
                            onerror="this.src='/favicon.svg'; this.onerror=null;"
                            loading="lazy"
                            data-media-type="image"
                          />
                        )}
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                          <div class="w-full p-4">
                            <h4 class="text-white text-lg font-medium">{meme.title}</h4>
                            {meme.description && <p class="text-white/80 text-sm mt-1">{meme.description}</p>}
                          </div>
                        </div>
                      </div>
                      <div class="p-3">
                        <div class="flex flex-wrap gap-2">
                          {meme.tags.map(tag => (
                            <span class="bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300 px-2 py-0.5 rounded-full text-xs">
                              #{tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <!-- 罷免按鈕 -->
            <div class="mt-6 mb-6">
              
              <a href={`https://recall2025.ourtaiwan.tw/legislators/${legislator.name}`} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="block w-full bg-red-600 hover:bg-red-700 text-white text-center py-4 px-6 rounded-lg text-xl font-bold transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg">
                <div class="flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  我要罷免{legislator.name}
                </div>
              </a>
            </div>
            
            <div class="bg-gradient-to-r from-red-500 to-orange-500 dark:from-red-600 dark:to-orange-600 rounded-lg shadow-lg p-6 text-white">
              <h3 class="text-xl font-bold mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                </svg>
                分享這個頁面
              </h3>
              <p class="mb-4 text-white/90">將此立委資訊分享給更多人，擴大監督力量。</p>
              <div class="flex gap-3">
                <button class="share-page-button bg-white/20 hover:bg-white/30 p-2 rounded-full transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/>
                  </svg>
                </button>
                <button class="share-page-button bg-white/20 hover:bg-white/30 p-2 rounded-full transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"/>
                  </svg>
                </button>
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<!-- 媒體檢視器彈窗 -->
<div id="media-modal" class="fixed inset-0 bg-black bg-opacity-80 z-50 hidden flex items-center justify-center">
  <div class="relative w-full max-w-5xl mx-auto p-4">
    <!-- 關閉按鈕 -->
    <button id="close-modal" class="absolute top-2 right-2 text-white bg-red-600 rounded-full p-2 z-10">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    
    <!-- 媒體容器 -->
    <div class="media-container bg-gray-900 rounded-lg overflow-hidden">
      <!-- 圖片/影片將在這裡顯示 -->
      <div id="modal-media-display" class="relative flex items-center justify-center min-h-[50vh]">
        <img id="modal-image" class="max-h-[80vh] max-w-full object-contain hidden" src="" alt="" />
        <div id="modal-video-container" class="hidden w-full aspect-video">
          <iframe id="modal-video" class="w-full h-full" frameborder="0" allowfullscreen></iframe>
        </div>
        <!-- 加載指示器 -->
        <div id="loading-indicator" class="text-white text-center">
          <svg class="animate-spin h-10 w-10 mx-auto mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p>載入中...</p>
        </div>
      </div>
      
      <!-- 說明文字 -->
      <div class="bg-gray-800 p-4">
        <p id="modal-caption" class="text-white text-center"></p>
      </div>
    </div>
    
    <!-- 導航按鈕 -->
    <div class="absolute inset-y-0 left-0 flex items-center">
      <button id="prev-media" class="bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
    </div>
    <div class="absolute inset-y-0 right-0 flex items-center">
      <button id="next-media" class="bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  </div>
</div>

<style is:global>
  .reason-link-btn {
    @apply w-full flex items-center justify-center text-center font-bold rounded-lg shadow-md transition-all duration-200;
    min-height: 52px;
    padding: 0.75rem 0;
    font-size: 1.15rem;
    background: linear-gradient(90deg, #e0e7ff 0%, #bae6fd 100%);
    color: #2563eb;
    border: none;
    gap: 0.5em;
    box-sizing: border-box;
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
  }
  .reason-link-btn svg {
    width: 1.4em;
    height: 1.4em;
    margin-right: 0.25em;
    flex-shrink: 0;
    vertical-align: middle;
  }
  .reason-link-btn:hover, .reason-link-btn:focus {
    background: linear-gradient(90deg, #60a5fa 0%, #38bdf8 100%);
    color: #fff;
    box-shadow: 0 4px 16px 0 rgba(59,130,246,0.10);
    outline: none;
    text-decoration: none;
  }
  @media (max-width: 640px) {
    .reason-link-btn {
      font-size: 1rem;
      padding-top: 0.9rem;
      padding-bottom: 0.9rem;
      min-height: 48px;
    }
  }
</style>

<script define:vars={{ legislatorReasons }}>
  document.addEventListener('DOMContentLoaded', function() {
    // 媒體項目定義
    // MediaItem = {
    //   type: 'image' | 'video',
    //   url: string,
    //   caption: string (可選)
    // }
    
    // 處理理由分享按鈕
    const shareButtons = document.querySelectorAll('.share-button');
    shareButtons.forEach(button => {
      button.addEventListener('click', function() {
        const reasonContent = this.getAttribute('data-reason-content');
        const shareText = `我支持罷免${document.title.split(' - ')[0].replace(/<[^>]*>/g, '')}，因為: ${reasonContent}`;
        
        // 嘗試使用 Web Share API
        if (navigator.share) {
          navigator.share({
            title: document.title,
            text: shareText,
            url: window.location.href
          })
          .catch(error => console.log('分享失敗:', error));
        } else {
          // 回退到複製到剪貼板
          navigator.clipboard.writeText(shareText + '\n\n' + window.location.href)
            .then(() => {
              // 顯示已複製提示
              const tooltip = document.createElement('div');
              tooltip.textContent = '已複製到剪貼板';
              tooltip.className = 'absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black/80 text-white px-3 py-1 rounded text-sm';
              this.style.position = 'relative';
              this.appendChild(tooltip);
              
              setTimeout(() => {
                tooltip.remove();
              }, 2000);
            });
        }
      });
    });

    // 處理頁面分享按鈕
    const sharePageButtons = document.querySelectorAll('.share-page-button');
    sharePageButtons.forEach(button => {
      button.addEventListener('click', function() {
        const legislatorName = document.title.split(' - ')[0].replace(/<[^>]*>/g, '');
        const shareText = `我認為立委${legislatorName}應該被罷免，查看更多罷免理由:`;
        
        // 嘗試使用 Web Share API
        if (navigator.share) {
          navigator.share({
            title: document.title,
            text: shareText,
            url: window.location.href
          })
          .catch(error => console.log('分享失敗:', error));
        } else {
          // 回退到複製到剪貼板
          navigator.clipboard.writeText(shareText + '\n\n' + window.location.href)
            .then(() => {
              // 顯示已複製提示
              const tooltip = document.createElement('div');
              tooltip.textContent = '已複製到剪貼板';
              tooltip.className = 'absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black/80 text-white px-3 py-1 rounded text-sm';
              this.style.position = 'relative';
              this.appendChild(tooltip);
              
              setTimeout(() => {
                tooltip.remove();
              }, 2000);
            });
        }
      });
    });
    
    // 媒體模態框功能
    // 媒體檢視器相關元素
    const mediaModal = document.getElementById('media-modal');
    const modalImage = document.getElementById('modal-image');
    const modalVideo = document.getElementById('modal-video');
    const modalVideoContainer = document.getElementById('modal-video-container');
    const modalCaption = document.getElementById('modal-caption');
    const loadingIndicator = document.getElementById('loading-indicator');
    const closeModalBtn = document.getElementById('close-modal');
    const prevMediaBtn = document.getElementById('prev-media');
    const nextMediaBtn = document.getElementById('next-media');
    
    // 當前顯示的媒體索引和媒體項目列表
    let currentMediaIndex = 0;
    let currentMediaItems = [];
    
    // 為圖片註冊加載完成事件
    if (modalImage) {
      modalImage.onload = () => {
        if (loadingIndicator) loadingIndicator.classList.add('hidden');
        modalImage.classList.remove('hidden');
      };
    }
    
    // 顯示媒體檢視器
    function openMediaModal(mediaItems, startIndex = 0) {
      console.log('Opening media modal with', mediaItems, 'starting at index', startIndex);
      
      if (!mediaItems || mediaItems.length === 0) {
        console.error('No media items provided');
        return;
      }
      
      // 隱藏所有媒體元素
      if (modalImage) modalImage.classList.add('hidden');
      if (modalVideoContainer) modalVideoContainer.classList.add('hidden');
      if (loadingIndicator) loadingIndicator.classList.remove('hidden');
      
      currentMediaItems = mediaItems;
      currentMediaIndex = startIndex;
      
      if (mediaModal) {
        mediaModal.classList.remove('hidden');
        // 確保flex布局正確顯示
        setTimeout(() => {
          if (mediaModal) {
            mediaModal.style.display = 'flex';
          }
        }, 10);
      }
      
      showCurrentMedia();
    }
    
    // YouTube URL轉換函數
    function convertYouTubeUrl(url) {
      if (!url) return url;
      
      // 如果已經是嵌入格式，直接返回
      if (url.includes('youtube.com/embed/')) return url;
      
      // 從常規URL提取視頻ID
      const regexPattern = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\s]+)/;
      const match = url.match(regexPattern);
      
      if (match && match[1]) {
        // 返回嵌入格式
        return `https://www.youtube.com/embed/${match[1]}`;
      }
      
      // 無法解析時返回原始URL
      return url;
    }
    
    // 顯示當前選中的媒體
    function showCurrentMedia() {
      if (!currentMediaItems || currentMediaItems.length === 0) {
        console.error('No media items to show');
        return;
      }
      
      // 停止任何正在播放的本地影片
      const localVideo = document.getElementById('modal-local-video');
      if (localVideo && localVideo instanceof HTMLVideoElement) {
        localVideo.pause();
        localVideo.currentTime = 0;
      }
      
      const mediaItem = currentMediaItems[currentMediaIndex];
      console.log('Showing media item:', mediaItem);
      
      // 重置顯示狀態
      if (modalImage) modalImage.classList.add('hidden');
      if (modalVideoContainer) modalVideoContainer.classList.add('hidden');
      if (loadingIndicator) loadingIndicator.classList.remove('hidden');
      
      if (mediaItem.type === 'image') {
        // 顯示圖片
        if (modalImage) {
          modalImage.src = mediaItem.url;
          modalImage.alt = mediaItem.caption || '';
          // 圖片加載完成後會觸發onload事件顯示圖片
        }
      } else if (mediaItem.type === 'video') {
        // 判斷是否為YouTube影片或本地影片
        if (mediaItem.url.includes('youtube.com') || mediaItem.url.includes('youtu.be')) {
          // YouTube影片
          const videoUrl = convertYouTubeUrl(mediaItem.url);
          
          if (modalVideo && modalVideoContainer) {
            modalVideo.src = videoUrl;
            modalVideoContainer.classList.remove('hidden');
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
          }
        } else {
          // 本地影片檔 (.mp4, .webm 等)
          // 移除現有的video標籤 (如果有的話)
          const existingVideo = document.getElementById('modal-local-video');
          if (existingVideo) {
            existingVideo.remove();
          }
          
          // 創建新的video標籤
          const videoEl = document.createElement('video');
          videoEl.id = 'modal-local-video';
          videoEl.src = mediaItem.url;
          videoEl.controls = true;
          videoEl.autoplay = true;
          videoEl.className = 'w-full h-full';
          
          // 清空並添加video標籤到容器中
          if (modalVideoContainer) {
            modalVideoContainer.innerHTML = '';
            modalVideoContainer.appendChild(videoEl);
            modalVideoContainer.classList.remove('hidden');
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
          }
        }
      }
      
      // 更新說明文字
      if (modalCaption) {
        modalCaption.innerHTML = mediaItem.caption || '';
      }
      
      // 更新導航按鈕可見性
      updateNavigationButtons();
    }
    
    // 更新導航按鈕可見性
    function updateNavigationButtons() {
      if (!currentMediaItems || currentMediaItems.length <= 1) {
        // 只有一個或沒有媒體項目時隱藏導航按鈕
        if (prevMediaBtn) prevMediaBtn.classList.add('hidden');
        if (nextMediaBtn) nextMediaBtn.classList.add('hidden');
        return;
      }
      
      // 顯示導航按鈕
      if (prevMediaBtn) prevMediaBtn.classList.remove('hidden');
      if (nextMediaBtn) nextMediaBtn.classList.remove('hidden');
    }
    
    // 關閉媒體檢視器
    function closeMediaModal() {
      if (mediaModal) {
        mediaModal.classList.add('hidden');
        mediaModal.style.display = '';
      }
      
      // 停止影片播放
      if (modalVideo) {
        modalVideo.src = '';
      }
      
      // 停止本地影片播放
      const localVideo = document.getElementById('modal-local-video');
      if (localVideo && localVideo instanceof HTMLVideoElement) {
        localVideo.pause();
        localVideo.currentTime = 0;
      }
    }
    
    // 顯示下一個媒體
    function showNextMedia() {
      if (!currentMediaItems || currentMediaItems.length <= 1) return;
      
      // 停止YouTube影片播放
      if (modalVideo) {
        modalVideo.src = '';
      }
      
      // 停止本地影片播放
      const localVideo = document.getElementById('modal-local-video');
      if (localVideo && localVideo instanceof HTMLVideoElement) {
        localVideo.pause();
        localVideo.currentTime = 0;
      }
      
      currentMediaIndex = (currentMediaIndex + 1) % currentMediaItems.length;
      showCurrentMedia();
    }
    
    // 顯示上一個媒體
    function showPrevMedia() {
      if (!currentMediaItems || currentMediaItems.length <= 1) return;
      
      // 停止YouTube影片播放
      if (modalVideo) {
        modalVideo.src = '';
      }
      
      // 停止本地影片播放
      const localVideo = document.getElementById('modal-local-video');
      if (localVideo && localVideo instanceof HTMLVideoElement) {
        localVideo.pause();
        localVideo.currentTime = 0;
      }
      
      currentMediaIndex = (currentMediaIndex - 1 + currentMediaItems.length) % currentMediaItems.length;
      showCurrentMedia();
    }
    
    // 為按鈕註冊事件處理程序
    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', (e) => {
        e.preventDefault();
        closeMediaModal();
      });
    }
    
    if (prevMediaBtn) {
      prevMediaBtn.addEventListener('click', (e) => {
        e.preventDefault();
        showPrevMedia();
      });
    }
    
    if (nextMediaBtn) {
      nextMediaBtn.addEventListener('click', (e) => {
        e.preventDefault();
        showNextMedia();
      });
    }
    
    // 按ESC鍵關閉檢視器
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        closeMediaModal();
      } else if (e.key === 'ArrowRight') {
        showNextMedia();
      } else if (e.key === 'ArrowLeft') {
        showPrevMedia();
      }
    });
    
    // 模態框點擊背景關閉
    if (mediaModal) {
      mediaModal.addEventListener('click', (e) => {
        if (e.target === mediaModal) {
          closeMediaModal();
        }
      });
    }
    
    // 為所有影片容器添加點擊事件
    const videoContainers = document.querySelectorAll('.youtube-video-container');
    videoContainers.forEach(container => {
      // 阻止父元素的點擊事件
      container.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const iframe = this.querySelector('iframe');
        if (!iframe) return;
        
        const src = iframe.getAttribute('src');
        if (!src) return;
        
        // 獲取標題 - 從父容器尋找
        let videoTitle = '';
        const parentCard = this.closest('.video-carousel-item');
        if (parentCard) {
          const titleEl = parentCard.querySelector('h3');
          if (titleEl) videoTitle = titleEl.textContent || '';
        }
        
        // 創建媒體項目
        const mediaItem = {
          type: 'video',
          url: src,
          caption: videoTitle
        };
        
        // 打開模態框
        openMediaModal([mediaItem], 0);
      });
    });
    
    // 修正直接嵌入的iframe的點擊事件
    const iframes = document.querySelectorAll('iframe[src*="youtube.com/embed"]');
    iframes.forEach(iframe => {
      // 創建透明覆蓋層以捕捉點擊
      const overlay = document.createElement('div');
      overlay.style.position = 'absolute';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100%';
      overlay.style.height = '100%';
      overlay.style.cursor = 'pointer';
      overlay.style.zIndex = '10';
      
      const parent = iframe.parentElement;
      if (parent) {
        // 確保父元素有相對定位
        if (getComputedStyle(parent).position === 'static') {
          parent.style.position = 'relative';
        }
        
        parent.appendChild(overlay);
        
        // 添加點擊事件
        overlay.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          
          const src = iframe.getAttribute('src');
          if (!src) return;
          
          // 尋找可能的標題
          let videoTitle = '';
          const parentCard = parent.closest('.video-carousel-item, .bg-white, .bg-gray-800');
          if (parentCard) {
            const titleEl = parentCard.querySelector('h3');
            if (titleEl) videoTitle = titleEl.textContent || '';
          }
          
          const mediaItem = {
            type: 'video',
            url: src,
            caption: videoTitle
          };
          
          openMediaModal([mediaItem], 0);
        });
      }
    });

    // 梗圖模態框功能
    const memeItems = document.querySelectorAll('.meme-item');
    if (memeItems.length > 0) {
      let currentMemeIndex = 0;
      let memeGalleryItems = [];

      // 收集所有梗圖項目
      memeGalleryItems = Array.from(memeItems).map((item, index) => {
        const imgEl = item.querySelector('img');
        const videoEl = item.querySelector('video');
        const titleEl = item.querySelector('h4');
        const descEl = item.querySelector('p');
        
        // 確定媒體類型和URL
        let mediaType = 'image';
        let mediaSrc = '';
        
        if (videoEl) {
          mediaType = 'video';
          mediaSrc = videoEl.src;
        } else if (imgEl) {
          mediaType = 'image';
          mediaSrc = imgEl.src;
        }
        
        const mediaItem = {
          type: mediaType,
          url: mediaSrc,
          caption: (titleEl ? titleEl.textContent : '') + (descEl ? ' - ' + descEl.textContent : '')
        };
        
        return mediaItem;
      });
      
      // 為梗圖添加點擊事件
      memeItems.forEach((item, index) => {
        const mediaContainer = item.querySelector('.relative');
        if (mediaContainer) {
          mediaContainer.addEventListener('click', function() {
            openMediaModal(memeGalleryItems, index);
          });
        }
      });
    }
    
    // 為罷免理由中的媒體觸發器添加點擊事件
    document.querySelectorAll('.media-trigger').forEach((trigger) => {
      trigger.addEventListener('click', function() {
        console.log('Media trigger clicked');
        const mediaGallery = this.closest('.media-gallery');
        if (!mediaGallery) {
          console.log('No media gallery found');
          return;
        }
        
        // 获取当前理由ID
        const reasonId = mediaGallery.getAttribute('data-reason-id');
        if (!reasonId) {
          console.log('No reason ID found');
          return;
        }
        
        console.log('Looking for reason with ID:', reasonId);
        
        // 从所有理由中找到对应的理由
        const matchedReason = legislatorReasons.find(item => item.id === reasonId);
        if (!matchedReason || !matchedReason.media || matchedReason.media.length === 0) {
          console.log('No matching reason or media found');
          return;
        }
        
        console.log('Found matching reason:', matchedReason.title);
        
        // 创建媒体项数组
        const mediaItems = matchedReason.media
          .filter(item => item.type === 'image' || item.type === 'video')
          .map(item => ({
            type: item.type,
            url: item.url || '',
            caption: item.caption || matchedReason.title
          }));
        
        console.log('Created media items:', mediaItems);
        
        // 打开模态框，显示所有媒体项
        if (mediaItems.length > 0) {
          openMediaModal(mediaItems, 0);
        }
      });
    });
  });
</script> 