---
import Layout from '../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import { marked } from 'marked';

// Import CommonReasons component at the top of the file with other imports
import CommonReasons from '../components/CommonReasons.astro';
import LegislatorButton from '../components/LegislatorButton.astro';

// 定義類型
interface Reason {
  id: string;
  title: string;
  body: string; // 統一使用body欄位作為UI顯示
  htmlBody: string | Promise<string>; // 新增htmlBody欄位用於轉換後的HTML內容，允許Promise類型
  media: Array<{
    type: string; 
    url?: string; 
    caption?: string;
    youtubeId?: string;
  }>;
  popularity: number;
}

// 從 roles 內容集合獲取角色數據
const roleEntries = await getCollection('roles');
const roles = roleEntries
  .map(entry => ({
    id: entry.slug,
    name: entry.data.title,
    icon: entry.data.icon || entry.slug,
    orderID: entry.data.orderID || 999, // 如果沒有設定排序ID，給予一個很大的數字
  }))
  .sort((a, b) => (a.orderID || 999) - (b.orderID || 999)); // 依照orderID排序

// 從 reasons 內容集合獲取所有理由
const reasonEntries = await getCollection('reasons');
// 按熱門度排序
const sortedReasons = reasonEntries.sort((a, b) => 
  (b.data.popularity || 0) - (a.data.popularity || 0)
);

// 轉換為前端使用的格式，並將Markdown轉換為HTML
const allReasons: Record<string, Reason> = Object.fromEntries(
  sortedReasons.map(entry => {
    // 使用marked將Markdown轉換為HTML
    const htmlBody = marked(entry.body || '');
    
    return [
      entry.slug,
      {
        id: entry.slug,
        title: entry.data.title,
        body: entry.body || '', // 保留原始Markdown
        htmlBody: htmlBody, // 新增htmlBody欄位存儲轉換後的HTML
        media: entry.data.media || [],
        popularity: entry.data.popularity || 0
      }
    ];
  })
);

// 建立角色與理由的映射關係
const roleReasonMap: Record<string, string[]> = roles.reduce((map, role) => {
  map[role.id] = sortedReasons
    .filter(reason => reason.data.relatedRoles?.includes(role.id))
    .map(reason => reason.slug);
  return map;
}, {} as Record<string, string[]>);

// 為前端準備格式化的理由數據
const reasonsByRole: Record<string, Reason[]> = Object.keys(roleReasonMap).reduce((result, roleId) => {
  result[roleId] = roleReasonMap[roleId].map(reasonId => allReasons[reasonId]);
  return result;
}, {} as Record<string, Reason[]>);

// 預設選中的角色
const defaultRoleId = 'worker';

// 首頁 OpenGraph 相關資訊
const ogImage = "/images/index-各種角色思考罷免的理由.webp";
const description = "100個罷免的理由 - 分享35位立委的不適任理由，揭露問題，引起共鳴。選擇你的身分角色，立即了解適合你關注的罷免理由！";

// 修復 window 類型定義
// 在 script 區塊最上方添加：
interface Window {
  reasonsByRole: Record<string, any>;
}
---

<Layout 
  title="100個罷免的理由｜島國物語"
  description={description}
  image={ogImage}
  type="website"
>
  <div class="relative bg-gradient-to-b from-red-50 to-white dark:from-gray-900 dark:to-gray-950 min-h-screen py-1 px-4 sm:px-6 lg:px-8">
    <!-- 頁面頂部裝飾 -->
    <div class="absolute top-0 inset-x-0 h-40 bg-gradient-to-b from-red-100 to-transparent dark:from-red-900/20 dark:to-transparent -z-10"></div>
    
    <!-- 把角色罷免理由資料傳遞給前端JavaScript -->
    <script define:vars={{ reasonsByRoleData: reasonsByRole }}>
      window.reasonsByRole = reasonsByRoleData;
    </script>
    
    <div class="max-w-7xl mx-auto pb-8">
      <!-- 頁面標題 -->
      <div class="text-center mb-4 md:mb-8">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-2 md:mb-4">
          <span class="text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-orange-500 dark:from-red-400 dark:to-orange-300">為什麼要大罷免呢？</span>
        </h1>
        <p class="hidden md:block text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          選擇你的身分角色，我們將推薦適合你關注的罷免理由，幫助你了解不適任立委的問題所在。
        </p>
      </div>
      
      <!-- 全局 Markdown 樣式 -->
      <style is:global>
        .prose {
          @apply text-xl; /* 增加基本字體大小 */
        }
        .prose h1 {
          @apply text-3xl font-bold text-gray-800 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2 mb-4;
        }
        .prose h2 {
          @apply text-2xl font-semibold text-gray-800 dark:text-white mt-6 mb-3 pb-2 border-b border-gray-100 dark:border-gray-700;
        }
        .prose h3 {
          @apply text-xl font-medium text-gray-800 dark:text-white mt-5 mb-2;
        }
        .prose p {
          @apply text-gray-700 dark:text-gray-200 leading-relaxed mb-4;
        }
        .prose ul, .prose ol {
          @apply my-4 pl-5 list-disc;
        }
        .prose ol {
          @apply list-decimal;
        }
        .prose li {
          @apply mb-2 ml-2 pl-1;
        }
        .prose li::marker {
          @apply text-gray-500 dark:text-gray-400;
        }
        .prose a {
          @apply text-red-600 dark:text-red-400 hover:underline;
        }
        .prose blockquote {
          @apply border-l-4 border-red-600 dark:border-red-400 pl-4 italic my-4 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-r-md text-lg;
        }
        .prose code {
          @apply bg-gray-100 dark:bg-gray-700 px-1.5 py-0.5 rounded text-base;
        }
        .prose pre {
          @apply bg-gray-800 dark:bg-gray-900 text-white p-3 rounded-md overflow-x-auto my-4;
        }
        .prose pre code {
          @apply text-base;
        }
        .prose img {
          @apply rounded-lg shadow-md my-4 max-w-full mx-auto;
        }
        .prose hr {
          @apply my-6 border-gray-200 dark:border-gray-700;
        }
        .prose table {
          @apply w-full border-collapse my-4;
        }
        .prose th {
          @apply bg-gray-100 dark:bg-gray-700 text-left px-3 py-2 border border-gray-200 dark:border-gray-600 text-base;
        }
        .prose td {
          @apply px-3 py-2 border border-gray-200 dark:border-gray-600 text-base;
        }

        /* 針對行動裝置的特殊處理 */
        @media (max-width: 640px) {
          .prose {
            @apply text-lg; /* 行動裝置略微縮小字體，但仍然比原本大 */
          }
          .prose h1 {
            @apply text-2xl pb-2 mb-3;
          }
          .prose h2 {
            @apply text-xl mt-5 mb-2;
          }
          .prose h3 {
            @apply text-lg mt-4 mb-2;
          }
          .prose p, .prose li {
            @apply text-base;
          }
          .prose blockquote {
            @apply text-base py-2 pl-3;
          }
        }

        /* 將 reason-body 內的內容也應用 prose 樣式 */
        .reason-body {
          @apply prose max-w-none;
        }
        .reason-link-btn {
          @apply w-full flex items-center justify-center text-center font-bold rounded-lg shadow-md transition-all duration-200;
          min-height: 52px;
          padding: 0.75rem 0;
          font-size: 1.15rem;
          background: linear-gradient(90deg, #e0e7ff 0%, #bae6fd 100%);
          color: #2563eb;
          border: none;
          gap: 0.5em;
          box-sizing: border-box;
          margin-top: 0.75rem;
          margin-bottom: 0.75rem;
        }
        .reason-link-btn svg {
          width: 1.4em;
          height: 1.4em;
          margin-right: 0.25em;
          flex-shrink: 0;
          vertical-align: middle;
        }
        .reason-link-btn:hover, .reason-link-btn:focus {
          background: linear-gradient(90deg, #60a5fa 0%, #38bdf8 100%);
          color: #fff;
          box-shadow: 0 4px 16px 0 rgba(59,130,246,0.10);
          outline: none;
          text-decoration: none;
        }
        .reason-item .flex.justify-between.items-center,
        .reason-card .flex.justify-between.items-center {
          display: block !important;
        }
        @media (max-width: 640px) {
          .reason-link-btn {
            font-size: 1rem;
            padding-top: 0.9rem;
            padding-bottom: 0.9rem;
            min-height: 48px;
          }
        }
      </style>
      
      <!-- 左右兩欄布局 -->
      <div class="flex flex-col md:flex-row gap-8 mb-12">
        <!-- 左欄：主視覺圖片 -->
        <div class="md:w-1/2">
          <div class="relative mx-auto">
            <picture>
              <!-- AVIF format for browsers that support it -->
              <source
                type="image/avif"
                sizes="(max-width: 640px) 100vw, 640px"
                srcset="
                  /images/optimized/index-roles-320.avif 320w,
                  /images/optimized/index-roles-480.avif 480w,
                  /images/optimized/index-roles-640.avif 640w
                "
              >
              <!-- WebP format as fallback -->
              <source
                type="image/webp"
                sizes="(max-width: 640px) 100vw, 640px"
                srcset="
                  /images/optimized/index-roles-320.webp 320w,
                  /images/optimized/index-roles-480.webp 480w,
                  /images/optimized/index-roles-640.webp 640w
                "
              >
              <!-- Original image as final fallback -->
              <img 
                src="/images/optimized/index-roles-640.webp"
                class="w-full h-auto max-h-[450px] object-contain rounded-xl shadow-md mx-auto transition-all duration-300 hover:shadow-lg"
                alt="各種角色一起思考罷免的理由"
                width="640"
                height="427"
                loading="eager"
                decoding="async"
                fetchpriority="high"
              >
            </picture>
            <div class="absolute inset-0 rounded-xl bg-gradient-to-t from-white/5 to-transparent pointer-events-none"></div>
          </div>
          <h2 class="text-2xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-orange-500 dark:from-red-400 dark:to-orange-300 mt-0 md:mt-4 md:hidden">選擇你的角色</h2>
        </div>
        
        <!-- 右欄：角色選擇區 -->
        <div class="md:w-1/2 mt-0 md:mt-0">

          <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 gap-4 -mt-7 md:mt-0">
            {roles.map(role => (
              <button
                class="role-button bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md hover:shadow-lg transition-all duration-300 flex flex-col items-center justify-center border-2 border-transparent hover:border-red-500 focus:border-red-500 focus:outline-none"
                data-role-id={role.id}
              >
                <div class="w-16 h-16 md:w-20 md:h-20 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mb-4">
                  {role.icon === 'student' && (
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path d="M12 14l9-5-9-5-9 5 9 5z" />
                      <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                    </svg>
                  )}
                  {role.icon === 'worker' && (
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  )}
                  {role.icon === 'parent' && (
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                  {role.icon === 'elder' && (
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  )}
                  {role.icon === 'kmt' && (
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                  )}
                </div>
                <h3 class="text-lg md:text-xl font-semibold text-gray-900 dark:text-white">{role.name}</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 text-center mt-1"></p>
              </button>
            ))}
          </div>
        </div>
      </div>
      <!-- 罷免理由內容區塊 -->
      <div class="space-y-8">
        <!-- 版型一：列表式 -->
        <div id="layout-list" class="role-reasons-container">
          <div class="flex justify-between items-center py-5 mb-1">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white text-center w-full">
              <span id="selected-role-name-2" class="text-red-600 dark:text-red-400 mr-2">學生</span>
              <span class="inline-block">關心的罷免理由</span>
            </h2>
            <!-- 版型切換按鈕 -->
            <div class="flex space-x-4">
              <button id="btn-layout-list" class="layout-btn bg-red-600 text-white px-5 py-2 rounded-lg shadow-md hover:bg-red-700 transition-colors min-w-[120px]">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
                列表式
              </button>
              <button id="btn-layout-grid" class="layout-btn bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-5 py-2 rounded-lg shadow-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors min-w-[140px]">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                卡片網格
              </button>
            </div>
          </div>
          <div class="space-y-4">
            {[1, 2, 3, 4, 5].map((_, index) => (
              <div class="reason-item bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 flex flex-col">
                <div class="flex flex-col md:flex-row">
                  <div class="md:w-1/3 h-48 md:h-auto relative">
                    <div class="media-gallery h-full">
                      <img src="/placeholder-image.jpg" alt="理由圖片" class="w-full h-full object-cover cursor-pointer media-trigger" />
                      <div class="absolute bottom-2 right-2 bg-black/70 text-white text-xs font-medium px-2 py-1 rounded-full backdrop-blur-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                        </svg>
                        <span class="media-count">1</span>
                      </div>
                    </div>
                  </div>
                  <div class="p-5 md:w-2/3 flex flex-col h-full">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 reason-title line-clamp-2">理由標題載入中...</h3>
                    <div class="text-sm text-gray-700 dark:text-gray-300 mb-3 reason-body line-clamp-3">理由描述載入中...</div>
                  
                    <a href="/reasons/placeholder" class="mt-2 w-full flex items-center justify-end text-red-600 hover:text-red-800 transition-colors font-medium">
                      查看完整理由
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <!-- 版型二：卡片網格 -->
        <div id="layout-grid" class="role-reasons-container hidden">
          <div class="flex justify-between items-center py-5 mb-1">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white text-center w-full">
              <span id="selected-role-name" class="text-red-600 dark:text-red-400 mr-2">學生</span>
              <span class="inline-block">關心的罷免理由</span>
            </h2>
            <!-- 版型切換按鈕 -->
            <div class="flex space-x-4">
              <button id="btn-layout-list" class="layout-btn bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-5 py-2 rounded-lg shadow-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors min-w-[120px]">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
                列表式
              </button>
              <button id="btn-layout-grid" class="layout-btn bg-red-600 text-white px-5 py-2 rounded-lg shadow-md hover:bg-red-700 transition-colors min-w-[140px]">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                卡片網格
              </button>
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5].map((_, index) => (
              <div class="reason-card bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 flex flex-col">
                <div class="relative h-48">
                  <div class="media-gallery h-full">
                    <img src="/placeholder-image.jpg" alt="理由圖片" class="w-full h-full object-cover cursor-pointer media-trigger" />
                    <div class="absolute bottom-2 right-2 bg-black/70 text-white text-xs font-medium px-2 py-1 rounded-full backdrop-blur-sm flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                      </svg>
                      <span class="media-count">1</span>
                    </div>
                  </div>
                </div>
                <div class="p-5 flex flex-col flex-grow">
                  <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 reason-title line-clamp-2">理由標題載入中...</h3>
                  <div class="text-sm text-gray-700 dark:text-gray-300 mb-3 reason-body line-clamp-3">理由描述載入中...</div>
                  <div class="flex flex-wrap gap-2 mb-3 mt-auto">
                  </div>
                  <a href="/reasons/placeholder" class="mt-2 w-full flex items-center justify-end text-red-600 hover:text-red-800 transition-colors font-medium">
                    查看完整理由
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
        
      </div>
      
    </div>
  </div>
  
  <!-- 立委列表按鈕 -->
  <LegislatorButton className="mt-6" />

  <!-- 共同的罷免理由 -->
  <CommonReasons />

  <!-- 立委列表按鈕 -->
  <LegislatorButton />
</Layout>

<!-- 媒體檢視器彈窗 -->
<div id="media-modal" class="fixed inset-0 bg-black bg-opacity-80 z-50 hidden flex items-center justify-center">
  <div class="relative w-full max-w-5xl mx-auto p-4">
    <!-- 關閉按鈕 -->
    <button id="close-modal" class="absolute top-2 right-2 text-white bg-red-600 rounded-full p-2 z-10">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    
    <!-- 媒體容器 -->
    <div class="media-container bg-gray-900 rounded-lg overflow-hidden">
      <!-- 圖片/影片將在這裡顯示 -->
      <div id="modal-media-display" class="relative flex items-center justify-center min-h-[50vh]">
        <img id="modal-image" class="max-h-[80vh] max-w-full object-contain hidden" src="" alt="" />
        <div id="modal-video-container" class="hidden w-full aspect-video">
          <iframe id="modal-video" class="w-full h-full" frameborder="0" allowfullscreen></iframe>
        </div>
        <!-- 加載指示器 -->
        <div id="loading-indicator" class="text-white text-center">
          <svg class="animate-spin h-10 w-10 mx-auto mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p>載入中...</p>
        </div>
      </div>
      
      <!-- 說明文字 -->
      <div class="bg-gray-800 p-4">
        <p id="modal-caption" class="text-white text-center"></p>
      </div>
    </div>
    
    <!-- 導航按鈕 -->
    <div class="absolute inset-y-0 left-0 flex items-center">
      <button id="prev-media" class="bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
    </div>
    <div class="absolute inset-y-0 right-0 flex items-center">
      <button id="next-media" class="bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  </div>
</div>

<script>
  // TypeScript 擴展 Window 接口
  declare global {
    interface Window {
      reasonsByRole: Record<string, any>;
    }
  }

  document.addEventListener('DOMContentLoaded', () => {
    
    // 處理角色選擇
    const roleButtons = document.querySelectorAll('.role-button');
    const selectedRoleNames = document.querySelectorAll('[id^="selected-role-name"]');
    
    // 設定預設選中角色
    let currentRole = 'worker';
    
    // 檢測是否為行動裝置
    const isMobile = window.innerWidth < 768;

    // 定義媒體項目類型
    interface MediaItem {
      type: 'image' | 'video' | 'youtube';
      url?: string;
      caption?: string;
      youtubeId?: string;
    }

    // 媒體檢視器相關元素
    const mediaModal = document.getElementById('media-modal');
    const modalImage = document.getElementById('modal-image') as HTMLImageElement;
    const modalVideo = document.getElementById('modal-video') as HTMLIFrameElement;
    const modalVideoContainer = document.getElementById('modal-video-container');
    const modalCaption = document.getElementById('modal-caption');
    const loadingIndicator = document.getElementById('loading-indicator');
    const closeModalBtn = document.getElementById('close-modal');
    const prevMediaBtn = document.getElementById('prev-media');
    const nextMediaBtn = document.getElementById('next-media');
    
    // 當前顯示的媒體索引和媒體項目列表
    let currentMediaIndex = 0;
    let currentMediaItems: MediaItem[] = [];
    
    // 檢查URL參數並定位到指定理由
    function handleURLParameters() {
      // 解析URL，檢查是否有hash參數
      const hash = window.location.hash;
      if (hash) {
        const reasonId = hash.substring(1); // 移除#符號
        
        // 從id中提取角色前綴 (例如: 'worker-1' 提取 'worker')
        const parts = reasonId.split('-');
        if (parts.length >= 2) {
          const roleId = parts[0];
          
          // 如果有效的角色，先切換到該角色
          if ((window as any).reasonsByRole[roleId]) {
            
            // 更新對應角色的理由顯示
            updateReasons(roleId);
            
            // 增加延遲時間確保DOM更新完成
            setTimeout(() => {
              // 嘗試多種選擇器尋找目標元素
              let targetElement = document.querySelector(`[data-reason-id="${reasonId}"]`);
              
              // 如果找不到，嘗試其他方法
              if (!targetElement) {
                // 嘗試在兩種佈局中找
                targetElement = document.querySelector(`#layout-list [data-reason-id="${reasonId}"]`) || 
                                document.querySelector(`#layout-grid [data-reason-id="${reasonId}"]`);
              }
              
              // 還是找不到時，根據布局類型來尋找第N個元素
              if (!targetElement) {
                // 找出目標理由的索引位置
                const reasons = (window as any).reasonsByRole[roleId] || [];
                const reasonIndex = reasons.findIndex((r: any) => r.id === reasonId);
                
                if (reasonIndex !== -1) {
                  // 確定當前顯示的佈局
                  const visibleLayout = document.querySelector('#layout-list:not(.hidden)') ? 'list' : 'grid';
                  
                  // 根據佈局類型選擇對應容器中的第N個元素
                  const containerSelector = visibleLayout === 'list' ? '#layout-list .reason-item' : '#layout-grid .reason-card';
                  const elements = document.querySelectorAll(containerSelector);
                  if (elements.length > reasonIndex) {
                    targetElement = elements[reasonIndex];
                  }
                }
              }
              
              if (targetElement) {
                
                // 確保目標布局可見
                const targetLayout = targetElement.closest('#layout-grid') ? 'grid' : 'list';
                
                // 如果目標元素在另一個當前隱藏的布局中，需要先切換布局
                if ((targetLayout === 'grid' && document.querySelector('#layout-grid.hidden')) ||
                    (targetLayout === 'list' && document.querySelector('#layout-list.hidden'))) {
                  
                  // 點擊相應布局按鈕
                  document.getElementById(`btn-layout-${targetLayout}`)?.click();
                  
                  // 再次延遲，確保布局切換完成
                  setTimeout(() => {
                    // 重新查找目標元素（因為布局已變更）
                    targetElement = document.querySelector(`[data-reason-id="${reasonId}"]`);
                    if (targetElement) {
                      scrollToElement(targetElement);
                    }
                  }, 100);
                } else {
                  // 直接滾動到元素
                  scrollToElement(targetElement);
                }
              } else {
                console.warn('Target element not found for reasonId:', reasonId);
              }
            }, 500); // 增加延遲時間
          }
        }
      }
    }
    
    // 抽取滾動到元素的函數
    function scrollToElement(element: Element) {
      // 為確保跨瀏覽器兼容性，嘗試多種滾動方法
      try {
        // 方法1: scrollIntoView
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 添加持續高亮效果
        element.classList.add('highlight-reason');
        
        // 不再移除高亮效果，使其持續存在
        // setTimeout(() => {
        //   element.classList.remove('highlight-reason');
        // }, 3000);
      } catch (e) {
        console.error('Error using scrollIntoView:', e);
        
        // 備用方法: 使用window.scrollTo
        try {
          const rect = element.getBoundingClientRect();
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const targetTop = rect.top + scrollTop - window.innerHeight / 2 + rect.height / 2;
          
          window.scrollTo({
            top: targetTop,
            behavior: 'smooth'
          });
          
          // 添加持續高亮效果
          element.classList.add('highlight-reason');
          
          // 不再移除高亮效果
          // setTimeout(() => {
          //   element.classList.remove('highlight-reason');
          // }, 3000);
        } catch (e2) {
          console.error('Fallback scrolling also failed:', e2);
        }
      }
    }
    
    // 處理分享按鈕點擊
    function handleShareButtonClick(reasonId: string, reasonTitle: string) {
      // 創建完整URL，使用與"查看詳情"相同的URL格式
      const shareUrl = `${window.location.origin}/reasons/${reasonId}`;
      
      // 檢查是否支援Web Share API
      if (navigator.share) {
        navigator.share({
          title: '100個罷免的理由',
          text: `分享一個罷免理由給您：${reasonTitle}`,
          url: shareUrl
        })
        .catch(error => {
          console.log('分享失敗:', error);
          // 若分享API失敗，回退到複製到剪貼簿
          copyToClipboard(shareUrl);
        });
      } else {
        // 不支援Web Share API時，複製連結到剪貼簿
        copyToClipboard(shareUrl);
      }
    }
    
    // 複製到剪貼簿功能
    function copyToClipboard(text: string) {
      navigator.clipboard.writeText(text)
        .then(() => {
          alert('連結已複製到剪貼簿');
        })
        .catch(err => {
          console.error('複製失敗:', err);
          alert('複製連結失敗，請手動複製網址');
        });
    }
    
    // 獲取全站媒體數據的快取
    const getAllReasons = () => {
      const allReasons: Record<string, any> = {};
      const roles = Object.keys((window as any).reasonsByRole || {});
      roles.forEach(role => {
        allReasons[role] = (window as any).reasonsByRole[role] || [];
      });
      return allReasons;
    };
    
    // 為圖片註冊加載完成事件
    if (modalImage) {
      modalImage.onload = () => {
        if (loadingIndicator) loadingIndicator.classList.add('hidden');
        modalImage.classList.remove('hidden');
      };
    }
    
    // 顯示媒體檢視器
    function openMediaModal(mediaItems: MediaItem[], startIndex = 0) {
      if (!mediaItems || mediaItems.length === 0) {
        console.error('No media items provided');
        return;
      }
      
      // 隱藏所有媒體元素
      if (modalImage) modalImage.classList.add('hidden');
      if (modalVideoContainer) modalVideoContainer.classList.add('hidden');
      if (loadingIndicator) loadingIndicator.classList.remove('hidden');
      
      currentMediaItems = mediaItems;
      currentMediaIndex = startIndex;
      
      if (mediaModal) {
        mediaModal.classList.remove('hidden');
        // 確保flex布局正確顯示
        setTimeout(() => {
          if (mediaModal) {
            mediaModal.style.display = 'flex';
          }
        }, 10);
      }
      
      showCurrentMedia();
    }
    
    // YouTube URL轉換函數
    function convertYouTubeUrl(url: string): string {
      if (!url) return url;
      
      // 如果已經是嵌入格式，直接返回
      if (url.includes('youtube.com/embed/')) return url;
      
      // 從常規URL提取視頻ID
      const regexPattern = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/shorts\/)([^&?\/]+)/;
      const match = url.match(regexPattern);
      
      if (match && match[1]) {
        // 返回嵌入格式
        return `https://www.youtube.com/embed/${match[1]}`;
      }
      
      // 無法解析時返回原始URL
      return url;
    }
    
    // 顯示當前選中的媒體
    function showCurrentMedia() {
      if (!currentMediaItems || currentMediaItems.length === 0) {
        console.error('No media items to show');
        return;
      }
      
      const mediaItem = currentMediaItems[currentMediaIndex];
      
      // 重置顯示狀態
      if (modalImage) modalImage.classList.add('hidden');
      if (modalVideoContainer) modalVideoContainer.classList.add('hidden');
      if (loadingIndicator) loadingIndicator.classList.remove('hidden');
      
      if (mediaItem.type === 'image') {
        // 顯示圖片
        if (modalImage) {
          modalImage.src = mediaItem.url || '';
          modalImage.alt = mediaItem.caption || '';
          // 圖片加載完成後會觸發onload事件顯示圖片
        }
      } else if (mediaItem.type === 'youtube' && mediaItem.youtubeId) {
        // 直接使用 YouTube ID
        if (modalVideo && modalVideoContainer) {
          modalVideo.src = `https://www.youtube.com/embed/${mediaItem.youtubeId}`;
          modalVideoContainer.classList.remove('hidden');
          if (loadingIndicator) loadingIndicator.classList.add('hidden');
        }
      } else if (mediaItem.type === 'video') {
        // 轉換YouTube URL
        const videoUrl = convertYouTubeUrl(mediaItem.url || '');
        
        // 顯示影片
        if (modalVideo && modalVideoContainer) {
          modalVideo.src = videoUrl;
          modalVideoContainer.classList.remove('hidden');
          if (loadingIndicator) loadingIndicator.classList.add('hidden');
        }
      }
      
      // 更新說明文字，使用innerHTML以支持HTML標籤
      if (modalCaption) {
        modalCaption.innerHTML = mediaItem.caption || '';
      }
      
      // 更新導航按鈕可見性
      updateNavigationButtons();
    }
    
    // 更新導航按鈕可見性
    function updateNavigationButtons() {
      if (!currentMediaItems || currentMediaItems.length <= 1) {
        // 只有一個或沒有媒體項目時隱藏導航按鈕
        if (prevMediaBtn) prevMediaBtn.classList.add('hidden');
        if (nextMediaBtn) nextMediaBtn.classList.add('hidden');
        return;
      }
      
      // 顯示導航按鈕
      if (prevMediaBtn) prevMediaBtn.classList.remove('hidden');
      if (nextMediaBtn) nextMediaBtn.classList.remove('hidden');
    }
    
    // 關閉媒體檢視器
    function closeMediaModal() {
      if (mediaModal) {
        mediaModal.classList.add('hidden');
        mediaModal.style.display = '';
      }
      
      // 停止影片播放
      if (modalVideo) {
        modalVideo.src = '';
      }
    }
    
    // 顯示下一個媒體
    function showNextMedia() {
      if (!currentMediaItems || currentMediaItems.length <= 1) return;
      
      currentMediaIndex = (currentMediaIndex + 1) % currentMediaItems.length;
      showCurrentMedia();
    }
    
    // 顯示上一個媒體
    function showPrevMedia() {
      if (!currentMediaItems || currentMediaItems.length <= 1) return;
      
      currentMediaIndex = (currentMediaIndex - 1 + currentMediaItems.length) % currentMediaItems.length;
      showCurrentMedia();
    }
    
    // 為按鈕註冊事件處理程序
    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', (e) => {
        e.preventDefault();
        closeMediaModal();
      });
    }
    
    if (prevMediaBtn) {
      prevMediaBtn.addEventListener('click', (e) => {
        e.preventDefault();
        showPrevMedia();
      });
    }
    
    if (nextMediaBtn) {
      nextMediaBtn.addEventListener('click', (e) => {
        e.preventDefault();
        showNextMedia();
      });
    }
    
    // 按ESC鍵關閉檢視器
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        closeMediaModal();
      } else if (e.key === 'ArrowRight') {
        showNextMedia();
      } else if (e.key === 'ArrowLeft') {
        showPrevMedia();
      }
    });
    
    // 更新理由內容
    function updateReasons(roleId: string) {
      // 使用任意類型訪問window.reasonsByRole，避開TypeScript錯誤
      const reasonsData = (window as any).reasonsByRole[roleId] || [];
      
      currentRole = roleId;
      
      // 更新角色名稱顯示
      const roleButton = document.querySelector(`.role-button[data-role-id="${roleId}"]`);
      const roleNameElement = roleButton?.querySelector('h3');
      const roleName = roleNameElement ? roleNameElement.textContent || '學生' : '學生';
      selectedRoleNames.forEach(el => {
        if (el) {
          el.textContent = roleName;
        }
      });
      
      // 更新角色按鈕樣式
      roleButtons.forEach(btn => {
        if (btn.getAttribute('data-role-id') === roleId) {
          btn.classList.add('border-red-500');
          btn.classList.add('bg-red-50');
          btn.classList.add('dark:bg-red-900/10');
        } else {
          btn.classList.remove('border-red-500');
          btn.classList.remove('bg-red-50');
          btn.classList.remove('dark:bg-red-900/10');
        }
      });
      
      // 取得該角色的罷免理由數據
      const reasons = reasonsData;
      
      // 更新卡片網格版型
      const gridCards = document.querySelectorAll('#layout-grid .reason-card');
      gridCards.forEach((card, index) => {
        if (index < reasons.length) {
          const reason = reasons[index];
          const titleEl = card.querySelector('.reason-title');
          const descEl = card.querySelector('.reason-body');
          const imgEl = card.querySelector('img');
          const mediaCountEl = card.querySelector('.media-count');
          const detailsLink = card.querySelector('.reason-link');
          const detailsLinkBtn = card.querySelector('.reason-link-btn');
          // 新版本設計的按鈕選擇器
          const reasonBtn = card.querySelector('a[href^="/reasons/"]');
          
          if (titleEl) titleEl.textContent = reason.title;
          // 確保body欄位存在，或顯示後備內容
          if (descEl) {
            descEl.innerHTML = reason.htmlBody || '暫無描述';
            // 添加 prose 類以啟用 Markdown 樣式
            descEl.classList.add('prose');
          }
          if (detailsLink) detailsLink.setAttribute('href', `/reasons/${reason.id}`);
          if (detailsLinkBtn) detailsLinkBtn.setAttribute('href', `/reasons/${reason.id}`);
          // 更新新版按鈕的連結
          if (reasonBtn) reasonBtn.setAttribute('href', `/reasons/${reason.id}`);
          
          // 設置reason-id用於URL定位和分享
          card.setAttribute('data-reason-id', reason.id);
          
          // 更新媒體顯示
          if (imgEl && reason.media && reason.media.length > 0) {
            const firstMedia = reason.media[0];
            (imgEl as HTMLImageElement).src = firstMedia.url;
            (imgEl as HTMLImageElement).alt = firstMedia.caption || reason.title;
            
            // 設置data屬性以識別點擊時的索引
            card.setAttribute('data-reason-index', index.toString());
            
            // 更新媒體計數 - 新格式只顯示總數
            if (mediaCountEl) {
              mediaCountEl.textContent = `${reason.media.length}`;
            }
          }
        }
      });
      
      // 更新列表版型
      const listItems = document.querySelectorAll('#layout-list .reason-item');
      listItems.forEach((item, index) => {
        if (index < reasons.length) {
          const reason = reasons[index];
          const titleEl = item.querySelector('.reason-title');
          const descEl = item.querySelector('.reason-body');
          const imgEl = item.querySelector('img');
          const mediaCountEl = item.querySelector('.media-count');
          const detailsLink = item.querySelector('.reason-link');
          const detailsLinkBtn = item.querySelector('.reason-link-btn');
          // 新版本設計的按鈕選擇器
          const reasonBtn = item.querySelector('a[href^="/reasons/"]');
          
          if (titleEl) titleEl.textContent = reason.title;
          // 確保body欄位存在，或顯示後備內容
          if (descEl) {
            descEl.innerHTML = reason.htmlBody || '暫無描述';
            // 添加 prose 類以啟用 Markdown 樣式
            descEl.classList.add('prose');
          }
          if (detailsLink) detailsLink.setAttribute('href', `/reasons/${reason.id}`);
          if (detailsLinkBtn) detailsLinkBtn.setAttribute('href', `/reasons/${reason.id}`);
          // 更新新版按鈕的連結
          if (reasonBtn) reasonBtn.setAttribute('href', `/reasons/${reason.id}`);
          
          // 設置reason-id用於URL定位和分享
          item.setAttribute('data-reason-id', reason.id);
          
          // 更新媒體顯示
          if (imgEl && reason.media && reason.media.length > 0) {
            const firstMedia = reason.media[0];
            (imgEl as HTMLImageElement).src = firstMedia.url;
            (imgEl as HTMLImageElement).alt = firstMedia.caption || reason.title;
            
            // 設置data屬性以識別點擊時的索引
            item.setAttribute('data-reason-index', index.toString());
            
            // 更新媒體計數 - 新格式只顯示總數
            if (mediaCountEl) {
              mediaCountEl.textContent = `${reason.media.length}`;
            }
          }
        }
      });
      
      // 更新後重新綁定事件監聽
      bindMediaClickEvents();
    }
    
    // 手風琴效果
    const accordionButtons = document.querySelectorAll('#layout-accordion .reason-accordion button');
    
    accordionButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const content = btn.nextElementSibling;
        const icon = btn.querySelector('.accordion-icon');
        
        // 切換內容顯示狀態
        if (content && content.classList.contains('hidden')) {
          content.classList.remove('hidden');
          if (icon instanceof HTMLElement) {
            icon.style.transform = 'rotate(180deg)';
          }
        } else if (content) {
          content.classList.add('hidden');
          if (icon instanceof HTMLElement) {
            icon.style.transform = 'rotate(0)';
          }
        }
      });
    });
    
    // 版型切換
    const layoutButtons = document.querySelectorAll('.layout-btn');
    const layoutContainers = document.querySelectorAll('.role-reasons-container');
    
    // 手機上顯示卡片網格布局，桌面上保持切換功能
    if (isMobile) {
      // 隱藏所有版型切換按鈕
      document.querySelectorAll('.layout-btn').forEach(btn => {
        const btnContainer = btn.closest('div.flex.space-x-4');
        if (btnContainer) {
          btnContainer.classList.add('hidden');
        }
      });
      
      // 隱藏列表布局，只顯示網格布局
      const listLayout = document.getElementById('layout-list');
      const gridLayout = document.getElementById('layout-grid');
      
      if (listLayout) listLayout.classList.add('hidden');
      if (gridLayout) gridLayout.classList.remove('hidden');
    } else {
      // 桌面版保持原有功能
      layoutButtons.forEach(btn => {
        btn.addEventListener('click', () => {
          // 重置所有按鈕樣式
          layoutButtons.forEach(b => {
            b.classList.remove('bg-red-600', 'text-white');
            b.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
          });
          
          // 識別是哪種布局按鈕被點擊
          const buttonId = btn.id;
          const isGridButton = buttonId.includes('grid');
          const layoutType = isGridButton ? 'grid' : 'list';
          
          // 設定所有對應類型的按鈕樣式
          document.querySelectorAll(`.layout-btn[id$="${layoutType}"]`).forEach(sameTypeBtn => {
            sameTypeBtn.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
            sameTypeBtn.classList.add('bg-red-600', 'text-white');
          });
          
          // 隱藏所有版型
          layoutContainers.forEach(container => {
            container.classList.add('hidden');
          });
          
          // 顯示選中的版型
          const layoutElement = document.getElementById(`layout-${layoutType}`);
          if (layoutElement) {
            layoutElement.classList.remove('hidden');
          }
        });
      });
    }
    
    // 媒體模態框點擊背景關閉
    if (mediaModal) {
      mediaModal.addEventListener('click', (e) => {
        if (e.target === mediaModal) {
          closeMediaModal();
        }
      });
    }
    
    // 綁定媒體點擊事件的直接實現方式
    function bindMediaClickEvents() {
      // 選擇所有媒體觸發器
      document.querySelectorAll('.media-trigger').forEach((trigger) => {
        // 移除舊事件
        const newTrigger = trigger.cloneNode(true);
        if (trigger.parentNode) {
          trigger.parentNode.replaceChild(newTrigger, trigger);
        }
        
        // 為新元素添加事件
        newTrigger.addEventListener('click', function(this: Element, e) {
          e.preventDefault();
          e.stopPropagation();
          
          // 找到包含該觸發器的卡片/項目
          const container = this.closest('.reason-card, .reason-item');
          if (!container) {
            console.error('Could not find parent container');
            return;
          }
          
          // 獲取索引
          const reasonIndex = parseInt(container.getAttribute('data-reason-index') || '-1');
          if (reasonIndex === -1) {
            console.error('Invalid reason index');
            return;
          }
          
          // 獲取當前角色的理由數據
          const reasons = (window as any).reasonsByRole[currentRole] || [];
          if (reasonIndex >= 0 && reasonIndex < reasons.length) {
            const reason = reasons[reasonIndex];
            if (reason.media && reason.media.length > 0) {
              openMediaModal(reason.media, 0);
            } else {
              console.error('No media found for reason');
            }
          } else {
            console.error('Reason index out of bounds');
          }
        });
      });
    }
    
    // 初始化頁面
    updateReasons(currentRole);
    
    // 為角色按鈕添加點擊事件
    roleButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const roleId = btn.getAttribute('data-role-id');
        if (roleId) {
          updateReasons(roleId);
          
          // 在行動裝置上自動滾動到內容區域
          if (window.innerWidth < 768) {
            // 延遲執行以確保內容已更新
            setTimeout(() => {
              // 滾動到標題或內容區域，增加向下滾動的距離
              const contentElement = document.querySelector('#layout-grid h2') || 
                                     document.querySelector('.role-reasons-container:not(.hidden)');
              
              if (contentElement) {
                // 獲取元素位置，並向下滾動額外的距離
                const rect = contentElement.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const targetTop = rect.top + scrollTop - 120; // 向下多滾動一些距離
                
                window.scrollTo({
                  top: targetTop,
                  behavior: 'smooth'
                });
              }
            }, 100);
          }
        }
      });
    });
    
    // 處理URL參數定位
    handleURLParameters();
    
    // 監聽URL變化，支援瀏覽器返回按鈕
    window.addEventListener('hashchange', handleURLParameters);

    // 設定罷免理由區塊 reason-body 內所有連結為在新分頁開啟
    const reasonBodies = document.querySelectorAll('.reason-body');
    reasonBodies.forEach(body => {
      const links = body.querySelectorAll('a');
      links.forEach(link => {
        link.setAttribute('target', '_blank');
        link.setAttribute('rel', 'noopener');
      });
    });
  });
</script>

<!-- 加入分享功能的CSS樣式 -->
<style>
  /* 在手機裝置上直接顯示卡片網格布局，隱藏列表布局 */
  @media (max-width: 767px) {
    #layout-list {
      display: none !important;
    }
    
    #layout-grid {
      display: block !important;
    }
    
    /* 隱藏版型切換按鈕 */
    .layout-btn {
      display: none !important;
    }
  }
  
  .highlight-reason {
    animation: highlight-pulse 2s ease-in-out infinite alternate;
    border: 3px solid rgba(239, 68, 68, 0.8) !important;
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
    transition: all 0.3s ease;
  }
  
  @keyframes highlight-pulse {
    0% { 
      border-color: rgba(239, 68, 68, 0.3);
      box-shadow: 0 0 10px rgba(239, 68, 68, 0.2);
    }
    50% {
      border-color: rgba(239, 68, 68, 0.8);
      box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
    }
    100% {
      border-color: rgba(239, 68, 68, 0.3);
      box-shadow: 0 0 10px rgba(239, 68, 68, 0.2);
    }
  }

  .common-reasons-fade {
    /* 確保 relative 已被 Tailwind 加入 */
  }
  .common-reasons-fade::before,
  .common-reasons-fade::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 8rem; /* 漸層高度，可調整 */
    z-index: 5; /* 確保在內容之上 */
    pointer-events: none; /* 不影響滑鼠事件 */
  }
  .common-reasons-fade::before {
    top: 0;
    /* 淺色模式：從白色漸變到透明 */
    background: linear-gradient(to bottom, white, rgba(255, 255, 255, 0));
  }
  .common-reasons-fade::after {
    bottom: 0;
    /* 淺色模式：從透明漸變到白色 */
    background: linear-gradient(to top, white, rgba(255, 255, 255, 0));
  }
  /* 深色模式下的漸層 */
  .dark .common-reasons-fade::before {
    /* gray-950 (#030712) 到透明 */
    background: linear-gradient(to bottom, #030712, rgba(3, 7, 18, 0));
  }
  .dark .common-reasons-fade::after {
    /* gray-950 (#030712) 到透明 */
    background: linear-gradient(to top, #030712, rgba(3, 7, 18, 0));
  }
</style> 

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const commonReasonsContainer = document.querySelector('.common-reasons-fade');
    const fadeTop = document.getElementById('reasons-fade-top');
    const fadeBottom = document.getElementById('reasons-fade-bottom');

    if (!commonReasonsContainer || !fadeTop || !fadeBottom) {
      console.warn('Common reasons fade elements not found.');
      return;
    }

    const updateFadeVisibility = () => {
      const rect = commonReasonsContainer.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const fadeMargin = 50; // 距離視窗邊緣多少像素開始淡出

      // 如果整個區塊舒適地放在視窗內，則不需要淡出效果
      if (rect.height < viewportHeight - 2 * fadeMargin) {
        fadeTop.style.opacity = '0';
        fadeBottom.style.opacity = '0';
        return;
      }

      // 當容器頂部接近或超出視窗頂部時顯示
      fadeTop.style.opacity = rect.top < fadeMargin ? '1' : '0';

      // 底部淡出
      // 當容器底部接近或超出視窗底部時顯示
      fadeBottom.style.opacity = rect.bottom > viewportHeight - fadeMargin ? '1' : '0';
    };

    // 添加 window 滾動和大小變化事件監聽
    window.addEventListener('scroll', updateFadeVisibility);
    window.addEventListener('resize', updateFadeVisibility);

    // 初始檢查 (延遲以確保尺寸計算正確)
    setTimeout(updateFadeVisibility, 100);
  });
</script>