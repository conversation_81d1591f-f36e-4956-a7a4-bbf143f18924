---
import Layout from '../layouts/Layout.astro';
---

<Layout title="搜尋功能測試頁面">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">搜尋功能測試頁面</h1>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">桌面版搜尋</h2>
      <p class="mb-4">點擊右上角的搜尋圖標或使用快捷鍵 ⌘+K 開啟搜尋</p>
      <button id="open-desktop-search" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
        開啟桌面版搜尋
      </button>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold mb-4">行動版搜尋</h2>
      <p class="mb-4">點擊此按鈕測試行動版搜尋功能</p>
      <button id="test-mobile-search" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
        開啟行動版搜尋
      </button>
      <button id="close-mobile-search" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded ml-2">
        關閉行動版搜尋
      </button>
    </div>
    
    <div class="mt-8 bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
      <h3 class="font-semibold mb-2">運行時信息:</h3>
      <div id="debug-info" class="font-mono text-sm overflow-auto max-h-60"></div>
    </div>
  </div>
</Layout>

<script>
  function logDebug(message: string) {
    const debugEl = document.getElementById('debug-info');
    if (debugEl) {
      const logItem = document.createElement('div');
      logItem.textContent = `[${new Date().toISOString()}] ${message}`;
      logItem.className = 'py-1 border-b border-gray-200 dark:border-gray-600';
      debugEl.appendChild(logItem);
      debugEl.scrollTop = debugEl.scrollHeight;
    }
    console.log(message);
  }

  document.addEventListener('DOMContentLoaded', () => {
    logDebug('頁面加載完成');
    
    // 檢查全局搜尋函數是否存在
    const windowAny = window as any;
    logDebug(`openSearchModal 函數存在: ${typeof windowAny.openSearchModal === 'function'}`);
    logDebug(`openMobileSearchModal 函數存在: ${typeof windowAny.openMobileSearchModal === 'function'}`);
    
    // 桌面版搜尋按鈕
    const desktopSearchBtn = document.getElementById('open-desktop-search');
    if (desktopSearchBtn) {
      desktopSearchBtn.addEventListener('click', () => {
        logDebug('點擊桌面版搜尋按鈕');
        if (typeof windowAny.openSearchModal === 'function') {
          windowAny.openSearchModal();
        } else {
          logDebug('錯誤: openSearchModal 函數不存在');
          alert('桌面版搜尋功能不可用');
        }
      });
    }
    
    // 行動版搜尋按鈕
    const mobileSearchBtn = document.getElementById('test-mobile-search');
    if (mobileSearchBtn) {
      mobileSearchBtn.addEventListener('click', () => {
        logDebug('點擊行動版搜尋按鈕');
        if (typeof windowAny.openMobileSearchModal === 'function') {
          windowAny.openMobileSearchModal();
          logDebug('已調用 openMobileSearchModal 函數');
        } else {
          logDebug('錯誤: openMobileSearchModal 函數不存在');
          alert('行動版搜尋功能不可用');
        }
      });
    }
    
    // 關閉行動版搜尋按鈕
    const closeMobileSearchBtn = document.getElementById('close-mobile-search');
    if (closeMobileSearchBtn) {
      closeMobileSearchBtn.addEventListener('click', () => {
        logDebug('點擊關閉行動版搜尋按鈕');
        if (typeof windowAny.closeMobileSearchModal === 'function') {
          windowAny.closeMobileSearchModal();
          logDebug('已調用 closeMobileSearchModal 函數');
        } else {
          logDebug('錯誤: closeMobileSearchModal 函數不存在');
          alert('關閉行動版搜尋功能不可用');
        }
      });
    }
    
    // 監聽鍵盤事件
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        logDebug('按下 Escape 鍵');
      }
    });
  });
</script> 