---
import { getCollection, getEntry } from 'astro:content';
import Layout from '../../layouts/Layout.astro';

// 獲取URL參數
export async function getStaticPaths() {
  const reasonEntries = await getCollection('reasons');
  
  // 按照發布日期排序理由
  reasonEntries.sort((a, b) => {
    const dateA = a.data.publishDate ? new Date(a.data.publishDate) : new Date(0);
    const dateB = b.data.publishDate ? new Date(b.data.publishDate) : new Date(0);
    return dateB.getTime() - dateA.getTime(); // 降序排列
  });
  
  return reasonEntries.map((entry, index) => {
    // 計算上一個和下一個理由
    const prevReason = index > 0 ? reasonEntries[index - 1] : null;
    const nextReason = index < reasonEntries.length - 1 ? reasonEntries[index + 1] : null;
    
    // 獲取前一篇和下一篇的首圖
    const prevImage = prevReason && prevReason.data.media && prevReason.data.media.length > 0 && prevReason.data.media[0].type === 'image' 
      ? prevReason.data.media[0].url 
      : "/default-preview.jpg";
    
    const nextImage = nextReason && nextReason.data.media && nextReason.data.media.length > 0 && nextReason.data.media[0].type === 'image' 
      ? nextReason.data.media[0].url 
      : "/default-preview.jpg";
    
    return {
      params: { id: entry.slug },
      props: { 
        entry,
        prevReason: prevReason ? { 
          slug: prevReason.slug, 
          title: prevReason.data.title,
          image: prevImage
        } : null,
        nextReason: nextReason ? { 
          slug: nextReason.slug, 
          title: nextReason.data.title,
          image: nextImage
        } : null
      },
    };
  });
}

const { entry, prevReason, nextReason } = Astro.props;
const { Content } = await entry.render();

// 獲取相關立委
let relatedLegislators = entry.data.relatedLegislators || [];

// 定義所有立委名單 (37名)
const allLegislatorsList = [
  "林沛祥", "王鴻薇", "李彥秀", "羅智強", "徐巧芯", 
  "賴士葆", "葉元之", "洪孟楷", "張智倫", "林德福", 
  "羅明才", "廖先翔", "牛煦庭", "涂權吉", "魯明哲", 
  "萬美玲", "呂玉玲", "邱若華", "鄭正鈐", "徐欣瑩", 
  "林思銘", "邱鎮軍", "顏寬恆", "楊瓊瓔", "廖偉翔", 
  "黃健豪", "羅廷瑋", "江啟臣", "謝衣鳳", "馬文君", 
  "游顥", "丁學忠", "傅崐萁", "黃建賓", "陳玉珍", 
  "陳雪生", "陳超明"
];

// 如果relatedLegislators包含"all"，使用預定義的立委列表
if (relatedLegislators.includes('all')) {
  relatedLegislators = allLegislatorsList;
}

// 獲取相關角色
const relatedRoles = [];
for (const roleId of entry.data.relatedRoles || []) {
  try {
    const roleEntry = await getEntry('roles', roleId);
    if (roleEntry) {
      relatedRoles.push(roleEntry);
    }
  } catch (error) {
    console.error(`角色不存在: ${roleId}`);
  }
}

// 準備 OpenGraph 圖片 URL
let ogImage = "/default-og-image.png";
if (entry.data.media && entry.data.media.length > 0 && entry.data.media[0].type === 'image') {
  ogImage = entry.data.media[0].url;
}

// 準備描述文字，使用body欄位，如果沒有則使用預設描述
let description = entry.data.body;
if (!description) {
  // 使用預設描述文字
  description = `【${entry.data.title}】- 分享35位立委的不適任理由，揭露問題，引起共鳴。`;
}
---

<Layout 
  title={`${entry.data.title} | 100個罷免的理由`}
  description={description}
  image={ogImage}
  type="article"
>
  <!-- 上一篇/下一篇理由導航按鈕 -->
  <div class="fixed-nav-buttons">
    {prevReason && (
      <a href={`/reasons/${prevReason.slug}`} class="nav-button left-button" title={`上一篇: ${prevReason.title}`}>
        <div class="nav-button-content">
          <div class="nav-arrow left-arrow">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 19l-7-7 7-7" />
            </svg>
          </div>
          <div class="nav-text">
            <span class="nav-label">上一篇</span>
            <span class="nav-title-text">{prevReason.title}</span>
          </div>
          <div class="nav-image-preview">
            <img src={prevReason.image} alt={`預覽: ${prevReason.title}`} />
          </div>
        </div>
      </a>
    )}
    
    {nextReason && (
      <a href={`/reasons/${nextReason.slug}`} class="nav-button right-button" title={`下一篇: ${nextReason.title}`}>
        <div class="nav-button-content">
          <div class="nav-arrow right-arrow">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 5l7 7-7 7" />
            </svg>
          </div>
          <div class="nav-text">
            <span class="nav-label">下一篇</span>
            <span class="nav-title-text">{nextReason.title}</span>
          </div>
          <div class="nav-image-preview">
            <img src={nextReason.image} alt={`預覽: ${nextReason.title}`} />
          </div>
        </div>
      </a>
    )}
  </div>

  <div class="max-w-4xl mx-auto px-0  sm:px-4 sm:py-8">
    <article class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
      <!-- 頭部區塊：標題與熱門度 -->
      <div class="p-4 pb-3 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-start">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {entry.data.title}
          </h1>
          <div class="hidden sm:flex items-center bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 px-3 py-1 rounded-full text-sm mb-2 sm:mb-0 self-start">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            熱門
          </div>
        </div>
        
        <!-- 發布日期與熱門標籤（在行動裝置共用同一行） -->
        {entry.data.publishDate && (
          <div class="flex flex-row items-center justify-start gap-2 mt-2">
            <p class="text-sm text-gray-500 dark:text-gray-400">
              發布於 {new Date(entry.data.publishDate).toLocaleDateString('zh-TW')}
            </p>
            <div class="flex sm:hidden items-center bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 px-3 py-1 rounded-full text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              熱門
            </div>
          </div>
        )}
      </div>
      
      <!-- 主要內容區 -->
      <div class="p-2 sm:p-3 lg:p-4">
        <!-- 渲染Markdown內容 -->
        <div class="prose dark:prose-invert max-w-none bg-white dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm p-3 sm:p-4 md:p-5">
          <!-- 內容標題樣式增強 -->
          <style is:global>
            .prose {
              @apply text-xl pt-0 mt-0; /* 增加基本字體大小並移除頂部間距 */
            }
            .prose h1 {
              @apply text-3xl font-bold text-gray-800 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2 mb-4 mt-0 pt-0;
            }
            .prose h2 {
              @apply text-2xl font-semibold text-gray-800 dark:text-white mt-4 mb-3 pb-2 border-b border-gray-100 dark:border-gray-700;
            }
            .prose h3 {
              @apply text-xl font-medium text-gray-800 dark:text-white mt-4 mb-2;
            }
            .prose p {
              @apply text-gray-700 dark:text-gray-200 leading-relaxed mb-4 mt-0;
            }
            .prose p:first-child {
              @apply mt-0;
            }
            .prose ul, .prose ol {
              @apply my-4 pl-5 list-disc;
            }
            .prose ol {
              @apply list-decimal;
            }
            .prose li {
              @apply mb-2 ml-2 pl-1;
            }
            .prose li::marker {
              @apply text-gray-500 dark:text-gray-400;
            }
            .prose a {
              @apply text-red-600 dark:text-red-400 hover:underline;
            }
            .prose blockquote {
              @apply border-l-4 border-red-600 dark:border-red-400 pl-4 italic my-4 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-r-md text-lg;
            }
            .prose code {
              @apply bg-gray-100 dark:bg-gray-700 px-1.5 py-0.5 rounded text-base;
            }
            .prose pre {
              @apply bg-gray-800 dark:bg-gray-900 text-white p-3 rounded-md overflow-x-auto my-4;
            }
            .prose pre code {
              @apply text-base;
            }
            .prose img {
              @apply rounded-lg shadow-md my-4 max-w-full mx-auto;
            }
            .prose hr {
              @apply my-6 border-gray-200 dark:border-gray-700;
            }
            .prose table {
              @apply w-full border-collapse my-4;
            }
            .prose th {
              @apply bg-gray-100 dark:bg-gray-700 text-left px-3 py-2 border border-gray-200 dark:border-gray-600 text-base;
            }
            .prose td {
              @apply px-3 py-2 border border-gray-200 dark:border-gray-600 text-base;
            }

            /* 針對行動裝置的特殊處理 */
            @media (max-width: 640px) {
              .prose {
                @apply text-lg; /* 行動裝置略微縮小字體，但仍然比原本大 */
              }
              .prose h1 {
                @apply text-2xl pb-2 mb-3;
              }
              .prose h2 {
                @apply text-xl mt-5 mb-2;
              }
              .prose h3 {
                @apply text-lg mt-4 mb-2;
              }
              .prose p, .prose li {
                @apply text-base;
              }
              .prose blockquote {
                @apply text-base py-2 pl-3;
              }
            }
          </style>
          <Content />
          
          <script>
            // 設定主要內容區塊的所有連結為在新分頁開啟
            document.addEventListener('DOMContentLoaded', () => {
              const contentLinks = document.querySelector('.prose')?.querySelectorAll('a');
              if (contentLinks) {
                contentLinks.forEach(link => {
                  // 設定連結在新分頁開啟
                  link.setAttribute('target', '_blank');
                  // 添加 rel="noopener" 以提高安全性
                  link.setAttribute('rel', 'noopener');
                });
              }
            });
          </script>
        </div>
      </div>
      
      <!-- 媒體內容輪播 -->
      {entry.data.media && entry.data.media.length > 0 && (
        <div class="border-t border-gray-200 dark:border-gray-700 p-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">相關媒體</h2>
          
          <div class="media-gallery grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {entry.data.media.map((item: any, index: number) => (
              <div class="media-item rounded-lg overflow-hidden shadow-md bg-gray-100 dark:bg-gray-700" 
                data-type={item.type}
                data-video-url={item.type === 'video' ? item.url : null}
                data-index={index}
              >
                {item.type === 'image' && (
                  <img 
                    src={item.url} 
                    alt={item.caption || entry.data.title} 
                    class="w-full h-48 object-cover cursor-pointer media-trigger"
                    data-index={index}
                  />
                )}
                {item.type === 'youtube' && item.youtubeId && (
                  <div class="relative aspect-video">
                    <iframe 
                      src={`https://www.youtube.com/embed/${item.youtubeId}`} 
                      class="w-full h-full absolute inset-0"
                      frameborder="0" 
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                      allowfullscreen
                    ></iframe>
                  </div>
                )}
                {item.type === 'video' && item.url && (
                  <div class="relative aspect-video cursor-pointer media-trigger" data-index={index}>
                    <div class="w-full h-full flex items-center justify-center bg-gray-800">
                      <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div class="w-16 h-16 bg-red-600 bg-opacity-80 rounded-full flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                      </div>
                      <p class="text-white z-10">點擊播放影片</p>
                    </div>
                  </div>
                )}
                {item.caption && (
                  <div class="p-3 text-sm text-gray-700 dark:text-gray-300" set:html={item.caption} />
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      
      <!-- 相關資訊 -->
      <div class="border-t border-gray-200 dark:border-gray-700 p-6">
        <!-- 相關立委 -->
        {relatedLegislators.length > 0 && (
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">相關立委</h2>
            
            <!-- 立委通緝令跑馬燈 -->
            {relatedLegislators.length > 0 && (
              <div class="mt-4 mb-6 overflow-hidden relative">
                <div class="wanted-marquee-container relative overflow-hidden border-2 border-red-800 rounded-md bg-amber-50 dark:bg-amber-900 p-4">
                  <div class="wanted-marquee-track flex animate-marquee">
                    {relatedLegislators.map((legislator: string) => {
                      const legislatorName = legislator.trim();
                      // 從名稱中提取數字來對應圖片
                      const matchNum = /^\d+/.exec(legislatorName);
                      const num = matchNum ? matchNum[0] : "";
                      // 通緝照片路徑 (黑白效果)
                      const imgPath = num ? `/legislators/${legislatorName}.jpg` : `/legislators/placeholder-1.svg`;
                      // 彩色照片路徑 (立委個人頁面使用的照片)
                      const colorImgPath = `/legislators/${legislatorName}.jpg`;
                      
                      return (
                        <div class="wanted-poster min-w-[150px] w-[150px] flex-shrink-0">
                          <a href={`/legislator/${legislatorName}`} class="block">
                            <div class="bg-amber-100 dark:bg-amber-800 border-2 border-red-800 rounded-md overflow-hidden shadow-lg p-2 transform hover:-rotate-2 transition-transform mx-2">
                              <div class="text-center text-red-800 dark:text-red-400 font-bold border-b-2 border-red-800 mb-2 pb-1">
                                <span class="text-xs">通緝</span>
                              </div>
                              <div class="relative aspect-square overflow-hidden mb-2 group">
                                <img 
                                  src={imgPath} 
                                  alt={`立委 ${legislatorName} 照片`} 
                                  class="w-full h-full object-cover grayscale sepia-[30%] transition-all duration-300 group-hover:opacity-0"
                                  onerror={`this.onerror=null; this.src='/legislators/placeholder-1.svg'`}
                                />
                                {/* 滑鼠懸停時顯示的彩色照片 */}
                                <img 
                                  src={colorImgPath} 
                                  alt={`${legislatorName}的照片`} 
                                  class="absolute inset-0 w-full h-full object-cover opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-110"
                                  onerror={`this.onerror=null; this.src='/legislators/placeholder-1.svg'`}
                                />
                                <div class="absolute inset-0 border-4 border-amber-800 opacity-20 pointer-events-none"></div>
                              </div>
                              <div class="text-center font-bold text-red-900 dark:text-amber-400 text-sm">
                                {legislatorName}
                              </div>
                            </div>
                          </a>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
            
            <!-- 立委名稱標籤 -->
            <div class="flex flex-wrap gap-2 mt-4">
              {relatedLegislators.map((legislator: string) => (
                <a 
                  href={`/legislator/${legislator}`}
                  class="inline-flex items-center bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-md text-sm mb-2"
                >
                  {legislator}
                </a>
              ))}
            </div>
          </div>
        )}

        <!-- 相關角色 -->
        {relatedRoles.length > 0 && (
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">關注角色</h2>
            <div class="flex flex-wrap gap-2">
              {relatedRoles.map(role => (
                <a 
                  href={`/roles/${role.slug}`}
                  class="inline-flex items-center bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-800/50 text-red-600 dark:text-red-400 px-3 py-1 rounded-md text-sm"
                >
                  {role.data.title}
                </a>
              ))}
            </div>
          </div>
        )}
        {/* 移動後的返回罷免理由列表按鈕 */}
        <div class="mb-8">
          <a href="/" class="inline-flex items-center text-red-600 hover:text-red-800 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            返回罷免理由列表
          </a>
        </div>
      </div>
      
      <!-- 分享按鈕 -->
      <div class="border-t border-gray-200 dark:border-gray-700 p-6">
        <div class="flex -ml-3 sm:ml-0 flex-col gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">分享這個理由</h2>
          <div class="flex flex-nowrap gap-2 sm:overflow-visible">
            <button id="share-facebook" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md flex items-center whitespace-nowrap">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
              </svg>
              Facebook
            </button>
            <button id="share-line" class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-md flex items-center whitespace-nowrap">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2c5.514 0 10 3.592 10 8.007 0 4.917-5.145 7.961-9.91 7.961-1.937 0-3.383-.397-4.394-.644-1 .613-1.594 1.037-4.272 1.82.535-1.373.722-2.748.601-4.265-.837-1-2.025-2.4-2.025-4.872 0-4.415 4.486-8.007 10-8.007zm-3.958 10.784c-.124 0-.246-.045-.341-.127l-.858-.702c-.231-.198-.266-.557-.062-.792.203-.235.559-.27.792-.062l.498.409 1.145-1.129c.21-.207.552-.203.761.008.209.211.212.556 0 .767l-1.637 1.613c-.096.095-.224.015-.298.015zm8.45-.353c0 .337-.271.61-.608.61s-.608-.273-.608-.61v-2.375h-.61c-.336 0-.608-.272-.608-.609s.272-.609.609-.609h1.218c.336 0 .608.272.608.609v2.984zm-3.536-2.984c.335 0 .607.272.607.609v2.975c0 .336-.272.61-.607.61-.337 0-.609-.274-.609-.61v-2.975c0-.337.272-.609.609-.609zm-1.683 1.218v.383h-1.828v.384h1.218c.336 0 .609.273.609.609 0 .336-.273.609-.609.609h-1.218v.384h1.828c.336 0 .609.274.609.609 0 .336-.273.61-.609.61h-2.045l.01-.001c-.335 0-.608-.272-.608-.608v-3.583c0-.337.272-.609.608-.609h2.035c.336 0 .609.272.609.609 0 .336-.273.608-.609.608h-1.828v.396h1.828z"/>
              </svg>
              LINE
            </button>
            <button id="copy-link" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-md flex items-center whitespace-nowrap">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              複製連結
            </button>
          </div>
        </div>
      </div>
    </article>
  </div>

  <!-- 媒體查看器 Modal -->
  <div id="media-modal" class="fixed inset-0 bg-black bg-opacity-80 z-50 hidden flex items-center justify-center p-4">
    <div class="relative w-full max-w-5xl mx-auto">
      <!-- 關閉按鈕 -->
      <button id="close-modal" class="absolute top-2 right-2 text-white bg-red-600 rounded-full p-2 z-10">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
      
      <!-- 媒體容器 -->
      <div class="media-container bg-gray-900 rounded-lg overflow-hidden">
        <!-- 圖片/影片將在這裡顯示 -->
        <div id="modal-media-display" class="relative flex items-center justify-center min-h-[50vh]">
          <img id="modal-image" class="max-h-[80vh] max-w-full object-contain hidden" src="" alt="" />
          <div id="modal-video-container" class="hidden w-full aspect-video">
            <video id="modal-video" class="w-full h-full" controls playsinline>
              <source src="" type="video/mp4">
              您的瀏覽器不支援HTML5 video標籤
            </video>
          </div>
          <!-- 加載指示器 -->
          <div id="loading-indicator" class="text-white text-center">
            <svg class="animate-spin h-10 w-10 mx-auto mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p>載入中...</p>
          </div>
        </div>
        
        <!-- 說明文字 -->
        <div class="bg-gray-800 p-4">
          <p id="modal-caption" class="text-white text-center"></p>
        </div>
      </div>
      
      <!-- 導航按鈕 -->
      <div class="absolute inset-y-0 left-0 flex items-center">
        <button id="prev-media" class="bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      </div>
      <div class="absolute inset-y-0 right-0 flex items-center">
        <button id="next-media" class="bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</Layout>

<style>
  /* 上一篇/下一篇導航按鈕樣式 */
  .fixed-nav-buttons {
    position: fixed;
    left: 0;
    right: 0;
    height: 100vh;     /* 預設桌機用滿高 */
    top: 0;            /* 預設桌機從上方開始 */
    z-index: 30;
    pointer-events: none; /* 防止干擾頁面內容的點擊 */
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .nav-button {
    display: flex;
    align-items: center;
    height: 180px; /* 增加高度 */
    background-color: rgba(229, 62, 62, 0.1); /* 透明紅色背景 */
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
    color: rgba(229, 62, 62, 0.7); /* 透明紅色文字 */
    padding: 0.5rem 0.3rem;
    pointer-events: auto; /* 允許按鈕可點擊 */
    text-decoration: none;
    max-width: 3rem;
    overflow: hidden;
    position: relative; /* 確保相對定位 */
  }

  .nav-button.right-button {
    border-radius: 8px 0 0 8px;
    justify-content: flex-end;
    right: 0;
    position: absolute;
  }

  .nav-button.left-button {
    border-radius: 0 8px 8px 0;
    justify-content: flex-start;
    left: 0;
    position: absolute;
  }

  .nav-button-content {
    display: flex;
    align-items: center;
    white-space: nowrap;
    width: 100%;
  }

  .right-button .nav-button-content {
    flex-direction: row;
  }

  .left-button .nav-button-content {
    flex-direction: row-reverse;
  }
  
  /* 箭頭樣式 */
  .nav-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1.5rem;
    z-index: 2;
    position: relative;
    flex-shrink: 0;
  }
  
  .nav-arrow svg {
    filter: drop-shadow(0 0 1px rgba(229, 62, 62, 0.5));
  }
  
  .left-arrow {
    margin-right: 0;
    margin-left: -0.2rem;
  }
  
  .right-arrow {
    margin-left: 0;
    margin-right: 0.5rem;
  }

  /* Right button specific fixes */
  .right-button .nav-button-content {
    justify-content: flex-start;
  }

  .right-button .nav-arrow {
    order: 3; /* 將箭頭移到最後 */
  }

  .right-button .nav-text {
    order: 2;
  }

  .right-button .nav-image-preview {
    order: 1;
  }
  
  /* 調整按鈕內部的padding */
  .nav-button {
    padding: 0.5rem 0.3rem;
  }
  
  /* 懸停效果 */
  .nav-button:hover {
    background-color: rgba(229, 62, 62, 0.2); /* 稍微深一點的紅色背景 */
    color: rgba(229, 62, 62, 1); /* 完全不透明的紅色文字 */
    max-width: 370px; /* 展開按鈕 - 增加寬度以容納標題和圖片 */
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0.3rem;
  }
  
  .nav-button:hover .nav-text {
    opacity: 1;
    max-width: 210px;
    margin: 0 0.3rem;
  }
  
  .nav-button:hover .nav-image-preview {
    width: 140px;
    opacity: 1;
  }
  
  .nav-button:hover .nav-arrow svg {
    filter: drop-shadow(0 0 2px rgba(229, 62, 62, 0.7));
  }
  
  .nav-button:hover .nav-label {
    color: rgba(229, 62, 62, 1);
    text-shadow: 0 0 1px rgba(229, 62, 62, 0.3);
  }

  .nav-text {
    display: flex;
    flex-direction: column;
    margin: 0 0.3rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    max-width: 0;
    overflow: hidden;
  }

  .nav-label {
    font-weight: bold;
    font-size: 1.1rem;
    color: rgba(229, 62, 62, 0.9);
    margin-bottom: 0.3rem;
  }

  .nav-title-text {
    font-size: 1rem;
    font-weight: 500;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }

  /* 圖片預覽區域 */
  .nav-image-preview {
    width: 0;
    height: 120px;
    overflow: hidden;
    border-radius: 4px;
    opacity: 0;
    transition: all 0.3s ease;
    margin: 0 0.3rem;
    position: relative;
  }

  .nav-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
  
  /* 響應式設計 */
  @media (max-width: 768px) {
    .fixed-nav-buttons {
      top: auto;
      bottom: 0;
      height: 120px;    /* 改為固定底部的高度 */
      align-items: center; /* 垂直置中在這一小區塊 */
    }
    .nav-button {
      height: 100px;
      max-width: 2.2rem;
    }
    .nav-arrow {
      min-width: 1rem;
    }
    .right-button .nav-arrow {
      margin-left: 0.2rem;
      margin-right: 0;
      order: unset;
    }
    .nav-button:hover {
      max-width: 180px;
    }
    .nav-image-preview {
      height: 60px;
    }
    .nav-button:hover .nav-image-preview {
      width: 60px;
    }
    .nav-title-text {
      max-width: 90px;
      font-size: 0.9rem;
    }
    /* 新增：展開時背景不透明，確保字體可見（提升 specificity 並移到全域） */
    .nav-button.expanded {
      background-color: #fff !important;
      color: #b91c1c !important;
      box-shadow: 0 2px 12px rgba(0,0,0,0.08);
      max-width: 220px !important;
      z-index: 100;
      border: 2.5px solid #2563eb !important;
    }
    .nav-button.expanded .nav-label,
    .nav-button.expanded .nav-title-text {
      color: #b91c1c !important;
      text-shadow: none;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // 分享功能
    const shareFacebook = document.getElementById('share-facebook');
    const shareLine = document.getElementById('share-line');
    const copyLink = document.getElementById('copy-link');
    
    const pageUrl = window.location.href;
    const pageTitle = document.title;
    
    if (shareFacebook) {
      shareFacebook.addEventListener('click', () => {
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(pageUrl)}`, '_blank');
      });
    }
    
    if (shareLine) {
      shareLine.addEventListener('click', () => {
        window.open(`https://social-plugins.line.me/lineit/share?url=${encodeURIComponent(pageUrl)}`, '_blank');
      });
    }
    
    if (copyLink) {
      copyLink.addEventListener('click', () => {
        navigator.clipboard.writeText(pageUrl).then(() => {
          alert('已複製連結至剪貼簿');
        }).catch(err => {
          console.error('複製連結失敗: ', err);
        });
      });
    }
    
    // 媒體查看器
    const mediaModal = document.getElementById('media-modal');
    const closeModalBtn = document.getElementById('close-modal');
    const prevMediaBtn = document.getElementById('prev-media');
    const nextMediaBtn = document.getElementById('next-media');
    const modalImage = document.getElementById('modal-image') as HTMLImageElement;
    const modalVideo = document.getElementById('modal-video') as HTMLVideoElement;
    const modalVideoContainer = document.getElementById('modal-video-container');
    const modalCaption = document.getElementById('modal-caption');
    const loadingIndicator = document.getElementById('loading-indicator');
    
    const mediaTriggers = document.querySelectorAll('.media-trigger');
    
    // 當前媒體索引和媒體列表
    let currentMediaIndex = 0;
    let mediaItems: Array<{type: string, url?: string, youtubeId?: string, caption?: string}> = [];
    
    // 初始化媒體項目
    function initMediaItems() {
      const items = document.querySelectorAll('.media-item');
      console.log('找到媒體項目數量:', items.length);
      
      mediaItems = Array.from(items).map((item, index) => {
        const type = item.getAttribute('data-type');
        const videoUrl = item.getAttribute('data-video-url');
        const imgElement = item.querySelector('img');
        const iframeElement = item.querySelector('iframe');
        const captionElement = item.querySelector('div[set\\:html]');
        
        console.log(`媒體項目 ${index}:`, { type, videoUrl });
        
        if (type === 'video' && videoUrl && videoUrl !== 'null') {
          console.log(`找到視頻 ${index}: ${videoUrl}`);
          return {
            type: 'video',
            url: videoUrl,
            caption: captionElement?.innerHTML || ''
          };
        } else if (type === 'image' || imgElement) {
          return {
            type: 'image',
            url: imgElement?.src || '',
            caption: captionElement?.innerHTML || ''
          };
        } else if (type === 'youtube' || iframeElement) {
          const src = iframeElement?.src || '';
          let youtubeId = '';
          if (src) {
            const match = src.match(/\/embed\/([^?]+)/);
            youtubeId = match ? match[1] : '';
          }
          return {
            type: 'youtube',
            youtubeId,
            caption: captionElement?.innerHTML || ''
          };
        }
        
        return null;
      }).filter(item => item !== null);
      
      console.log('初始化媒體項目:', mediaItems);
    }
    
    // 頁面加載後初始化媒體項目
    initMediaItems();
    
    // 為圖片註冊加載完成事件
    if (modalImage) {
      modalImage.onload = () => {
        if (loadingIndicator) loadingIndicator.classList.add('hidden');
        modalImage.classList.remove('hidden');
      };
    }
    
    // 轉換YouTube URL為可嵌入格式
    function convertYouTubeUrl(url: string): string {
      // 檢查是否已經是嵌入格式
      if (url.includes('youtube.com/embed/')) {
        return url;
      }
      
      // 提取YouTube ID
      let youtubeId = '';
      
      // 處理標準YouTube URL
      const standardMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/shorts\/)([^&?\/]+)/);
      if (standardMatch && standardMatch[1]) {
        youtubeId = standardMatch[1];
      }
      
      // 如果能夠提取ID，返回嵌入URL
      if (youtubeId) {
        return `https://www.youtube.com/embed/${youtubeId}`;
      }
      
      // 無法轉換時返回原始URL
      return url;
    }
    
    // 顯示媒體查看器
    function openMediaModal(index = 0) {
      if (!mediaItems || mediaItems.length === 0) return;
      
      currentMediaIndex = index;
      
      // 隱藏所有媒體元素
      if (modalImage) modalImage.classList.add('hidden');
      if (modalVideoContainer) modalVideoContainer.classList.add('hidden');
      if (loadingIndicator) loadingIndicator.classList.remove('hidden');
      
      if (mediaModal) {
        mediaModal.classList.remove('hidden');
        // 確保flex布局正確顯示
        setTimeout(() => {
          if (mediaModal) {
            mediaModal.style.display = 'flex';
          }
        }, 10);
      }
      
      showCurrentMedia();
    }
    
    // 顯示當前選中的媒體
    function showCurrentMedia() {
      if (!mediaItems || mediaItems.length === 0) {
        console.error('沒有可顯示的媒體項目');
        return;
      }
      
      const mediaItem = mediaItems[currentMediaIndex];
      console.log('當前顯示媒體項目:', mediaItem);
      
      // 重置顯示狀態，先停止所有影片播放
      stopAllVideoPlayback();
      
      if (modalImage) modalImage.classList.add('hidden');
      if (modalVideoContainer) modalVideoContainer.classList.add('hidden');
      if (loadingIndicator) loadingIndicator.classList.remove('hidden');
      
      if (mediaItem.type === 'image') {
        // 顯示圖片
        if (modalImage) {
          modalImage.src = mediaItem.url || '';
          modalImage.alt = mediaItem.caption || '';
          // 圖片加載完成後會觸發onload事件顯示圖片
        }
      } else if (mediaItem.type === 'youtube' && mediaItem.youtubeId) {
        // 顯示YouTube影片
        if (modalVideoContainer) {
          // 創建新的iframe以避免使用HTML5 video播放YouTube
          const videoContainer = modalVideoContainer;
          videoContainer.innerHTML = '';
          
          const iframe = document.createElement('iframe');
          iframe.src = `https://www.youtube.com/embed/${mediaItem.youtubeId}`;
          iframe.className = 'w-full h-full';
          iframe.frameBorder = '0';
          iframe.allowFullscreen = true;
          
          videoContainer.appendChild(iframe);
          videoContainer.classList.remove('hidden');
          if (loadingIndicator) loadingIndicator.classList.add('hidden');
        }
      } else if (mediaItem.type === 'video' && mediaItem.url) {
        // 顯示MP4影片
        console.log('準備播放MP4影片:', mediaItem.url);
        
        if (modalVideoContainer) {
          // 完全重新創建video元素，避免潛在的問題
          modalVideoContainer.innerHTML = '';
          
          const video = document.createElement('video');
          video.className = 'w-full h-full';
          video.controls = true;
          video.playsInline = true;
          video.autoplay = true;
          video.id = 'modal-video';
          
          const source = document.createElement('source');
          source.src = mediaItem.url;
          source.type = 'video/mp4';
          
          video.appendChild(source);
          video.appendChild(document.createTextNode('您的瀏覽器不支援HTML5 video標籤'));
          
          // 添加事件監聽器
          video.addEventListener('loadeddata', () => {
            console.log('影片數據已加載');
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
          });
          
          video.addEventListener('error', (e) => {
            console.error('影片加載出錯:', e);
            if (loadingIndicator) {
              loadingIndicator.innerHTML = `<p class="text-red-500">影片加載失敗: ${mediaItem.url}</p>`;
            }
          });
          
          modalVideoContainer.appendChild(video);
          modalVideoContainer.classList.remove('hidden');
          
          // 嘗試播放影片
          video.play().catch(error => {
            console.error('無法自動播放影片:', error);
          });
        }
      } else {
        console.error('不支持的媒體類型:', mediaItem);
        if (loadingIndicator) {
          loadingIndicator.innerHTML = '<p>不支持的媒體類型</p>';
          loadingIndicator.classList.remove('hidden');
        }
      }
      
      // 更新說明文字，使用innerHTML以支持HTML標籤
      if (modalCaption) {
        modalCaption.innerHTML = mediaItem.caption || '';
      }
      
      // 更新導航按鈕可見性
      updateNavigationButtons();
    }
    
    // 更新導航按鈕可見性
    function updateNavigationButtons() {
      if (!mediaItems || mediaItems.length <= 1) {
        // 只有一個或沒有媒體項目時隱藏導航按鈕
        if (prevMediaBtn) prevMediaBtn.classList.add('hidden');
        if (nextMediaBtn) nextMediaBtn.classList.add('hidden');
        return;
      }
      
      // 顯示導航按鈕
      if (prevMediaBtn) prevMediaBtn.classList.remove('hidden');
      if (nextMediaBtn) nextMediaBtn.classList.remove('hidden');
    }
    
    // 關閉媒體查看器
    function closeMediaModal() {
      if (mediaModal) {
        mediaModal.classList.add('hidden');
        mediaModal.style.display = '';
      }
      
      // 停止所有可能的影片播放
      stopAllVideoPlayback();
    }
    
    // 停止所有影片播放
    function stopAllVideoPlayback() {
      // 處理HTML5 video元素
      const videoElements = modalVideoContainer?.querySelectorAll('video');
      if (videoElements && videoElements.length > 0) {
        videoElements.forEach(video => {
          try {
            video.pause();
            video.currentTime = 0;
            console.log('已暫停HTML5影片播放');
          } catch (e) {
            console.error('暫停影片時出錯:', e);
          }
        });
      }
      
      // 處理iframe (YouTube)
      const iframes = modalVideoContainer?.querySelectorAll('iframe');
      if (iframes && iframes.length > 0) {
        iframes.forEach(iframe => {
          try {
            // 通過重設src來停止YouTube影片播放
            const src = iframe.getAttribute('src') || '';
            iframe.setAttribute('src', '');
            console.log('已重置YouTube iframe內容');
          } catch (e) {
            console.error('重置iframe時出錯:', e);
          }
        });
      }
      
      // 確保video容器是空的
      if (modalVideoContainer) {
        modalVideoContainer.classList.add('hidden');
      }
    }
    
    // 顯示下一個媒體
    function showNextMedia() {
      if (!mediaItems || mediaItems.length <= 1) return;
      
      // 先停止當前影片播放
      stopAllVideoPlayback();
      
      currentMediaIndex = (currentMediaIndex + 1) % mediaItems.length;
      showCurrentMedia();
    }
    
    // 顯示上一個媒體
    function showPrevMedia() {
      if (!mediaItems || mediaItems.length <= 1) return;
      
      // 先停止當前影片播放
      stopAllVideoPlayback();
      
      currentMediaIndex = (currentMediaIndex - 1 + mediaItems.length) % mediaItems.length;
      showCurrentMedia();
    }
    
    // 為按鈕註冊事件處理程序
    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', (e) => {
        e.preventDefault();
        closeMediaModal();
      });
    }
    
    if (prevMediaBtn) {
      prevMediaBtn.addEventListener('click', (e) => {
        e.preventDefault();
        showPrevMedia();
      });
    }
    
    if (nextMediaBtn) {
      nextMediaBtn.addEventListener('click', (e) => {
        e.preventDefault();
        showNextMedia();
      });
    }
    
    // 按ESC鍵關閉查看器
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        closeMediaModal();
      } else if (e.key === 'ArrowRight') {
        showNextMedia();
      } else if (e.key === 'ArrowLeft') {
        showPrevMedia();
      }
    });
    
    // 為媒體項目添加點擊事件
    mediaTriggers.forEach((trigger, index) => {
      trigger.addEventListener('click', () => {
        const dataIndex = trigger.getAttribute('data-index');
        const mediaIndex = dataIndex ? parseInt(dataIndex) : index;
        console.log(`點擊了媒體項目，索引: ${mediaIndex}`);
        openMediaModal(mediaIndex);
      });
    });
    
    // 點擊模態框背景關閉
    if (mediaModal) {
      mediaModal.addEventListener('click', (e) => {
        if (e.target === mediaModal) {
          closeMediaModal();
        }
      });
    }

    // 處理立委通緝令跑馬燈
    const setupMarquee = () => {
      const marqueeTrack = document.querySelector('.wanted-marquee-track');
      const marqueeContainer = document.querySelector('.wanted-marquee-container');
      
      if (marqueeTrack && marqueeContainer) {
        // 先獲取當前所有立委卡片並計算總寬度
        const posters = marqueeTrack.querySelectorAll('.wanted-poster');
        const containerWidth = marqueeContainer.clientWidth;
        
        if (posters.length > 0) {
          // 計算一組卡片的總寬度
          let postersWidth = 0;
          posters.forEach(poster => {
            postersWidth += (poster as HTMLElement).offsetWidth;
          });
          
          // 計算需要複製幾組才能填滿容器 (至少2組確保無縫循環，如果立委太少就多複製幾份)
          const requiredSets = Math.max(Math.ceil(containerWidth * 3 / postersWidth), 2);
          
          // 複製足夠的元素以填滿容器
          for (let i = 0; i < requiredSets - 1; i++) {
            posters.forEach(poster => {
              const clone = poster.cloneNode(true);
              marqueeTrack.appendChild(clone);
            });
          }
          
          // 為了無縫循環效果，再複製一份原始集合
          const allPosters = marqueeTrack.querySelectorAll('.wanted-poster');
          const visiblePosters = Array.from(allPosters).slice(0, posters.length);
          
          visiblePosters.forEach(poster => {
            const clone = poster.cloneNode(true);
            marqueeTrack.appendChild(clone);
          });
        }
        
        // 設置CSS動畫
        const track = marqueeTrack;
        const trackWidth = track.scrollWidth / 2;
        
        // 根據立委數量調整速度 - 少於3人速度更快，大於等於3人速度較慢
        const speedDivisor = posters.length < 3 ? 100 : 800;
        const duration = trackWidth / speedDivisor; // 調整速度，數字越大越慢
        
        // 應用CSS動畫
        (track as HTMLElement).style.animationDuration = `${duration}s`;
        track.classList.add('animate-marquee');
        
        // 設置CSS
        const style = document.createElement('style');
        style.textContent = `
          @keyframes marquee {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
          }
          
          .animate-marquee {
            animation: marquee ${duration}s linear infinite;
          }
          
          /* 滑鼠懸停時暫停動畫 */
          .wanted-marquee-container:hover .animate-marquee {
            animation-play-state: paused;
          }
          
          /* 移除卡片間間隙 */
          .wanted-marquee-track {
            display: flex;
            flex-wrap: nowrap;
            gap: 0;
          }
          
          .wanted-poster {
            flex-shrink: 0;
          }
          
          /* 新增樣式 */
          .wanted-poster a {
            text-decoration: none;
            display: block;
          }
        `;
        document.head.appendChild(style);
      }
    };
    
    // 頁面加載後設置跑馬燈
    setupMarquee();

    // 在頁面加載後重新綁定所有媒體觸發器的事件
    function rebindMediaTriggers() {
      document.querySelectorAll('.media-trigger').forEach((trigger) => {
        // 移除原有的事件處理器
        const newTrigger = trigger.cloneNode(true);
        if (trigger.parentNode) {
          trigger.parentNode.replaceChild(newTrigger, trigger);
        }
        
        // 添加新的事件處理器
        newTrigger.addEventListener('click', function(this: Element) {
          const dataIndex = this.getAttribute('data-index');
          const parentItem = this.closest('.media-item');
          const itemIndex = parentItem ? parseInt(parentItem.getAttribute('data-index') || '') : -1;
          const mediaIndex = dataIndex ? parseInt(dataIndex) : itemIndex;
          
          console.log(`點擊了媒體觸發器，索引: ${mediaIndex}`);
          if (mediaIndex >= 0 && mediaIndex < mediaItems.length) {
            openMediaModal(mediaIndex);
          } else {
            console.error(`無效的媒體索引: ${mediaIndex}`);
          }
        });
      });
      
      console.log('已重新綁定所有媒體觸發器事件');
    }
    
    // 頁面加載後執行
    setTimeout(rebindMediaTriggers, 500);

    // ========== 新增：上/下一筆按鈕在 mobile view 的點擊行為 ========== //
    function isMobile() {
      return window.innerWidth <= 768;
    }
    // 狀態：目前哪個按鈕展開
    let navExpanded: 'prev' | 'next' | null = null;

    // 取得按鈕元素
    const leftBtn = document.querySelector('.nav-button.left-button') as HTMLElement | null;
    const rightBtn = document.querySelector('.nav-button.right-button') as HTMLElement | null;

    // 幫按鈕加上展開 class（展開前先移除所有 expanded）
    function expandNavButton(btn: HTMLElement | null) {
      document.querySelectorAll('.nav-button.expanded').forEach(el => el.classList.remove('expanded'));
      if (btn) btn.classList.add('expanded');
    }
    function collapseNavButton(btn: HTMLElement | null) {
      if (btn) btn.classList.remove('expanded');
    }
    function collapseAllNavButtons() {
      collapseNavButton(leftBtn);
      collapseNavButton(rightBtn);
      navExpanded = null;
    }

    // 點擊事件
    if (leftBtn) {
      leftBtn.addEventListener('click', function(e) {
        if (isMobile()) {
          e.stopPropagation(); // 防止冒泡
          if (navExpanded !== 'prev') {
            e.preventDefault();
            collapseAllNavButtons();
            expandNavButton(leftBtn);
            navExpanded = 'prev';
          } else {
            navExpanded = null;
            // 不阻止預設行為
          }
        }
      });
    }
    if (rightBtn) {
      rightBtn.addEventListener('click', function(e) {
        if (isMobile()) {
          e.stopPropagation(); // 防止冒泡
          if (navExpanded !== 'next') {
            e.preventDefault();
            collapseAllNavButtons();
            expandNavButton(rightBtn);
            navExpanded = 'next';
          } else {
            navExpanded = null;
            // 不阻止預設行為
          }
        }
      });
    }
    // 點擊頁面其他地方時收合
    document.addEventListener('click', function(e) {
      if (isMobile()) {
        const target = e.target;
        if (!(target instanceof HTMLElement) || !target.closest('.nav-button')) {
          collapseAllNavButtons();
        }
      }
    });
    // ========== 新增結束 ========== //
  });
</script> 