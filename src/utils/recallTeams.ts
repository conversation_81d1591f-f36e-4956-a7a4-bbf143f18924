export interface RecallTeam {
  name: string;
  url: string;
}

export const recallTeamMap: Record<string, RecallTeam> = {
  "林沛祥": { name: "基隆絕沛", url: "https://linktr.ee/keelungreplay?ltclid=" },
  "王鴻薇": { name: "山除薇害", url: "https://linktr.ee/wanghongwei2025gg?utm_source=linktree_profile_share&ltsid=1ecda328-baa8-4ab0-aeb0-8c3403043c76&ltclid=" },
  "李彥秀": { name: "港湖除銹", url: "https://linktr.ee/recall.giansiu?ltclid=" },
  "羅智強": { name: "大安強強滾", url: "https://www.daanreboot.tw/?ltclid=" },
  "徐巧芯": { name: "剷除黑芯", url: "https://linktr.ee/recall.hsu900?ltclid=" },
  "賴士葆": { name: "文山退葆", url: "https://linktr.ee/banish.laishyhbao?ltclid=" },
  "洪孟楷": { name: "刻不容楷", url: "https://linktr.ee/nomorered?ltclid=" },
  "葉元之": { name: "板橋大刪元", url: "https://linktr.ee/banqiaobigsanyuan?ltclid=" },
  "張智倫": { name: "倫刪立舒", url: "https://linktr.ee/bettershuanghe?ltclid=" },
  "林德福": { name: "無獻清林", url: "https://linktr.ee/bettershuanghe?ltclid=" },
  "羅明才": { name: "拔羅波", url: "https://linktr.ee/baluobo1111?ltclid=" },
  "廖先翔": { name: "齊心斷翔", url: "https://linktr.ee/12scissorkick?ltclid=" },
  "牛煦庭": { name: "桃汰牛氓", url: "https://linktr.ee/birdcrushcow?ltclid=" },
  "涂權吉": { name: "全力一擊", url: "https://linktr.ee/taoyuanboxerrecall?ltclid=" },
  "魯明哲": { name: "壢拔魯蛋", url: "https://linktr.ee/chunglirecall?ltclid=" },
  "萬美玲": { name: "萬罷了沒啦", url: "https://linkgoods.com/wanbyela444?ltclid=" },
  "呂玉玲": { name: "罷呂清玲", url: "https://linktr.ee/recalllu0604?ltclid=" },
  "邱若華": { name: "桃六刪邱", url: "https://linktr.ee/recallt6chiu?ltclid=" },
  "鄭正鈐": { name: "罷鈐反中", url: "https://linktr.ee/recall.hccc?ltclid=" },
  "徐欣瑩": { name: "除舊汰欣", url: "https://linktr.ee/recall.hsu.hsin.ying?ltclid=" },
  "林思銘": { name: "撕除惡銘", url: "https://linktr.ee/recall.lsm?ltclid=" },
  "邱鎮軍": { name: "抗鎮行動", url: "https://sites.google.com/view/ba-miaoli-lawmaker/index?authuser=0&ltclid=" },
  "顏寬恆": { name: "中二解顏", url: "https://bento.me/taichung2jyen?ltclid=" },
  "楊瓊瓔": { name: "中三拒瓔", url: "https://linktr.ee/recallvote_taichung3rd?ltclid=" },
  "廖偉翔": { name: "展翅廢翔", url: "https://linktr.ee/tc4.recall?ltclid=" },
  "黃健豪": { name: "斷健行動", url: "https://linktr.ee/recallvote_beibeitun?ltclid=" },
  "羅廷瑋": { name: "敲羅行動", url: "https://linktr.ee/beat__low?ltclid=" },
  "江啟臣": { name: "一罷擊臣", url: "https://linktr.ee/taichung8th.recall?ltclid=" },
  "謝衣鳳": { name: "罷免缺衣不可", url: "https://linktr.ee/bamain_c1feng?ltclid=" },
  "馬文君": { name: "ALL罷馬", url: "https://magenta-pear-z108l8.mystrikingly.com/?ltclid=" },
  "游顥": { name: "去游除垢", url: "https://linktr.ee/toueryouout?ltclid=" },
  "丁學忠": { name: "雲林拔釘", url: "https://linktr.ee/badingact?ltclid=" },
  "傅崐萁": { name: "微光花蓮", url: "https://linktr.ee/shimmer.tw?ltclid=" },
  "黃建賓": { name: "挫賓行動", url: "https://line.me/ti/g2/durnftvfKTx6RIYBHo3-hzzwNR24TgWCCnaYQA?utm_source=invitation&utm_medium=link_copy&utm_campaign=default&ltclid=" },
  "陳玉珍": { name: "珍礙金門", url: "https://linktr.ee/goodbyebadtank?ltclid=" },
  "陳雪生": { name: "馬祖剷雪大隊", url: "https://linktr.ee/saynotosnow?ltclid=" },
  "陳超明": { name: "罷超密令", url: "" }
};

export function getRecallTeam(legislatorName: string): RecallTeam | null {
  return recallTeamMap[legislatorName] || null;
} 