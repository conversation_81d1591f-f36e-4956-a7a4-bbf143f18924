import type { ControversyCollection } from '../data/controversies/types';
import { getCollection } from 'astro:content';

/**
 * 根據立委名稱獲取其爭議事項
 * 
 * @param name 立委名稱
 * @returns 爭議事項集合，若未找到則返回空數組
 */
export async function getControversies(name: string): Promise<ControversyCollection> {
  try {
    // 從TinaCMS獲取爭議事項
    // @ts-ignore - 由於controversies是新增的集合，先用ignore繞過型別檢查
    const allControversies = await getCollection('controversies');
    if (!allControversies) {
      throw new Error('無法獲取爭議事項集合');
    }
    
    const legislatorControversies = allControversies
      .filter(item => item.data.legislatorName === name)
      .sort((a, b) => {
        // 如果有orderID則按照orderID排序，否則按照默認順序
        if (a.data.orderID !== undefined && b.data.orderID !== undefined) {
          return a.data.orderID - b.data.orderID;
        }
        return 0;
      })
      .map(item => ({
        title: item.data.title,
        content: item.data.content,
        reflections: item.data.reflections || [],
        tags: item.data.tags || []
      }));

    return legislatorControversies;
  } catch (error) {
    console.warn(`無法載入 ${name} 的爭議事項：`, error);
    
    // 如果TinaCMS讀取失敗，嘗試從舊的檔案系統讀取
    try {
      // 動態導入爭議事項檔案
      const module = await import(`../data/controversies/${name}.ts`);
      return module.default;
    } catch (secondError) {
      console.warn(`找不到立委 ${name} 的爭議事項檔案：`, secondError);
      return [];
    }
  }
} 