// This file contains global type declarations

// html2canvas declaration
declare module 'html2canvas' {
  export default function html2canvas(
    element: HTMLElement,
    options?: {
      scale?: number;
      useCORS?: boolean;
      backgroundColor?: string;
      logging?: boolean;
      allowTaint?: boolean;
      foreignObjectRendering?: boolean;
      removeContainer?: boolean;
    }
  ): Promise<HTMLCanvasElement>;
}

// Global declarations
interface Window {
  html2canvas: typeof import('html2canvas').default;
}

// Modern Clipboard API
interface ClipboardItem {
  readonly types: string[];
  getType(type: string): Promise<Blob>;
}

interface ClipboardItemData {
  [mimeType: string]: Blob;
}

declare var ClipboardItem: {
  prototype: ClipboardItem;
  new(itemData: ClipboardItemData): ClipboardItem;
};

interface Clipboard {
  read(): Promise<ClipboardItems>;
  write(data: ClipboardItems): Promise<void>;
}

type ClipboardItems = ClipboardItem[]; 