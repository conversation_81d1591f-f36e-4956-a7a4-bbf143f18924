# 100個罷免的理由

這是一個提供平台讓使用者分享35位立委問題行為的網站，讓民眾注意到這些問題，共同監督民主政治。

## 特色

- 35位立委的「角色卡」展示
- 分享和瀏覽罷免理由
- 立委個人頁面
- 重大事件時間軸
- 標籤系統

## 技術

- [Astro.js](https://astro.build/)
- [Tailwind CSS](https://tailwindcss.com/)

## 安裝與開發

### 前置需求

- Node.js 16 或以上
- npm 或 yarn

### 安裝步驟

1. 安裝相依套件

```bash
npm install
```

2. 啟動開發伺服器

```bash
npm run dev
```

3. 建置生產版本

```bash
npm run build
```

4. 預覽生產版本

```bash
npm run preview
```

## 專案結構

```
/
├── public/             # 靜態資源
│   └── legislators/    # 立委圖片
├── src/
│   ├── components/     # UI元件
│   ├── layouts/        # 頁面佈局
│   ├── pages/          # 頁面
│   └── data/           # 資料檔案
└── package.json
```

## 頁面說明

- **首頁**：主視覺立委卡片牆
- **立委個人頁**：顯示立委詳細資訊和相關罷免理由
- **重大事件頁**：列出重要事件
- **時間軸頁**：按時間順序展示事件

## 貢獻指南

歡迎提交問題或功能建議。

## 授權條款

本專案使用 MIT 授權協議 - 查看 [LICENSE](LICENSE) 檔案了解更多詳情。

```sh
npm create astro@latest -- --template minimal
```

[![Open in StackBlitz](https://developer.stackblitz.com/img/open_in_stackblitz.svg)](https://stackblitz.com/github/withastro/astro/tree/latest/examples/minimal)
[![Open with CodeSandbox](https://assets.codesandbox.io/github/button-edit-lime.svg)](https://codesandbox.io/p/sandbox/github/withastro/astro/tree/latest/examples/minimal)
[![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://codespaces.new/withastro/astro?devcontainer_path=.devcontainer/minimal/devcontainer.json)

> 🧑‍🚀 **Seasoned astronaut?** Delete this file. Have fun!

## 🚀 Project Structure

Inside of your Astro project, you'll see the following folders and files:

```text
/
├── public/
├── src/
│   └── pages/
│       └── index.astro
└── package.json
```

Astro looks for `.astro` or `.md` files in the `src/pages/` directory. Each page is exposed as a route based on its file name.

There's nothing special about `src/components/`, but that's where we like to put any Astro/React/Vue/Svelte/Preact components.

Any static assets, like images, can be placed in the `public/` directory.

## 🧞 Commands

All commands are run from the root of the project, from a terminal:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `