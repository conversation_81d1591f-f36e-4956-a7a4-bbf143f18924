// tina/config.ts
import { defineConfig } from "tinacms";
var branch = process.env.HEAD || process.env.VERCEL_GIT_COMMIT_REF || "main";
var config_default = defineConfig({
  branch,
  build: {
    outputFolder: "admin",
    publicFolder: "public"
  },
  media: {
    tina: {
      mediaRoot: "public/uploads",
      publicFolder: "public"
    }
  },
  schema: {
    collections: [
      {
        name: "legislators",
        label: "\u7ACB\u6CD5\u59D4\u54E1",
        path: "src/content/legislators",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${values?.name?.toLowerCase().trim().replace(/[^\w\s-]/g, "").replace(/[\s_-]+/g, "-").replace(/^-+|-+$/g, "")}`;
            }
          }
        },
        fields: [
          {
            type: "string",
            name: "name",
            label: "\u59D3\u540D",
            isTitle: true,
            required: true
          },
          {
            type: "string",
            name: "party",
            label: "\u653F\u9EE8",
            required: true
          },
          {
            type: "string",
            name: "district",
            label: "\u9078\u5340",
            required: true
          },
          {
            type: "image",
            name: "imageUrl",
            label: "\u7167\u7247"
          },
          {
            type: "string",
            name: "nicknames",
            label: "\u6C5F\u6E56\u7A31\u865F",
            list: true,
            description: "\u7ACB\u59D4\u7684\u6C5F\u6E56\u7A31\u865F\uFF0C\u53EF\u4EE5\u6709\u591A\u500B\uFF0C\u9078\u586B"
          },
          {
            type: "rich-text",
            name: "body",
            label: "\u7C21\u4ECB",
            isBody: true
          }
        ]
      },
      {
        name: "roles",
        label: "\u89D2\u8272\u5206\u985E",
        path: "src/content/roles",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${values?.title?.toLowerCase().trim().replace(/[^\w\s-]/g, "").replace(/[\s_-]+/g, "-").replace(/^-+|-+$/g, "")}`;
            }
          }
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "\u89D2\u8272\u540D\u7A31",
            isTitle: true,
            required: true
          },
          {
            type: "image",
            name: "icon",
            label: "\u89D2\u8272\u5716\u793A"
          },
          {
            type: "number",
            name: "orderID",
            label: "\u6392\u5E8F\u7DE8\u865F",
            description: "\u7528\u65BC\u6C7A\u5B9A\u89D2\u8272\u7684\u986F\u793A\u9806\u5E8F"
          },
          {
            type: "rich-text",
            name: "description",
            label: "\u89D2\u8272\u8AAA\u660E",
            isBody: true
          }
        ]
      },
      {
        name: "reasons",
        label: "\u7F77\u514D\u7406\u7531",
        path: "src/content/reasons",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${values?.orderID || Date.now()}-${values?.title?.toLowerCase().trim().replace(/[^\w\s-]/g, "").replace(/[\s_-]+/g, "-").replace(/^-+|-+$/g, "")}`;
            }
          }
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "\u6A19\u984C",
            isTitle: true,
            required: true
          },
          {
            type: "rich-text",
            name: "body",
            label: "\u5167\u5BB9",
            isBody: true
          },
          {
            type: "number",
            name: "popularity",
            label: "\u71B1\u9580\u5EA6",
            description: "0-100\u7684\u6578\u503C\uFF0C\u4EE3\u8868\u7406\u7531\u7684\u71B1\u9580\u7A0B\u5EA6"
          },
          {
            type: "number",
            name: "orderID",
            label: "\u6392\u5E8F\u7DE8\u865F",
            description: "\u7528\u65BC\u6C7A\u5B9A\u5167\u5BB9\u7684\u6392\u5E8F\u9806\u5E8F"
          },
          {
            type: "datetime",
            name: "publishDate",
            label: "\u767C\u4F48\u65E5\u671F"
          },
          {
            type: "string",
            name: "relatedLegislators",
            label: "\u95DC\u806F\u7ACB\u59D4",
            list: true,
            ui: {
              component: "select"
            },
            options: [
              "all",
              "\u6797\u6C9B\u7965",
              "\u738B\u9D3B\u8587",
              "\u674E\u5F65\u79C0",
              "\u7F85\u667A\u5F37",
              "\u5F90\u5DE7\u82AF",
              "\u8CF4\u58EB\u8446",
              "\u8449\u5143\u4E4B",
              "\u6D2A\u5B5F\u6977",
              "\u5F35\u667A\u502B",
              "\u6797\u5FB7\u798F",
              "\u7F85\u660E\u624D",
              "\u5ED6\u5148\u7FD4",
              "\u725B\u7166\u5EAD",
              "\u6D82\u6B0A\u5409",
              "\u9B6F\u660E\u54F2",
              "\u842C\u7F8E\u73B2",
              "\u5442\u7389\u73B2",
              "\u90B1\u82E5\u83EF",
              "\u912D\u6B63\u9210",
              "\u5F90\u6B23\u7469",
              "\u6797\u601D\u9298",
              "\u90B1\u93AE\u8ECD",
              "\u984F\u5BEC\u6046",
              "\u694A\u74CA\u74D4",
              "\u5ED6\u5049\u7FD4",
              "\u9EC3\u5065\u8C6A",
              "\u7F85\u5EF7\u744B",
              "\u6C5F\u555F\u81E3",
              "\u8B1D\u8863\u9CF3",
              "\u99AC\u6587\u541B",
              "\u6E38\u9865",
              "\u4E01\u5B78\u5FE0",
              "\u5085\u5D10\u8401",
              "\u9EC3\u5EFA\u8CD3",
              "\u9673\u7389\u73CD",
              "\u9673\u96EA\u751F",
              "\u9673\u8D85\u660E"
            ]
          },
          {
            type: "string",
            name: "relatedRoles",
            label: "\u9069\u7528\u89D2\u8272",
            list: true,
            ui: {
              component: "select"
            },
            options: [
              "\u4E0A\u73ED\u65CF",
              "\u5B78\u751F",
              "\u5BB6\u9577",
              "\u9577\u8F29",
              "\u570B\u6C11\u9EE8\u54E1",
              "\u6C11\u773E\u9EE8\u54E1"
            ]
          },
          {
            type: "object",
            name: "media",
            label: "\u591A\u5A92\u9AD4\u5167\u5BB9",
            list: true,
            fields: [
              {
                type: "string",
                name: "type",
                label: "\u985E\u578B",
                options: ["image", "video", "youtube"],
                required: true
              },
              {
                type: "image",
                name: "url",
                label: "\u6A94\u6848",
                required: false
              },
              {
                type: "string",
                name: "youtubeId",
                label: "YouTube ID",
                description: "\u4F8B\u5982: https://www.youtube.com/watch?v=dQw4w9WgXcQ \u4E2D\u7684 dQw4w9WgXcQ",
                required: false
              },
              {
                type: "string",
                name: "caption",
                label: "\u8AAA\u660E\u6587\u5B57"
              }
            ]
          },
          {
            type: "string",
            name: "tags",
            label: "\u6A19\u7C64",
            list: true,
            ui: {
              component: "tags"
            }
          }
        ]
      },
      {
        name: "media",
        label: "\u5A92\u9AD4\u5167\u5BB9",
        path: "src/content/media",
        format: "mdx",
        ui: {
          filename: {
            // Filename generator for new documents
            slugify: (values) => {
              return `${values?.orderID || Date.now()}-${values?.title?.toLowerCase().trim().replace(/[^\w\s-]/g, "").replace(/[\s_-]+/g, "-").replace(/^-+|-+$/g, "")}`;
            }
          }
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "\u6A19\u984C",
            isTitle: true,
            required: true
          },
          {
            type: "string",
            name: "description",
            label: "\u63CF\u8FF0",
            ui: {
              component: "textarea"
            }
          },
          {
            type: "datetime",
            name: "date",
            label: "\u65E5\u671F"
          },
          {
            type: "number",
            name: "orderID",
            label: "\u6392\u5E8FID",
            description: "\u7528\u65BC\u6C7A\u5B9A\u5167\u5BB9\u7684\u6392\u5E8F\u9806\u5E8F"
          },
          {
            type: "string",
            name: "youtubeId",
            label: "YouTube ID",
            description: "\u4F8B\u5982: https://www.youtube.com/watch?v=dQw4w9WgXcQ \u4E2D\u7684 dQw4w9WgXcQ"
          },
          {
            type: "image",
            name: "image",
            label: "\u4E3B\u8981\u5716\u7247"
          },
          {
            type: "string",
            name: "source",
            label: "\u4F86\u6E90"
          },
          {
            type: "string",
            name: "tags",
            label: "\u6A19\u7C64",
            list: true,
            ui: {
              component: "tags"
            }
          },
          {
            type: "string",
            name: "groups",
            label: "\u5206\u7D44",
            list: true,
            options: [
              "\u7F77\u514D\u91D1\u66F2",
              "\u7F77\u514D\u6BCF\u65E5\u4E00\u5B57",
              "\u7CBE\u5F69\u5F71\u7247\u96C6\u9326",
              "\u6797\u6C9B\u7965",
              "\u738B\u9D3B\u8587",
              "\u674E\u5F65\u79C0",
              "\u7F85\u667A\u5F37",
              "\u5F90\u5DE7\u82AF",
              "\u8CF4\u58EB\u8446",
              "\u8449\u5143\u4E4B",
              "\u6D2A\u5B5F\u6977",
              "\u5F35\u667A\u502B",
              "\u6797\u5FB7\u798F",
              "\u7F85\u660E\u624D",
              "\u5ED6\u5148\u7FD4",
              "\u725B\u7166\u5EAD",
              "\u6D82\u6B0A\u5409",
              "\u9B6F\u660E\u54F2",
              "\u842C\u7F8E\u73B2",
              "\u5442\u7389\u73B2",
              "\u90B1\u82E5\u83EF",
              "\u912D\u6B63\u9210",
              "\u5F90\u6B23\u7469",
              "\u6797\u601D\u9298",
              "\u90B1\u93AE\u8ECD",
              "\u984F\u5BEC\u6046",
              "\u694A\u74CA\u74D4",
              "\u5ED6\u5049\u7FD4",
              "\u9EC3\u5065\u8C6A",
              "\u7F85\u5EF7\u744B",
              "\u6C5F\u555F\u81E3",
              "\u8B1D\u8863\u9CF3",
              "\u99AC\u6587\u541B",
              "\u6E38\u9865",
              "\u4E01\u5B78\u5FE0",
              "\u5085\u5D10\u8401",
              "\u9EC3\u5EFA\u8CD3",
              "\u9673\u7389\u73CD",
              "\u9673\u96EA\u751F",
              "\u9673\u8D85\u660E"
            ]
          },
          {
            type: "rich-text",
            name: "body",
            label: "\u5167\u5BB9",
            isBody: true
          }
        ]
      },
      {
        name: "memes",
        label: "\u6897\u5716\u5167\u5BB9",
        path: "src/content/memes",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${Date.now()}-${values?.title?.toLowerCase().trim().replace(/[^\w\s-]/g, "").replace(/[\s_-]+/g, "-").replace(/^-+|-+$/g, "")}`;
            }
          }
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "\u6A19\u984C",
            isTitle: true,
            required: true
          },
          {
            type: "string",
            name: "description",
            label: "\u63CF\u8FF0",
            ui: {
              component: "textarea"
            }
          },
          {
            type: "image",
            name: "imageUrl",
            label: "\u6897\u5716\u5716\u7247",
            required: true
          },
          {
            type: "string",
            name: "tags",
            label: "\u6A19\u7C64",
            list: true,
            ui: {
              component: "tags"
            }
          },
          {
            type: "string",
            name: "legislatorNames",
            label: "\u76F8\u95DC\u7ACB\u59D4",
            description: "\u8207\u6B64\u6897\u5716\u76F8\u95DC\u7684\u7ACB\u59D4",
            list: true,
            options: [
              "\u6797\u6C9B\u7965",
              "\u738B\u9D3B\u8587",
              "\u674E\u5F65\u79C0",
              "\u7F85\u667A\u5F37",
              "\u5F90\u5DE7\u82AF",
              "\u8CF4\u58EB\u8446",
              "\u8449\u5143\u4E4B",
              "\u6D2A\u5B5F\u6977",
              "\u5F35\u667A\u502B",
              "\u6797\u5FB7\u798F",
              "\u7F85\u660E\u624D",
              "\u5ED6\u5148\u7FD4",
              "\u725B\u7166\u5EAD",
              "\u6D82\u6B0A\u5409",
              "\u9B6F\u660E\u54F2",
              "\u842C\u7F8E\u73B2",
              "\u5442\u7389\u73B2",
              "\u90B1\u82E5\u83EF",
              "\u912D\u6B63\u9210",
              "\u5F90\u6B23\u7469",
              "\u6797\u601D\u9298",
              "\u90B1\u93AE\u8ECD",
              "\u984F\u5BEC\u6046",
              "\u694A\u74CA\u74D4",
              "\u5ED6\u5049\u7FD4",
              "\u9EC3\u5065\u8C6A",
              "\u7F85\u5EF7\u744B",
              "\u6C5F\u555F\u81E3",
              "\u8B1D\u8863\u9CF3",
              "\u99AC\u6587\u541B",
              "\u6E38\u9865",
              "\u4E01\u5B78\u5FE0",
              "\u5085\u5D10\u8401",
              "\u9EC3\u5EFA\u8CD3",
              "\u9673\u7389\u73CD",
              "\u9673\u96EA\u751F",
              "\u9673\u8D85\u660E"
            ]
          },
          {
            type: "rich-text",
            name: "body",
            label: "\u5167\u5BB9",
            isBody: true
          }
        ]
      },
      {
        name: "controversies",
        label: "\u722D\u8B70\u4E8B\u9805",
        path: "src/content/controversies",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${values?.orderID || Date.now()}-${values?.title?.toLowerCase().trim().replace(/[^\w\s-]/g, "").replace(/[\s_-]+/g, "-").replace(/^-+|-+$/g, "")}`;
            }
          }
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "\u6A19\u984C",
            isTitle: true,
            required: true
          },
          {
            type: "string",
            name: "content",
            label: "\u5167\u5BB9",
            ui: {
              component: "textarea"
            },
            required: true
          },
          {
            type: "string",
            name: "reflections",
            label: "\u53CD\u601D\u8207\u63D0\u554F",
            list: true,
            ui: {
              component: "textarea"
            }
          },
          {
            type: "number",
            name: "orderID",
            label: "\u6392\u5E8F\u7DE8\u865F",
            description: "\u7528\u65BC\u6C7A\u5B9A\u722D\u8B70\u4E8B\u9805\u7684\u986F\u793A\u9806\u5E8F"
          },
          {
            type: "string",
            name: "legislatorName",
            label: "\u76F8\u95DC\u7ACB\u59D4",
            description: "\u6B64\u722D\u8B70\u4E8B\u9805\u6240\u5C6C\u7684\u7ACB\u59D4",
            required: true,
            options: [
              "\u6797\u6C9B\u7965",
              "\u738B\u9D3B\u8587",
              "\u674E\u5F65\u79C0",
              "\u7F85\u667A\u5F37",
              "\u5F90\u5DE7\u82AF",
              "\u8CF4\u58EB\u8446",
              "\u8449\u5143\u4E4B",
              "\u6D2A\u5B5F\u6977",
              "\u5F35\u667A\u502B",
              "\u6797\u5FB7\u798F",
              "\u7F85\u660E\u624D",
              "\u5ED6\u5148\u7FD4",
              "\u725B\u7166\u5EAD",
              "\u6D82\u6B0A\u5409",
              "\u9B6F\u660E\u54F2",
              "\u842C\u7F8E\u73B2",
              "\u5442\u7389\u73B2",
              "\u90B1\u82E5\u83EF",
              "\u912D\u6B63\u9210",
              "\u5F90\u6B23\u7469",
              "\u6797\u601D\u9298",
              "\u90B1\u93AE\u8ECD",
              "\u984F\u5BEC\u6046",
              "\u694A\u74CA\u74D4",
              "\u5ED6\u5049\u7FD4",
              "\u9EC3\u5065\u8C6A",
              "\u7F85\u5EF7\u744B",
              "\u6C5F\u555F\u81E3",
              "\u8B1D\u8863\u9CF3",
              "\u99AC\u6587\u541B",
              "\u6E38\u9865",
              "\u4E01\u5B78\u5FE0",
              "\u5085\u5D10\u8401",
              "\u9EC3\u5EFA\u8CD3",
              "\u9673\u7389\u73CD",
              "\u9673\u96EA\u751F",
              "\u9673\u8D85\u660E"
            ]
          },
          {
            type: "string",
            name: "tags",
            label: "\u6A19\u7C64",
            list: true,
            ui: {
              component: "tags"
            }
          },
          {
            type: "rich-text",
            name: "body",
            label: "\u8A73\u7D30\u5167\u5BB9",
            isBody: true
          }
        ]
      }
    ]
  }
});
export {
  config_default as default
};
