{"DocumentConnection": {"type": "DocumentConnection", "resolveType": "multiCollectionDocumentList", "collections": ["legislators", "roles", "reasons", "media", "memes", "controversies"]}, "Node": {"type": "Node", "resolveType": "nodeDocument"}, "DocumentNode": {"type": "DocumentNode", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "Legislators": {"type": "Legislators", "resolveType": "collectionDocument", "collection": "legislators", "createLegislators": "create", "updateLegislators": "update"}, "LegislatorsConnection": {"type": "LegislatorsConnection", "resolveType": "collectionDocumentList", "collection": "legislators"}, "Roles": {"type": "Roles", "resolveType": "collectionDocument", "collection": "roles", "createRoles": "create", "updateRoles": "update"}, "RolesConnection": {"type": "RolesConnection", "resolveType": "collectionDocumentList", "collection": "roles"}, "Reasons": {"type": "Reasons", "resolveType": "collectionDocument", "collection": "reasons", "createReasons": "create", "updateReasons": "update"}, "ReasonsConnection": {"type": "ReasonsConnection", "resolveType": "collectionDocumentList", "collection": "reasons"}, "Media": {"type": "Media", "resolveType": "collectionDocument", "collection": "media", "createMedia": "create", "updateMedia": "update"}, "MediaConnection": {"type": "MediaConnection", "resolveType": "collectionDocumentList", "collection": "media"}, "Memes": {"type": "<PERSON><PERSON>", "resolveType": "collectionDocument", "collection": "memes", "createMemes": "create", "updateMemes": "update"}, "MemesConnection": {"type": "MemesConnection", "resolveType": "collectionDocumentList", "collection": "memes"}, "Controversies": {"type": "Controversies", "resolveType": "collectionDocument", "collection": "controversies", "createControversies": "create", "updateControversies": "update"}, "ControversiesConnection": {"type": "ControversiesConnection", "resolveType": "collectionDocumentList", "collection": "controversies"}}