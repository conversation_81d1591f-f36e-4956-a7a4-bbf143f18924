//@ts-nocheck
  // DO NOT MODIFY THIS FILE. This file is automatically generated by Tina
  export function gql(strings: TemplateStringsArray, ...args: string[]): string {
    let str = ''
    strings.forEach((string, i) => {
      str += string + (args[i] || '')
    })
    return str
  }
  export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** References another document, used as a foreign key */
  Reference: { input: any; output: any; }
  JSON: { input: any; output: any; }
};

export type SystemInfo = {
  __typename?: 'SystemInfo';
  filename: Scalars['String']['output'];
  title?: Maybe<Scalars['String']['output']>;
  basename: Scalars['String']['output'];
  hasReferences?: Maybe<Scalars['Boolean']['output']>;
  breadcrumbs: Array<Scalars['String']['output']>;
  path: Scalars['String']['output'];
  relativePath: Scalars['String']['output'];
  extension: Scalars['String']['output'];
  template: Scalars['String']['output'];
  collection: Collection;
};


export type SystemInfoBreadcrumbsArgs = {
  excludeExtension?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Folder = {
  __typename?: 'Folder';
  name: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type PageInfo = {
  __typename?: 'PageInfo';
  hasPreviousPage: Scalars['Boolean']['output'];
  hasNextPage: Scalars['Boolean']['output'];
  startCursor: Scalars['String']['output'];
  endCursor: Scalars['String']['output'];
};

export type Node = {
  id: Scalars['ID']['output'];
};

export type Document = {
  id: Scalars['ID']['output'];
  _sys?: Maybe<SystemInfo>;
  _values: Scalars['JSON']['output'];
};

/** A relay-compliant pagination connection */
export type Connection = {
  totalCount: Scalars['Float']['output'];
  pageInfo: PageInfo;
};

export type Query = {
  __typename?: 'Query';
  getOptimizedQuery?: Maybe<Scalars['String']['output']>;
  collection: Collection;
  collections: Array<Collection>;
  node: Node;
  document: DocumentNode;
  legislators: Legislators;
  legislatorsConnection: LegislatorsConnection;
  roles: Roles;
  rolesConnection: RolesConnection;
  reasons: Reasons;
  reasonsConnection: ReasonsConnection;
  media: Media;
  mediaConnection: MediaConnection;
  memes: Memes;
  memesConnection: MemesConnection;
  controversies: Controversies;
  controversiesConnection: ControversiesConnection;
};


export type QueryGetOptimizedQueryArgs = {
  queryString: Scalars['String']['input'];
};


export type QueryCollectionArgs = {
  collection?: InputMaybe<Scalars['String']['input']>;
};


export type QueryNodeArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type QueryDocumentArgs = {
  collection?: InputMaybe<Scalars['String']['input']>;
  relativePath?: InputMaybe<Scalars['String']['input']>;
};


export type QueryLegislatorsArgs = {
  relativePath?: InputMaybe<Scalars['String']['input']>;
};


export type QueryLegislatorsConnectionArgs = {
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<LegislatorsFilter>;
};


export type QueryRolesArgs = {
  relativePath?: InputMaybe<Scalars['String']['input']>;
};


export type QueryRolesConnectionArgs = {
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<RolesFilter>;
};


export type QueryReasonsArgs = {
  relativePath?: InputMaybe<Scalars['String']['input']>;
};


export type QueryReasonsConnectionArgs = {
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<ReasonsFilter>;
};


export type QueryMediaArgs = {
  relativePath?: InputMaybe<Scalars['String']['input']>;
};


export type QueryMediaConnectionArgs = {
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<MediaFilter>;
};


export type QueryMemesArgs = {
  relativePath?: InputMaybe<Scalars['String']['input']>;
};


export type QueryMemesConnectionArgs = {
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<MemesFilter>;
};


export type QueryControversiesArgs = {
  relativePath?: InputMaybe<Scalars['String']['input']>;
};


export type QueryControversiesConnectionArgs = {
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<ControversiesFilter>;
};

export type DocumentFilter = {
  legislators?: InputMaybe<LegislatorsFilter>;
  roles?: InputMaybe<RolesFilter>;
  reasons?: InputMaybe<ReasonsFilter>;
  media?: InputMaybe<MediaFilter>;
  memes?: InputMaybe<MemesFilter>;
  controversies?: InputMaybe<ControversiesFilter>;
};

export type DocumentConnectionEdges = {
  __typename?: 'DocumentConnectionEdges';
  cursor: Scalars['String']['output'];
  node?: Maybe<DocumentNode>;
};

export type DocumentConnection = Connection & {
  __typename?: 'DocumentConnection';
  pageInfo: PageInfo;
  totalCount: Scalars['Float']['output'];
  edges?: Maybe<Array<Maybe<DocumentConnectionEdges>>>;
};

export type Collection = {
  __typename?: 'Collection';
  name: Scalars['String']['output'];
  slug: Scalars['String']['output'];
  label?: Maybe<Scalars['String']['output']>;
  path: Scalars['String']['output'];
  format?: Maybe<Scalars['String']['output']>;
  matches?: Maybe<Scalars['String']['output']>;
  templates?: Maybe<Array<Maybe<Scalars['JSON']['output']>>>;
  fields?: Maybe<Array<Maybe<Scalars['JSON']['output']>>>;
  documents: DocumentConnection;
};


export type CollectionDocumentsArgs = {
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<DocumentFilter>;
  folder?: InputMaybe<Scalars['String']['input']>;
};

export type DocumentNode = Legislators | Roles | Reasons | Media | Memes | Controversies | Folder;

export type Legislators = Node & Document & {
  __typename?: 'Legislators';
  name: Scalars['String']['output'];
  party: Scalars['String']['output'];
  district: Scalars['String']['output'];
  imageUrl?: Maybe<Scalars['String']['output']>;
  nicknames?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  body?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['ID']['output'];
  _sys: SystemInfo;
  _values: Scalars['JSON']['output'];
};

export type StringFilter = {
  startsWith?: InputMaybe<Scalars['String']['input']>;
  eq?: InputMaybe<Scalars['String']['input']>;
  exists?: InputMaybe<Scalars['Boolean']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ImageFilter = {
  startsWith?: InputMaybe<Scalars['String']['input']>;
  eq?: InputMaybe<Scalars['String']['input']>;
  exists?: InputMaybe<Scalars['Boolean']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type RichTextFilter = {
  startsWith?: InputMaybe<Scalars['String']['input']>;
  eq?: InputMaybe<Scalars['String']['input']>;
  exists?: InputMaybe<Scalars['Boolean']['input']>;
};

export type LegislatorsFilter = {
  name?: InputMaybe<StringFilter>;
  party?: InputMaybe<StringFilter>;
  district?: InputMaybe<StringFilter>;
  imageUrl?: InputMaybe<ImageFilter>;
  nicknames?: InputMaybe<StringFilter>;
  body?: InputMaybe<RichTextFilter>;
};

export type LegislatorsConnectionEdges = {
  __typename?: 'LegislatorsConnectionEdges';
  cursor: Scalars['String']['output'];
  node?: Maybe<Legislators>;
};

export type LegislatorsConnection = Connection & {
  __typename?: 'LegislatorsConnection';
  pageInfo: PageInfo;
  totalCount: Scalars['Float']['output'];
  edges?: Maybe<Array<Maybe<LegislatorsConnectionEdges>>>;
};

export type Roles = Node & Document & {
  __typename?: 'Roles';
  title: Scalars['String']['output'];
  icon?: Maybe<Scalars['String']['output']>;
  orderID?: Maybe<Scalars['Float']['output']>;
  description?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['ID']['output'];
  _sys: SystemInfo;
  _values: Scalars['JSON']['output'];
};

export type NumberFilter = {
  lt?: InputMaybe<Scalars['Float']['input']>;
  lte?: InputMaybe<Scalars['Float']['input']>;
  gte?: InputMaybe<Scalars['Float']['input']>;
  gt?: InputMaybe<Scalars['Float']['input']>;
  eq?: InputMaybe<Scalars['Float']['input']>;
  exists?: InputMaybe<Scalars['Boolean']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Float']['input']>>>;
};

export type RolesFilter = {
  title?: InputMaybe<StringFilter>;
  icon?: InputMaybe<ImageFilter>;
  orderID?: InputMaybe<NumberFilter>;
  description?: InputMaybe<RichTextFilter>;
};

export type RolesConnectionEdges = {
  __typename?: 'RolesConnectionEdges';
  cursor: Scalars['String']['output'];
  node?: Maybe<Roles>;
};

export type RolesConnection = Connection & {
  __typename?: 'RolesConnection';
  pageInfo: PageInfo;
  totalCount: Scalars['Float']['output'];
  edges?: Maybe<Array<Maybe<RolesConnectionEdges>>>;
};

export type ReasonsMedia = {
  __typename?: 'ReasonsMedia';
  type: Scalars['String']['output'];
  url?: Maybe<Scalars['String']['output']>;
  youtubeId?: Maybe<Scalars['String']['output']>;
  caption?: Maybe<Scalars['String']['output']>;
};

export type Reasons = Node & Document & {
  __typename?: 'Reasons';
  title: Scalars['String']['output'];
  body?: Maybe<Scalars['JSON']['output']>;
  popularity?: Maybe<Scalars['Float']['output']>;
  orderID?: Maybe<Scalars['Float']['output']>;
  publishDate?: Maybe<Scalars['String']['output']>;
  relatedLegislators?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  relatedRoles?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  media?: Maybe<Array<Maybe<ReasonsMedia>>>;
  tags?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  id: Scalars['ID']['output'];
  _sys: SystemInfo;
  _values: Scalars['JSON']['output'];
};

export type DatetimeFilter = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  eq?: InputMaybe<Scalars['String']['input']>;
  exists?: InputMaybe<Scalars['Boolean']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ReasonsMediaFilter = {
  type?: InputMaybe<StringFilter>;
  url?: InputMaybe<ImageFilter>;
  youtubeId?: InputMaybe<StringFilter>;
  caption?: InputMaybe<StringFilter>;
};

export type ReasonsFilter = {
  title?: InputMaybe<StringFilter>;
  body?: InputMaybe<RichTextFilter>;
  popularity?: InputMaybe<NumberFilter>;
  orderID?: InputMaybe<NumberFilter>;
  publishDate?: InputMaybe<DatetimeFilter>;
  relatedLegislators?: InputMaybe<StringFilter>;
  relatedRoles?: InputMaybe<StringFilter>;
  media?: InputMaybe<ReasonsMediaFilter>;
  tags?: InputMaybe<StringFilter>;
};

export type ReasonsConnectionEdges = {
  __typename?: 'ReasonsConnectionEdges';
  cursor: Scalars['String']['output'];
  node?: Maybe<Reasons>;
};

export type ReasonsConnection = Connection & {
  __typename?: 'ReasonsConnection';
  pageInfo: PageInfo;
  totalCount: Scalars['Float']['output'];
  edges?: Maybe<Array<Maybe<ReasonsConnectionEdges>>>;
};

export type Media = Node & Document & {
  __typename?: 'Media';
  title: Scalars['String']['output'];
  description?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['String']['output']>;
  orderID?: Maybe<Scalars['Float']['output']>;
  youtubeId?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  groups?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  body?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['ID']['output'];
  _sys: SystemInfo;
  _values: Scalars['JSON']['output'];
};

export type MediaFilter = {
  title?: InputMaybe<StringFilter>;
  description?: InputMaybe<StringFilter>;
  date?: InputMaybe<DatetimeFilter>;
  orderID?: InputMaybe<NumberFilter>;
  youtubeId?: InputMaybe<StringFilter>;
  image?: InputMaybe<ImageFilter>;
  source?: InputMaybe<StringFilter>;
  tags?: InputMaybe<StringFilter>;
  groups?: InputMaybe<StringFilter>;
  body?: InputMaybe<RichTextFilter>;
};

export type MediaConnectionEdges = {
  __typename?: 'MediaConnectionEdges';
  cursor: Scalars['String']['output'];
  node?: Maybe<Media>;
};

export type MediaConnection = Connection & {
  __typename?: 'MediaConnection';
  pageInfo: PageInfo;
  totalCount: Scalars['Float']['output'];
  edges?: Maybe<Array<Maybe<MediaConnectionEdges>>>;
};

export type Memes = Node & Document & {
  __typename?: 'Memes';
  title: Scalars['String']['output'];
  description?: Maybe<Scalars['String']['output']>;
  imageUrl: Scalars['String']['output'];
  tags?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  legislatorNames?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  body?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['ID']['output'];
  _sys: SystemInfo;
  _values: Scalars['JSON']['output'];
};

export type MemesFilter = {
  title?: InputMaybe<StringFilter>;
  description?: InputMaybe<StringFilter>;
  imageUrl?: InputMaybe<ImageFilter>;
  tags?: InputMaybe<StringFilter>;
  legislatorNames?: InputMaybe<StringFilter>;
  body?: InputMaybe<RichTextFilter>;
};

export type MemesConnectionEdges = {
  __typename?: 'MemesConnectionEdges';
  cursor: Scalars['String']['output'];
  node?: Maybe<Memes>;
};

export type MemesConnection = Connection & {
  __typename?: 'MemesConnection';
  pageInfo: PageInfo;
  totalCount: Scalars['Float']['output'];
  edges?: Maybe<Array<Maybe<MemesConnectionEdges>>>;
};

export type Controversies = Node & Document & {
  __typename?: 'Controversies';
  title: Scalars['String']['output'];
  content: Scalars['String']['output'];
  reflections?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  orderID?: Maybe<Scalars['Float']['output']>;
  legislatorName: Scalars['String']['output'];
  tags?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  body?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['ID']['output'];
  _sys: SystemInfo;
  _values: Scalars['JSON']['output'];
};

export type ControversiesFilter = {
  title?: InputMaybe<StringFilter>;
  content?: InputMaybe<StringFilter>;
  reflections?: InputMaybe<StringFilter>;
  orderID?: InputMaybe<NumberFilter>;
  legislatorName?: InputMaybe<StringFilter>;
  tags?: InputMaybe<StringFilter>;
  body?: InputMaybe<RichTextFilter>;
};

export type ControversiesConnectionEdges = {
  __typename?: 'ControversiesConnectionEdges';
  cursor: Scalars['String']['output'];
  node?: Maybe<Controversies>;
};

export type ControversiesConnection = Connection & {
  __typename?: 'ControversiesConnection';
  pageInfo: PageInfo;
  totalCount: Scalars['Float']['output'];
  edges?: Maybe<Array<Maybe<ControversiesConnectionEdges>>>;
};

export type Mutation = {
  __typename?: 'Mutation';
  addPendingDocument: DocumentNode;
  updateDocument: DocumentNode;
  deleteDocument: DocumentNode;
  createDocument: DocumentNode;
  createFolder: DocumentNode;
  updateLegislators: Legislators;
  createLegislators: Legislators;
  updateRoles: Roles;
  createRoles: Roles;
  updateReasons: Reasons;
  createReasons: Reasons;
  updateMedia: Media;
  createMedia: Media;
  updateMemes: Memes;
  createMemes: Memes;
  updateControversies: Controversies;
  createControversies: Controversies;
};


export type MutationAddPendingDocumentArgs = {
  collection: Scalars['String']['input'];
  relativePath: Scalars['String']['input'];
  template?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateDocumentArgs = {
  collection?: InputMaybe<Scalars['String']['input']>;
  relativePath: Scalars['String']['input'];
  params: DocumentUpdateMutation;
};


export type MutationDeleteDocumentArgs = {
  collection?: InputMaybe<Scalars['String']['input']>;
  relativePath: Scalars['String']['input'];
};


export type MutationCreateDocumentArgs = {
  collection?: InputMaybe<Scalars['String']['input']>;
  relativePath: Scalars['String']['input'];
  params: DocumentMutation;
};


export type MutationCreateFolderArgs = {
  collection?: InputMaybe<Scalars['String']['input']>;
  relativePath: Scalars['String']['input'];
};


export type MutationUpdateLegislatorsArgs = {
  relativePath: Scalars['String']['input'];
  params: LegislatorsMutation;
};


export type MutationCreateLegislatorsArgs = {
  relativePath: Scalars['String']['input'];
  params: LegislatorsMutation;
};


export type MutationUpdateRolesArgs = {
  relativePath: Scalars['String']['input'];
  params: RolesMutation;
};


export type MutationCreateRolesArgs = {
  relativePath: Scalars['String']['input'];
  params: RolesMutation;
};


export type MutationUpdateReasonsArgs = {
  relativePath: Scalars['String']['input'];
  params: ReasonsMutation;
};


export type MutationCreateReasonsArgs = {
  relativePath: Scalars['String']['input'];
  params: ReasonsMutation;
};


export type MutationUpdateMediaArgs = {
  relativePath: Scalars['String']['input'];
  params: MediaMutation;
};


export type MutationCreateMediaArgs = {
  relativePath: Scalars['String']['input'];
  params: MediaMutation;
};


export type MutationUpdateMemesArgs = {
  relativePath: Scalars['String']['input'];
  params: MemesMutation;
};


export type MutationCreateMemesArgs = {
  relativePath: Scalars['String']['input'];
  params: MemesMutation;
};


export type MutationUpdateControversiesArgs = {
  relativePath: Scalars['String']['input'];
  params: ControversiesMutation;
};


export type MutationCreateControversiesArgs = {
  relativePath: Scalars['String']['input'];
  params: ControversiesMutation;
};

export type DocumentUpdateMutation = {
  legislators?: InputMaybe<LegislatorsMutation>;
  roles?: InputMaybe<RolesMutation>;
  reasons?: InputMaybe<ReasonsMutation>;
  media?: InputMaybe<MediaMutation>;
  memes?: InputMaybe<MemesMutation>;
  controversies?: InputMaybe<ControversiesMutation>;
  relativePath?: InputMaybe<Scalars['String']['input']>;
};

export type DocumentMutation = {
  legislators?: InputMaybe<LegislatorsMutation>;
  roles?: InputMaybe<RolesMutation>;
  reasons?: InputMaybe<ReasonsMutation>;
  media?: InputMaybe<MediaMutation>;
  memes?: InputMaybe<MemesMutation>;
  controversies?: InputMaybe<ControversiesMutation>;
};

export type LegislatorsMutation = {
  name?: InputMaybe<Scalars['String']['input']>;
  party?: InputMaybe<Scalars['String']['input']>;
  district?: InputMaybe<Scalars['String']['input']>;
  imageUrl?: InputMaybe<Scalars['String']['input']>;
  nicknames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  body?: InputMaybe<Scalars['JSON']['input']>;
};

export type RolesMutation = {
  title?: InputMaybe<Scalars['String']['input']>;
  icon?: InputMaybe<Scalars['String']['input']>;
  orderID?: InputMaybe<Scalars['Float']['input']>;
  description?: InputMaybe<Scalars['JSON']['input']>;
};

export type ReasonsMediaMutation = {
  type?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  youtubeId?: InputMaybe<Scalars['String']['input']>;
  caption?: InputMaybe<Scalars['String']['input']>;
};

export type ReasonsMutation = {
  title?: InputMaybe<Scalars['String']['input']>;
  body?: InputMaybe<Scalars['JSON']['input']>;
  popularity?: InputMaybe<Scalars['Float']['input']>;
  orderID?: InputMaybe<Scalars['Float']['input']>;
  publishDate?: InputMaybe<Scalars['String']['input']>;
  relatedLegislators?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  relatedRoles?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  media?: InputMaybe<Array<InputMaybe<ReasonsMediaMutation>>>;
  tags?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MediaMutation = {
  title?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['String']['input']>;
  orderID?: InputMaybe<Scalars['Float']['input']>;
  youtubeId?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  tags?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  groups?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  body?: InputMaybe<Scalars['JSON']['input']>;
};

export type MemesMutation = {
  title?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  imageUrl?: InputMaybe<Scalars['String']['input']>;
  tags?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  legislatorNames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  body?: InputMaybe<Scalars['JSON']['input']>;
};

export type ControversiesMutation = {
  title?: InputMaybe<Scalars['String']['input']>;
  content?: InputMaybe<Scalars['String']['input']>;
  reflections?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  orderID?: InputMaybe<Scalars['Float']['input']>;
  legislatorName?: InputMaybe<Scalars['String']['input']>;
  tags?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  body?: InputMaybe<Scalars['JSON']['input']>;
};

export type LegislatorsPartsFragment = { __typename: 'Legislators', name: string, party: string, district: string, imageUrl?: string | null, nicknames?: Array<string | null> | null, body?: any | null };

export type RolesPartsFragment = { __typename: 'Roles', title: string, icon?: string | null, orderID?: number | null, description?: any | null };

export type ReasonsPartsFragment = { __typename: 'Reasons', title: string, body?: any | null, popularity?: number | null, orderID?: number | null, publishDate?: string | null, relatedLegislators?: Array<string | null> | null, relatedRoles?: Array<string | null> | null, tags?: Array<string | null> | null, media?: Array<{ __typename: 'ReasonsMedia', type: string, url?: string | null, youtubeId?: string | null, caption?: string | null } | null> | null };

export type MediaPartsFragment = { __typename: 'Media', title: string, description?: string | null, date?: string | null, orderID?: number | null, youtubeId?: string | null, image?: string | null, source?: string | null, tags?: Array<string | null> | null, groups?: Array<string | null> | null, body?: any | null };

export type MemesPartsFragment = { __typename: 'Memes', title: string, description?: string | null, imageUrl: string, tags?: Array<string | null> | null, legislatorNames?: Array<string | null> | null, body?: any | null };

export type ControversiesPartsFragment = { __typename: 'Controversies', title: string, content: string, reflections?: Array<string | null> | null, orderID?: number | null, legislatorName: string, tags?: Array<string | null> | null, body?: any | null };

export type LegislatorsQueryVariables = Exact<{
  relativePath: Scalars['String']['input'];
}>;


export type LegislatorsQuery = { __typename?: 'Query', legislators: { __typename: 'Legislators', id: string, name: string, party: string, district: string, imageUrl?: string | null, nicknames?: Array<string | null> | null, body?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } };

export type LegislatorsConnectionQueryVariables = Exact<{
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<LegislatorsFilter>;
}>;


export type LegislatorsConnectionQuery = { __typename?: 'Query', legislatorsConnection: { __typename?: 'LegislatorsConnection', totalCount: number, pageInfo: { __typename?: 'PageInfo', hasPreviousPage: boolean, hasNextPage: boolean, startCursor: string, endCursor: string }, edges?: Array<{ __typename?: 'LegislatorsConnectionEdges', cursor: string, node?: { __typename: 'Legislators', id: string, name: string, party: string, district: string, imageUrl?: string | null, nicknames?: Array<string | null> | null, body?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } | null } | null> | null } };

export type RolesQueryVariables = Exact<{
  relativePath: Scalars['String']['input'];
}>;


export type RolesQuery = { __typename?: 'Query', roles: { __typename: 'Roles', id: string, title: string, icon?: string | null, orderID?: number | null, description?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } };

export type RolesConnectionQueryVariables = Exact<{
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<RolesFilter>;
}>;


export type RolesConnectionQuery = { __typename?: 'Query', rolesConnection: { __typename?: 'RolesConnection', totalCount: number, pageInfo: { __typename?: 'PageInfo', hasPreviousPage: boolean, hasNextPage: boolean, startCursor: string, endCursor: string }, edges?: Array<{ __typename?: 'RolesConnectionEdges', cursor: string, node?: { __typename: 'Roles', id: string, title: string, icon?: string | null, orderID?: number | null, description?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } | null } | null> | null } };

export type ReasonsQueryVariables = Exact<{
  relativePath: Scalars['String']['input'];
}>;


export type ReasonsQuery = { __typename?: 'Query', reasons: { __typename: 'Reasons', id: string, title: string, body?: any | null, popularity?: number | null, orderID?: number | null, publishDate?: string | null, relatedLegislators?: Array<string | null> | null, relatedRoles?: Array<string | null> | null, tags?: Array<string | null> | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string }, media?: Array<{ __typename: 'ReasonsMedia', type: string, url?: string | null, youtubeId?: string | null, caption?: string | null } | null> | null } };

export type ReasonsConnectionQueryVariables = Exact<{
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<ReasonsFilter>;
}>;


export type ReasonsConnectionQuery = { __typename?: 'Query', reasonsConnection: { __typename?: 'ReasonsConnection', totalCount: number, pageInfo: { __typename?: 'PageInfo', hasPreviousPage: boolean, hasNextPage: boolean, startCursor: string, endCursor: string }, edges?: Array<{ __typename?: 'ReasonsConnectionEdges', cursor: string, node?: { __typename: 'Reasons', id: string, title: string, body?: any | null, popularity?: number | null, orderID?: number | null, publishDate?: string | null, relatedLegislators?: Array<string | null> | null, relatedRoles?: Array<string | null> | null, tags?: Array<string | null> | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string }, media?: Array<{ __typename: 'ReasonsMedia', type: string, url?: string | null, youtubeId?: string | null, caption?: string | null } | null> | null } | null } | null> | null } };

export type MediaQueryVariables = Exact<{
  relativePath: Scalars['String']['input'];
}>;


export type MediaQuery = { __typename?: 'Query', media: { __typename: 'Media', id: string, title: string, description?: string | null, date?: string | null, orderID?: number | null, youtubeId?: string | null, image?: string | null, source?: string | null, tags?: Array<string | null> | null, groups?: Array<string | null> | null, body?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } };

export type MediaConnectionQueryVariables = Exact<{
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<MediaFilter>;
}>;


export type MediaConnectionQuery = { __typename?: 'Query', mediaConnection: { __typename?: 'MediaConnection', totalCount: number, pageInfo: { __typename?: 'PageInfo', hasPreviousPage: boolean, hasNextPage: boolean, startCursor: string, endCursor: string }, edges?: Array<{ __typename?: 'MediaConnectionEdges', cursor: string, node?: { __typename: 'Media', id: string, title: string, description?: string | null, date?: string | null, orderID?: number | null, youtubeId?: string | null, image?: string | null, source?: string | null, tags?: Array<string | null> | null, groups?: Array<string | null> | null, body?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } | null } | null> | null } };

export type MemesQueryVariables = Exact<{
  relativePath: Scalars['String']['input'];
}>;


export type MemesQuery = { __typename?: 'Query', memes: { __typename: 'Memes', id: string, title: string, description?: string | null, imageUrl: string, tags?: Array<string | null> | null, legislatorNames?: Array<string | null> | null, body?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } };

export type MemesConnectionQueryVariables = Exact<{
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<MemesFilter>;
}>;


export type MemesConnectionQuery = { __typename?: 'Query', memesConnection: { __typename?: 'MemesConnection', totalCount: number, pageInfo: { __typename?: 'PageInfo', hasPreviousPage: boolean, hasNextPage: boolean, startCursor: string, endCursor: string }, edges?: Array<{ __typename?: 'MemesConnectionEdges', cursor: string, node?: { __typename: 'Memes', id: string, title: string, description?: string | null, imageUrl: string, tags?: Array<string | null> | null, legislatorNames?: Array<string | null> | null, body?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } | null } | null> | null } };

export type ControversiesQueryVariables = Exact<{
  relativePath: Scalars['String']['input'];
}>;


export type ControversiesQuery = { __typename?: 'Query', controversies: { __typename: 'Controversies', id: string, title: string, content: string, reflections?: Array<string | null> | null, orderID?: number | null, legislatorName: string, tags?: Array<string | null> | null, body?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } };

export type ControversiesConnectionQueryVariables = Exact<{
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Float']['input']>;
  last?: InputMaybe<Scalars['Float']['input']>;
  sort?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<ControversiesFilter>;
}>;


export type ControversiesConnectionQuery = { __typename?: 'Query', controversiesConnection: { __typename?: 'ControversiesConnection', totalCount: number, pageInfo: { __typename?: 'PageInfo', hasPreviousPage: boolean, hasNextPage: boolean, startCursor: string, endCursor: string }, edges?: Array<{ __typename?: 'ControversiesConnectionEdges', cursor: string, node?: { __typename: 'Controversies', id: string, title: string, content: string, reflections?: Array<string | null> | null, orderID?: number | null, legislatorName: string, tags?: Array<string | null> | null, body?: any | null, _sys: { __typename?: 'SystemInfo', filename: string, basename: string, hasReferences?: boolean | null, breadcrumbs: Array<string>, path: string, relativePath: string, extension: string } } | null } | null> | null } };

export const LegislatorsPartsFragmentDoc = gql`
    fragment LegislatorsParts on Legislators {
  __typename
  name
  party
  district
  imageUrl
  nicknames
  body
}
    `;
export const RolesPartsFragmentDoc = gql`
    fragment RolesParts on Roles {
  __typename
  title
  icon
  orderID
  description
}
    `;
export const ReasonsPartsFragmentDoc = gql`
    fragment ReasonsParts on Reasons {
  __typename
  title
  body
  popularity
  orderID
  publishDate
  relatedLegislators
  relatedRoles
  media {
    __typename
    type
    url
    youtubeId
    caption
  }
  tags
}
    `;
export const MediaPartsFragmentDoc = gql`
    fragment MediaParts on Media {
  __typename
  title
  description
  date
  orderID
  youtubeId
  image
  source
  tags
  groups
  body
}
    `;
export const MemesPartsFragmentDoc = gql`
    fragment MemesParts on Memes {
  __typename
  title
  description
  imageUrl
  tags
  legislatorNames
  body
}
    `;
export const ControversiesPartsFragmentDoc = gql`
    fragment ControversiesParts on Controversies {
  __typename
  title
  content
  reflections
  orderID
  legislatorName
  tags
  body
}
    `;
export const LegislatorsDocument = gql`
    query legislators($relativePath: String!) {
  legislators(relativePath: $relativePath) {
    ... on Document {
      _sys {
        filename
        basename
        hasReferences
        breadcrumbs
        path
        relativePath
        extension
      }
      id
    }
    ...LegislatorsParts
  }
}
    ${LegislatorsPartsFragmentDoc}`;
export const LegislatorsConnectionDocument = gql`
    query legislatorsConnection($before: String, $after: String, $first: Float, $last: Float, $sort: String, $filter: LegislatorsFilter) {
  legislatorsConnection(
    before: $before
    after: $after
    first: $first
    last: $last
    sort: $sort
    filter: $filter
  ) {
    pageInfo {
      hasPreviousPage
      hasNextPage
      startCursor
      endCursor
    }
    totalCount
    edges {
      cursor
      node {
        ... on Document {
          _sys {
            filename
            basename
            hasReferences
            breadcrumbs
            path
            relativePath
            extension
          }
          id
        }
        ...LegislatorsParts
      }
    }
  }
}
    ${LegislatorsPartsFragmentDoc}`;
export const RolesDocument = gql`
    query roles($relativePath: String!) {
  roles(relativePath: $relativePath) {
    ... on Document {
      _sys {
        filename
        basename
        hasReferences
        breadcrumbs
        path
        relativePath
        extension
      }
      id
    }
    ...RolesParts
  }
}
    ${RolesPartsFragmentDoc}`;
export const RolesConnectionDocument = gql`
    query rolesConnection($before: String, $after: String, $first: Float, $last: Float, $sort: String, $filter: RolesFilter) {
  rolesConnection(
    before: $before
    after: $after
    first: $first
    last: $last
    sort: $sort
    filter: $filter
  ) {
    pageInfo {
      hasPreviousPage
      hasNextPage
      startCursor
      endCursor
    }
    totalCount
    edges {
      cursor
      node {
        ... on Document {
          _sys {
            filename
            basename
            hasReferences
            breadcrumbs
            path
            relativePath
            extension
          }
          id
        }
        ...RolesParts
      }
    }
  }
}
    ${RolesPartsFragmentDoc}`;
export const ReasonsDocument = gql`
    query reasons($relativePath: String!) {
  reasons(relativePath: $relativePath) {
    ... on Document {
      _sys {
        filename
        basename
        hasReferences
        breadcrumbs
        path
        relativePath
        extension
      }
      id
    }
    ...ReasonsParts
  }
}
    ${ReasonsPartsFragmentDoc}`;
export const ReasonsConnectionDocument = gql`
    query reasonsConnection($before: String, $after: String, $first: Float, $last: Float, $sort: String, $filter: ReasonsFilter) {
  reasonsConnection(
    before: $before
    after: $after
    first: $first
    last: $last
    sort: $sort
    filter: $filter
  ) {
    pageInfo {
      hasPreviousPage
      hasNextPage
      startCursor
      endCursor
    }
    totalCount
    edges {
      cursor
      node {
        ... on Document {
          _sys {
            filename
            basename
            hasReferences
            breadcrumbs
            path
            relativePath
            extension
          }
          id
        }
        ...ReasonsParts
      }
    }
  }
}
    ${ReasonsPartsFragmentDoc}`;
export const MediaDocument = gql`
    query media($relativePath: String!) {
  media(relativePath: $relativePath) {
    ... on Document {
      _sys {
        filename
        basename
        hasReferences
        breadcrumbs
        path
        relativePath
        extension
      }
      id
    }
    ...MediaParts
  }
}
    ${MediaPartsFragmentDoc}`;
export const MediaConnectionDocument = gql`
    query mediaConnection($before: String, $after: String, $first: Float, $last: Float, $sort: String, $filter: MediaFilter) {
  mediaConnection(
    before: $before
    after: $after
    first: $first
    last: $last
    sort: $sort
    filter: $filter
  ) {
    pageInfo {
      hasPreviousPage
      hasNextPage
      startCursor
      endCursor
    }
    totalCount
    edges {
      cursor
      node {
        ... on Document {
          _sys {
            filename
            basename
            hasReferences
            breadcrumbs
            path
            relativePath
            extension
          }
          id
        }
        ...MediaParts
      }
    }
  }
}
    ${MediaPartsFragmentDoc}`;
export const MemesDocument = gql`
    query memes($relativePath: String!) {
  memes(relativePath: $relativePath) {
    ... on Document {
      _sys {
        filename
        basename
        hasReferences
        breadcrumbs
        path
        relativePath
        extension
      }
      id
    }
    ...MemesParts
  }
}
    ${MemesPartsFragmentDoc}`;
export const MemesConnectionDocument = gql`
    query memesConnection($before: String, $after: String, $first: Float, $last: Float, $sort: String, $filter: MemesFilter) {
  memesConnection(
    before: $before
    after: $after
    first: $first
    last: $last
    sort: $sort
    filter: $filter
  ) {
    pageInfo {
      hasPreviousPage
      hasNextPage
      startCursor
      endCursor
    }
    totalCount
    edges {
      cursor
      node {
        ... on Document {
          _sys {
            filename
            basename
            hasReferences
            breadcrumbs
            path
            relativePath
            extension
          }
          id
        }
        ...MemesParts
      }
    }
  }
}
    ${MemesPartsFragmentDoc}`;
export const ControversiesDocument = gql`
    query controversies($relativePath: String!) {
  controversies(relativePath: $relativePath) {
    ... on Document {
      _sys {
        filename
        basename
        hasReferences
        breadcrumbs
        path
        relativePath
        extension
      }
      id
    }
    ...ControversiesParts
  }
}
    ${ControversiesPartsFragmentDoc}`;
export const ControversiesConnectionDocument = gql`
    query controversiesConnection($before: String, $after: String, $first: Float, $last: Float, $sort: String, $filter: ControversiesFilter) {
  controversiesConnection(
    before: $before
    after: $after
    first: $first
    last: $last
    sort: $sort
    filter: $filter
  ) {
    pageInfo {
      hasPreviousPage
      hasNextPage
      startCursor
      endCursor
    }
    totalCount
    edges {
      cursor
      node {
        ... on Document {
          _sys {
            filename
            basename
            hasReferences
            breadcrumbs
            path
            relativePath
            extension
          }
          id
        }
        ...ControversiesParts
      }
    }
  }
}
    ${ControversiesPartsFragmentDoc}`;
export type Requester<C= {}> = <R, V>(doc: DocumentNode, vars?: V, options?: C) => Promise<R>
  export function getSdk<C>(requester: Requester<C>) {
    return {
      legislators(variables: LegislatorsQueryVariables, options?: C): Promise<{data: LegislatorsQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: LegislatorsQueryVariables, query: string}> {
        return requester<{data: LegislatorsQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: LegislatorsQueryVariables, query: string}, LegislatorsQueryVariables>(LegislatorsDocument, variables, options);
      },
    legislatorsConnection(variables?: LegislatorsConnectionQueryVariables, options?: C): Promise<{data: LegislatorsConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: LegislatorsConnectionQueryVariables, query: string}> {
        return requester<{data: LegislatorsConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: LegislatorsConnectionQueryVariables, query: string}, LegislatorsConnectionQueryVariables>(LegislatorsConnectionDocument, variables, options);
      },
    roles(variables: RolesQueryVariables, options?: C): Promise<{data: RolesQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: RolesQueryVariables, query: string}> {
        return requester<{data: RolesQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: RolesQueryVariables, query: string}, RolesQueryVariables>(RolesDocument, variables, options);
      },
    rolesConnection(variables?: RolesConnectionQueryVariables, options?: C): Promise<{data: RolesConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: RolesConnectionQueryVariables, query: string}> {
        return requester<{data: RolesConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: RolesConnectionQueryVariables, query: string}, RolesConnectionQueryVariables>(RolesConnectionDocument, variables, options);
      },
    reasons(variables: ReasonsQueryVariables, options?: C): Promise<{data: ReasonsQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: ReasonsQueryVariables, query: string}> {
        return requester<{data: ReasonsQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: ReasonsQueryVariables, query: string}, ReasonsQueryVariables>(ReasonsDocument, variables, options);
      },
    reasonsConnection(variables?: ReasonsConnectionQueryVariables, options?: C): Promise<{data: ReasonsConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: ReasonsConnectionQueryVariables, query: string}> {
        return requester<{data: ReasonsConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: ReasonsConnectionQueryVariables, query: string}, ReasonsConnectionQueryVariables>(ReasonsConnectionDocument, variables, options);
      },
    media(variables: MediaQueryVariables, options?: C): Promise<{data: MediaQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: MediaQueryVariables, query: string}> {
        return requester<{data: MediaQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: MediaQueryVariables, query: string}, MediaQueryVariables>(MediaDocument, variables, options);
      },
    mediaConnection(variables?: MediaConnectionQueryVariables, options?: C): Promise<{data: MediaConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: MediaConnectionQueryVariables, query: string}> {
        return requester<{data: MediaConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: MediaConnectionQueryVariables, query: string}, MediaConnectionQueryVariables>(MediaConnectionDocument, variables, options);
      },
    memes(variables: MemesQueryVariables, options?: C): Promise<{data: MemesQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: MemesQueryVariables, query: string}> {
        return requester<{data: MemesQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: MemesQueryVariables, query: string}, MemesQueryVariables>(MemesDocument, variables, options);
      },
    memesConnection(variables?: MemesConnectionQueryVariables, options?: C): Promise<{data: MemesConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: MemesConnectionQueryVariables, query: string}> {
        return requester<{data: MemesConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: MemesConnectionQueryVariables, query: string}, MemesConnectionQueryVariables>(MemesConnectionDocument, variables, options);
      },
    controversies(variables: ControversiesQueryVariables, options?: C): Promise<{data: ControversiesQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: ControversiesQueryVariables, query: string}> {
        return requester<{data: ControversiesQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: ControversiesQueryVariables, query: string}, ControversiesQueryVariables>(ControversiesDocument, variables, options);
      },
    controversiesConnection(variables?: ControversiesConnectionQueryVariables, options?: C): Promise<{data: ControversiesConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: ControversiesConnectionQueryVariables, query: string}> {
        return requester<{data: ControversiesConnectionQuery, errors?: { message: string, locations: { line: number, column: number }[], path: string[] }[], variables: ControversiesConnectionQueryVariables, query: string}, ControversiesConnectionQueryVariables>(ControversiesConnectionDocument, variables, options);
      }
    };
  }
  export type Sdk = ReturnType<typeof getSdk>;

// TinaSDK generated code
import { createClient, TinaClient } from "tinacms/dist/client";

const generateRequester = (
  client: TinaClient,
) => {
  const requester: (
    doc: any,
    vars?: any,
    options?: {
      branch?: string,
      /**
       * Aside from `method` and `body`, all fetch options are passed
       * through to underlying fetch request
       */
      fetchOptions?: Omit<Parameters<typeof fetch>[1], 'body' | 'method'>,
    },
    client
  ) => Promise<any> = async (doc, vars, options) => {
    let url = client.apiUrl
    if (options?.branch) {
      const index = client.apiUrl.lastIndexOf('/')
      url = client.apiUrl.substring(0, index + 1) + options.branch
    }
    const data = await client.request({
      query: doc,
      variables: vars,
      url,
    }, options)

    return { data: data?.data, errors: data?.errors, query: doc, variables: vars || {} }
  }

  return requester
}

/**
 * @experimental this class can be used but may change in the future
 **/
export const ExperimentalGetTinaClient = () =>
  getSdk(
    generateRequester(
      createClient({
        url: "http://localhost:4001/graphql",
        queries,
      })
    )
  )

export const queries = (
  client: TinaClient,
) => {
  const requester = generateRequester(client)
  return getSdk(requester)
}

  