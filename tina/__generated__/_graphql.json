{"kind": "Document", "definitions": [{"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "Reference"}, "description": {"kind": "StringValue", "value": "References another document, used as a foreign key"}, "directives": []}, {"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "JSON"}, "description": {"kind": "StringValue", "value": ""}, "directives": []}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SystemInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "filename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "basename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasReferences"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "breadcrumbs"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "excludeExtension"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "relativePath"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "extension"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "template"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Folder"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasPreviousPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasNextPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "startCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "endCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Node"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Document"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": "A relay-compliant pagination connection"}, "name": {"kind": "Name", "value": "Connection"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Query"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "getOptimizedQuery"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "queryString"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collections"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "id"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "legislators"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Legislators"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "legislatorsConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Legislators<PERSON><PERSON><PERSON>"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "LegislatorsConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "roles"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Roles"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "rolesConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RolesFilter"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RolesConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "reasons"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Reasons"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "reasonsConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsFilter"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "media"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Media"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "mediaConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MediaFilter"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MediaConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "memes"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON>"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "memesConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MemesConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "controversies"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Controversies"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "controversiesConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ControversiesFilter"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ControversiesConnection"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "legislators"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Legislators<PERSON><PERSON><PERSON>"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "roles"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RolesFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "reasons"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "media"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MediaFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "memes"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "controversies"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ControversiesFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "DocumentConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "DocumentConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Collection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "slug"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "format"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "matches"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "templates"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "fields"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "documents"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "folder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnection"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "DocumentNode"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Legislators"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Roles"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Reasons"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Media"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON>"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Controversies"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Folder"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Legislators"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "party"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "district"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "imageUrl"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "nicknames"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "body"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "StringFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ImageFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "RichTextFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Legislators<PERSON><PERSON><PERSON>"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "name"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "party"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "district"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "imageUrl"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "nicknames"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "LegislatorsConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Legislators"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "LegislatorsConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "LegislatorsConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Roles"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "icon"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "orderID"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "NumberFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "lt"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "lte"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "gte"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "gt"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "RolesFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "icon"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "orderID"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "NumberFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "RolesConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Roles"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "RolesConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RolesConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "ReasonsMedia"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "type"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "url"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "youtubeId"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "caption"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Reasons"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "body"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "popularity"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "orderID"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "publishDate"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "relatedLegislators"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "relatedRoles"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "media"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsMedia"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "tags"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DatetimeFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ReasonsMediaFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "type"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "url"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "youtubeId"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "caption"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ReasonsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "popularity"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "NumberFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "orderID"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "NumberFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "publishDate"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DatetimeFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relatedLegislators"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relatedRoles"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "media"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsMediaFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "ReasonsConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Reasons"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "ReasonsConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Media"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "date"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "orderID"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "youtubeId"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "image"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "source"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "tags"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "groups"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "body"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "MediaFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "date"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DatetimeFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "orderID"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "NumberFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "youtubeId"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "image"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "source"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "groups"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "MediaConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Media"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "MediaConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MediaConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "<PERSON><PERSON>"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "imageUrl"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "tags"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "legislator<PERSON><PERSON><PERSON>"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "body"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "imageUrl"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "legislator<PERSON><PERSON><PERSON>"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "MemesConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON>"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "MemesConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MemesConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Controversies"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "content"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "reflections"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "orderID"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "legislator<PERSON><PERSON>"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "tags"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "body"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ControversiesFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "content"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "reflections"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "orderID"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "NumberFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "legislator<PERSON><PERSON>"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "ControversiesConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Controversies"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "ControversiesConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ControversiesConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Mutation"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "addPendingDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "template"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "deleteDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createFolder"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateLegislators"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "LegislatorsMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Legislators"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createLegislators"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "LegislatorsMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Legislators"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateRoles"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RolesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Roles"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createRoles"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RolesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Roles"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateReasons"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Reasons"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createReasons"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Reasons"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateMedia"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MediaMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Media"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createMedia"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MediaMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Media"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateMemes"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MemesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON>"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createMemes"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MemesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON>"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateControversies"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ControversiesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Controversies"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createControversies"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ControversiesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Controversies"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "legislators"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "LegislatorsMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "roles"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RolesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "reasons"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "media"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MediaMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "memes"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MemesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "controversies"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ControversiesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "legislators"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "LegislatorsMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "roles"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RolesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "reasons"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "media"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MediaMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "memes"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MemesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "controversies"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ControversiesMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "LegislatorsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "name"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "party"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "district"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "imageUrl"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "nicknames"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "RolesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "icon"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "orderID"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ReasonsMediaMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "type"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "url"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "youtubeId"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "caption"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ReasonsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "popularity"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "orderID"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "publishDate"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relatedLegislators"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relatedRoles"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "media"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ReasonsMediaMutation"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "MediaMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "date"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "orderID"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "youtubeId"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "image"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "source"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "groups"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "MemesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "imageUrl"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "legislator<PERSON><PERSON><PERSON>"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ControversiesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "content"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "reflections"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "orderID"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "legislator<PERSON><PERSON>"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "tags"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "body"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}]}