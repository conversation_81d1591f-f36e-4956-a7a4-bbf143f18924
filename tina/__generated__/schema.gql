# DO NOT MODIFY THIS FILE. This file is automatically generated by <PERSON>
"""References another document, used as a foreign key"""
scalar Reference

""""""
scalar JSON

type SystemInfo {
  filename: String!
  title: String
  basename: String!
  hasReferences: Boolean
  breadcrumbs(excludeExtension: Boolean): [String!]!
  path: String!
  relativePath: String!
  extension: String!
  template: String!
  collection: Collection!
}

type Folder {
  name: String!
  path: String!
}

type PageInfo {
  hasPreviousPage: Boolean!
  hasNextPage: Boolean!
  startCursor: String!
  endCursor: String!
}

""""""
interface Node {
  id: ID!
}

""""""
interface Document {
  id: ID!
  _sys: SystemInfo
  _values: JSON!
}

"""A relay-compliant pagination connection"""
interface Connection {
  totalCount: Float!
  pageInfo: PageInfo!
}

type Query {
  getOptimizedQuery(queryString: String!): String
  collection(collection: String): Collection!
  collections: [Collection!]!
  node(id: String): Node!
  document(collection: String, relativePath: String): DocumentNode!
  legislators(relativePath: String): Legislators!
  legislatorsConnection(before: String, after: String, first: Float, last: Float, sort: String, filter: LegislatorsFilter): LegislatorsConnection!
  roles(relativePath: String): Roles!
  rolesConnection(before: String, after: String, first: Float, last: Float, sort: String, filter: RolesFilter): RolesConnection!
  reasons(relativePath: String): Reasons!
  reasonsConnection(before: String, after: String, first: Float, last: Float, sort: String, filter: ReasonsFilter): ReasonsConnection!
  media(relativePath: String): Media!
  mediaConnection(before: String, after: String, first: Float, last: Float, sort: String, filter: MediaFilter): MediaConnection!
  memes(relativePath: String): Memes!
  memesConnection(before: String, after: String, first: Float, last: Float, sort: String, filter: MemesFilter): MemesConnection!
  controversies(relativePath: String): Controversies!
  controversiesConnection(before: String, after: String, first: Float, last: Float, sort: String, filter: ControversiesFilter): ControversiesConnection!
}

input DocumentFilter {
  legislators: LegislatorsFilter
  roles: RolesFilter
  reasons: ReasonsFilter
  media: MediaFilter
  memes: MemesFilter
  controversies: ControversiesFilter
}

type DocumentConnectionEdges {
  cursor: String!
  node: DocumentNode
}

type DocumentConnection implements Connection {
  pageInfo: PageInfo!
  totalCount: Float!
  edges: [DocumentConnectionEdges]
}

type Collection {
  name: String!
  slug: String!
  label: String
  path: String!
  format: String
  matches: String
  templates: [JSON]
  fields: [JSON]
  documents(before: String, after: String, first: Float, last: Float, sort: String, filter: DocumentFilter, folder: String): DocumentConnection!
}

union DocumentNode = Legislators | Roles | Reasons | Media | Memes | Controversies | Folder

type Legislators implements Node & Document {
  name: String!
  party: String!
  district: String!
  imageUrl: String
  nicknames: [String]
  body: JSON
  id: ID!
  _sys: SystemInfo!
  _values: JSON!
}

input StringFilter {
  startsWith: String
  eq: String
  exists: Boolean
  in: [String]
}

input ImageFilter {
  startsWith: String
  eq: String
  exists: Boolean
  in: [String]
}

input RichTextFilter {
  startsWith: String
  eq: String
  exists: Boolean
}

input LegislatorsFilter {
  name: StringFilter
  party: StringFilter
  district: StringFilter
  imageUrl: ImageFilter
  nicknames: StringFilter
  body: RichTextFilter
}

type LegislatorsConnectionEdges {
  cursor: String!
  node: Legislators
}

type LegislatorsConnection implements Connection {
  pageInfo: PageInfo!
  totalCount: Float!
  edges: [LegislatorsConnectionEdges]
}

type Roles implements Node & Document {
  title: String!
  icon: String
  orderID: Float
  description: JSON
  id: ID!
  _sys: SystemInfo!
  _values: JSON!
}

input NumberFilter {
  lt: Float
  lte: Float
  gte: Float
  gt: Float
  eq: Float
  exists: Boolean
  in: [Float]
}

input RolesFilter {
  title: StringFilter
  icon: ImageFilter
  orderID: NumberFilter
  description: RichTextFilter
}

type RolesConnectionEdges {
  cursor: String!
  node: Roles
}

type RolesConnection implements Connection {
  pageInfo: PageInfo!
  totalCount: Float!
  edges: [RolesConnectionEdges]
}

type ReasonsMedia {
  type: String!
  url: String
  youtubeId: String
  caption: String
}

type Reasons implements Node & Document {
  title: String!
  body: JSON
  popularity: Float
  orderID: Float
  publishDate: String
  relatedLegislators: [String]
  relatedRoles: [String]
  media: [ReasonsMedia]
  tags: [String]
  id: ID!
  _sys: SystemInfo!
  _values: JSON!
}

input DatetimeFilter {
  after: String
  before: String
  eq: String
  exists: Boolean
  in: [String]
}

input ReasonsMediaFilter {
  type: StringFilter
  url: ImageFilter
  youtubeId: StringFilter
  caption: StringFilter
}

input ReasonsFilter {
  title: StringFilter
  body: RichTextFilter
  popularity: NumberFilter
  orderID: NumberFilter
  publishDate: DatetimeFilter
  relatedLegislators: StringFilter
  relatedRoles: StringFilter
  media: ReasonsMediaFilter
  tags: StringFilter
}

type ReasonsConnectionEdges {
  cursor: String!
  node: Reasons
}

type ReasonsConnection implements Connection {
  pageInfo: PageInfo!
  totalCount: Float!
  edges: [ReasonsConnectionEdges]
}

type Media implements Node & Document {
  title: String!
  description: String
  date: String
  orderID: Float
  youtubeId: String
  image: String
  source: String
  tags: [String]
  groups: [String]
  body: JSON
  id: ID!
  _sys: SystemInfo!
  _values: JSON!
}

input MediaFilter {
  title: StringFilter
  description: StringFilter
  date: DatetimeFilter
  orderID: NumberFilter
  youtubeId: StringFilter
  image: ImageFilter
  source: StringFilter
  tags: StringFilter
  groups: StringFilter
  body: RichTextFilter
}

type MediaConnectionEdges {
  cursor: String!
  node: Media
}

type MediaConnection implements Connection {
  pageInfo: PageInfo!
  totalCount: Float!
  edges: [MediaConnectionEdges]
}

type Memes implements Node & Document {
  title: String!
  description: String
  imageUrl: String!
  tags: [String]
  legislatorNames: [String]
  body: JSON
  id: ID!
  _sys: SystemInfo!
  _values: JSON!
}

input MemesFilter {
  title: StringFilter
  description: StringFilter
  imageUrl: ImageFilter
  tags: StringFilter
  legislatorNames: StringFilter
  body: RichTextFilter
}

type MemesConnectionEdges {
  cursor: String!
  node: Memes
}

type MemesConnection implements Connection {
  pageInfo: PageInfo!
  totalCount: Float!
  edges: [MemesConnectionEdges]
}

type Controversies implements Node & Document {
  title: String!
  content: String!
  reflections: [String]
  orderID: Float
  legislatorName: String!
  tags: [String]
  body: JSON
  id: ID!
  _sys: SystemInfo!
  _values: JSON!
}

input ControversiesFilter {
  title: StringFilter
  content: StringFilter
  reflections: StringFilter
  orderID: NumberFilter
  legislatorName: StringFilter
  tags: StringFilter
  body: RichTextFilter
}

type ControversiesConnectionEdges {
  cursor: String!
  node: Controversies
}

type ControversiesConnection implements Connection {
  pageInfo: PageInfo!
  totalCount: Float!
  edges: [ControversiesConnectionEdges]
}

type Mutation {
  addPendingDocument(collection: String!, relativePath: String!, template: String): DocumentNode!
  updateDocument(collection: String, relativePath: String!, params: DocumentUpdateMutation!): DocumentNode!
  deleteDocument(collection: String, relativePath: String!): DocumentNode!
  createDocument(collection: String, relativePath: String!, params: DocumentMutation!): DocumentNode!
  createFolder(collection: String, relativePath: String!): DocumentNode!
  updateLegislators(relativePath: String!, params: LegislatorsMutation!): Legislators!
  createLegislators(relativePath: String!, params: LegislatorsMutation!): Legislators!
  updateRoles(relativePath: String!, params: RolesMutation!): Roles!
  createRoles(relativePath: String!, params: RolesMutation!): Roles!
  updateReasons(relativePath: String!, params: ReasonsMutation!): Reasons!
  createReasons(relativePath: String!, params: ReasonsMutation!): Reasons!
  updateMedia(relativePath: String!, params: MediaMutation!): Media!
  createMedia(relativePath: String!, params: MediaMutation!): Media!
  updateMemes(relativePath: String!, params: MemesMutation!): Memes!
  createMemes(relativePath: String!, params: MemesMutation!): Memes!
  updateControversies(relativePath: String!, params: ControversiesMutation!): Controversies!
  createControversies(relativePath: String!, params: ControversiesMutation!): Controversies!
}

input DocumentUpdateMutation {
  legislators: LegislatorsMutation
  roles: RolesMutation
  reasons: ReasonsMutation
  media: MediaMutation
  memes: MemesMutation
  controversies: ControversiesMutation
  relativePath: String
}

input DocumentMutation {
  legislators: LegislatorsMutation
  roles: RolesMutation
  reasons: ReasonsMutation
  media: MediaMutation
  memes: MemesMutation
  controversies: ControversiesMutation
}

input LegislatorsMutation {
  name: String
  party: String
  district: String
  imageUrl: String
  nicknames: [String]
  body: JSON
}

input RolesMutation {
  title: String
  icon: String
  orderID: Float
  description: JSON
}

input ReasonsMediaMutation {
  type: String
  url: String
  youtubeId: String
  caption: String
}

input ReasonsMutation {
  title: String
  body: JSON
  popularity: Float
  orderID: Float
  publishDate: String
  relatedLegislators: [String]
  relatedRoles: [String]
  media: [ReasonsMediaMutation]
  tags: [String]
}

input MediaMutation {
  title: String
  description: String
  date: String
  orderID: Float
  youtubeId: String
  image: String
  source: String
  tags: [String]
  groups: [String]
  body: JSON
}

input MemesMutation {
  title: String
  description: String
  imageUrl: String
  tags: [String]
  legislatorNames: [String]
  body: JSON
}

input ControversiesMutation {
  title: String
  content: String
  reflections: [String]
  orderID: Float
  legislatorName: String
  tags: [String]
  body: JSON
}

schema {
  query: Query
  mutation: Mutation
}
