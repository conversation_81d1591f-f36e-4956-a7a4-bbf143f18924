fragment LegislatorsParts on Legislators {
  __typename
  name
  party
  district
  imageUrl
  nicknames
  body
}

fragment RolesParts on Roles {
  __typename
  title
  icon
  orderID
  description
}

fragment ReasonsParts on Reasons {
  __typename
  title
  body
  popularity
  orderID
  publishDate
  relatedLegislators
  relatedRoles
  media {
    __typename
    type
    url
    youtubeId
    caption
  }
  tags
}

fragment MediaParts on Media {
  __typename
  title
  description
  date
  orderID
  youtubeId
  image
  source
  tags
  groups
  body
}

fragment MemesParts on Memes {
  __typename
  title
  description
  imageUrl
  tags
  legislatorNames
  body
}

fragment ControversiesParts on Controversies {
  __typename
  title
  content
  reflections
  orderID
  legislatorName
  tags
  body
}
