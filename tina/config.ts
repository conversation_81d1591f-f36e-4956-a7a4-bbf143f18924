import { defineConfig } from "tinacms";

// Your hosting provider likely exposes this as an environment variable
const branch = process.env.HEAD || process.env.VERCEL_GIT_COMMIT_REF || "main";

export default defineConfig({
  branch,
  build: {
    outputFolder: "admin",
    publicFolder: "public",
  },
  media: {
    tina: {
      mediaRoot: "public/uploads",
      publicFolder: "public",
    },
  },
  schema: {
    collections: [
      {
        name: "legislators",
        label: "立法委員",
        path: "src/content/legislators",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${values?.name?.toLowerCase()
                .trim()
                .replace(/[^\w\s-]/g, "")
                .replace(/[\s_-]+/g, "-")
                .replace(/^-+|-+$/g, "")}`;
            },
          },
        },
        fields: [
          {
            type: "string",
            name: "name",
            label: "姓名",
            isTitle: true,
            required: true,
          },
          {
            type: "string",
            name: "party",
            label: "政黨",
            required: true,
          },
          {
            type: "string",
            name: "district",
            label: "選區",
            required: true,
          },
          {
            type: "image",
            name: "imageUrl",
            label: "照片",
          },
          {
            type: "string",
            name: "nicknames",
            label: "江湖稱號",
            list: true,
            description: "立委的江湖稱號，可以有多個，選填",
          },
          {
            type: "rich-text",
            name: "body",
            label: "簡介",
            isBody: true,
          },
        ],
      },
      {
        name: "roles",
        label: "角色分類",
        path: "src/content/roles",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${values?.title?.toLowerCase()
                .trim()
                .replace(/[^\w\s-]/g, "")
                .replace(/[\s_-]+/g, "-")
                .replace(/^-+|-+$/g, "")}`;
            },
          },
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "角色名稱",
            isTitle: true,
            required: true,
          },
          {
            type: "image",
            name: "icon",
            label: "角色圖示",
          },
          {
            type: "number",
            name: "orderID",
            label: "排序編號",
            description: "用於決定角色的顯示順序",
          },
          {
            type: "rich-text",
            name: "description",
            label: "角色說明",
            isBody: true,
          },
        ],
      },
      {
        name: "reasons",
        label: "罷免理由",
        path: "src/content/reasons",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${values?.orderID || Date.now()}-${values?.title?.toLowerCase()
                .trim()
                .replace(/[^\w\s-]/g, "")
                .replace(/[\s_-]+/g, "-")
                .replace(/^-+|-+$/g, "")}`;
            },
          },
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "標題",
            isTitle: true,
            required: true,
          },
          {
            type: "rich-text",
            name: "body",
            label: "內容",
            isBody: true,
          },
          {
            type: "number",
            name: "popularity",
            label: "熱門度",
            description: "0-100的數值，代表理由的熱門程度",
          },
          {
            type: "number",
            name: "orderID",
            label: "排序編號",
            description: "用於決定內容的排序順序",
          },
          {
            type: "datetime",
            name: "publishDate",
            label: "發佈日期",
          },
          {
            type: "string",
            name: "relatedLegislators",
            label: "關聯立委",
            list: true,
            ui: {
              component: "select",
            },
            options: [
              "all",
              "林沛祥",
              "王鴻薇",
              "李彥秀",
              "羅智強",
              "徐巧芯",
              "賴士葆",
              "葉元之",
              "洪孟楷",
              "張智倫",
              "林德福",
              "羅明才",
              "廖先翔",
              "牛煦庭",
              "涂權吉",
              "魯明哲",
              "萬美玲",
              "呂玉玲",
              "邱若華",
              "鄭正鈐",
              "徐欣瑩",
              "林思銘",
              "邱鎮軍",
              "顏寬恆",
              "楊瓊瓔",
              "廖偉翔",
              "黃健豪",
              "羅廷瑋",
              "江啟臣",
              "謝衣鳳",
              "馬文君",
              "游顥",
              "丁學忠",
              "傅崐萁",
              "黃建賓",
              "陳玉珍",
              "陳雪生",
              "陳超明",
            ],
          },
          {
            type: "string",
            name: "relatedRoles",
            label: "適用角色",
            list: true,
            ui: {
              component: "select",
            },
            options: [
              "上班族",
              "學生",
              "家長",
              "長輩",
              "國民黨員",
              "民眾黨員"
            ],
          },
          {
            type: "object",
            name: "media",
            label: "多媒體內容",
            list: true,
            fields: [
              {
                type: "string",
                name: "type",
                label: "類型",
                options: ["image", "video", "youtube"],
                required: true,
              },
              {
                type: "image",
                name: "url",
                label: "檔案",
                required: false,
              },
              {
                type: "string",
                name: "youtubeId",
                label: "YouTube ID",
                description: "例如: https://www.youtube.com/watch?v=dQw4w9WgXcQ 中的 dQw4w9WgXcQ",
                required: false,
              },
              {
                type: "string",
                name: "caption",
                label: "說明文字",
              },
            ],
          },
          {
            type: "string",
            name: "tags",
            label: "標籤",
            list: true,
            ui: {
              component: "tags",
            },
          },
        ],
      },
      {
        name: "media",
        label: "媒體內容",
        path: "src/content/media",
        format: "mdx",
        ui: {
          filename: {
            // Filename generator for new documents
            slugify: (values) => {
              return `${values?.orderID || Date.now()}-${values?.title?.toLowerCase()
                .trim()
                .replace(/[^\w\s-]/g, "")
                .replace(/[\s_-]+/g, "-")
                .replace(/^-+|-+$/g, "")}`;
            },
          },
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "標題",
            isTitle: true,
            required: true,
          },
          {
            type: "string",
            name: "description",
            label: "描述",
            ui: {
              component: "textarea",
            },
          },
          {
            type: "datetime",
            name: "date",
            label: "日期",
          },
          {
            type: "number",
            name: "orderID",
            label: "排序ID",
            description: "用於決定內容的排序順序",
          },
          {
            type: "string",
            name: "youtubeId",
            label: "YouTube ID",
            description: "例如: https://www.youtube.com/watch?v=dQw4w9WgXcQ 中的 dQw4w9WgXcQ",
          },
          {
            type: "image",
            name: "image",
            label: "主要圖片",
          },
          {
            type: "string",
            name: "source",
            label: "來源",
          },
          {
            type: "string",
            name: "tags",
            label: "標籤",
            list: true,
            ui: {
              component: "tags",
            },
          },
          {
            type: "string",
            name: "groups",
            label: "分組",
            list: true,
            options: [
              "罷免金曲",
              "罷免每日一字",
              "精彩影片集錦",
              "林沛祥",
              "王鴻薇",
              "李彥秀",
              "羅智強",
              "徐巧芯",
              "賴士葆",
              "葉元之",
              "洪孟楷",
              "張智倫",
              "林德福",
              "羅明才",
              "廖先翔",
              "牛煦庭",
              "涂權吉",
              "魯明哲",
              "萬美玲",
              "呂玉玲",
              "邱若華",
              "鄭正鈐",
              "徐欣瑩",
              "林思銘",
              "邱鎮軍",
              "顏寬恆",
              "楊瓊瓔",
              "廖偉翔",
              "黃健豪",
              "羅廷瑋",
              "江啟臣",
              "謝衣鳳",
              "馬文君",
              "游顥",
              "丁學忠",
              "傅崐萁",
              "黃建賓",
              "陳玉珍",
              "陳雪生",
              "陳超明",
            ],
          },
          {
            type: "rich-text",
            name: "body",
            label: "內容",
            isBody: true,
          },
        ],
      },
      {
        name: "memes",
        label: "梗圖內容",
        path: "src/content/memes",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${Date.now()}-${values?.title?.toLowerCase()
                .trim()
                .replace(/[^\w\s-]/g, "")
                .replace(/[\s_-]+/g, "-")
                .replace(/^-+|-+$/g, "")}`;
            },
          },
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "標題",
            isTitle: true,
            required: true,
          },
          {
            type: "string",
            name: "description",
            label: "描述",
            ui: {
              component: "textarea",
            },
          },
          {
            type: "image",
            name: "imageUrl",
            label: "梗圖圖片",
            required: true,
          },
          {
            type: "string",
            name: "tags",
            label: "標籤",
            list: true,
            ui: {
              component: "tags",
            },
          },
          {
            type: "string",
            name: "legislatorNames",
            label: "相關立委",
            description: "與此梗圖相關的立委",
            list: true,
            options: [
              "林沛祥",
              "王鴻薇",
              "李彥秀",
              "羅智強",
              "徐巧芯",
              "賴士葆",
              "葉元之",
              "洪孟楷",
              "張智倫",
              "林德福",
              "羅明才",
              "廖先翔",
              "牛煦庭",
              "涂權吉",
              "魯明哲",
              "萬美玲",
              "呂玉玲",
              "邱若華",
              "鄭正鈐",
              "徐欣瑩",
              "林思銘",
              "邱鎮軍",
              "顏寬恆",
              "楊瓊瓔",
              "廖偉翔",
              "黃健豪",
              "羅廷瑋",
              "江啟臣",
              "謝衣鳳",
              "馬文君",
              "游顥",
              "丁學忠",
              "傅崐萁",
              "黃建賓",
              "陳玉珍",
              "陳雪生",
              "陳超明",
            ],
          },
          {
            type: "rich-text",
            name: "body",
            label: "內容",
            isBody: true,
          },
        ],
      },
      {
        name: "controversies",
        label: "爭議事項",
        path: "src/content/controversies",
        format: "mdx",
        ui: {
          filename: {
            slugify: (values) => {
              return `${values?.orderID || Date.now()}-${values?.title?.toLowerCase()
                .trim()
                .replace(/[^\w\s-]/g, "")
                .replace(/[\s_-]+/g, "-")
                .replace(/^-+|-+$/g, "")}`;
            },
          },
        },
        fields: [
          {
            type: "string",
            name: "title",
            label: "標題",
            isTitle: true,
            required: true,
          },
          {
            type: "string",
            name: "content",
            label: "內容",
            ui: {
              component: "textarea",
            },
            required: true,
          },
          {
            type: "string",
            name: "reflections",
            label: "反思與提問",
            list: true,
            ui: {
              component: "textarea",
            },
          },
          {
            type: "number",
            name: "orderID",
            label: "排序編號",
            description: "用於決定爭議事項的顯示順序",
          },
          {
            type: "string",
            name: "legislatorName",
            label: "相關立委",
            description: "此爭議事項所屬的立委",
            required: true,
            options: [
              "林沛祥",
              "王鴻薇",
              "李彥秀",
              "羅智強",
              "徐巧芯",
              "賴士葆",
              "葉元之",
              "洪孟楷",
              "張智倫",
              "林德福",
              "羅明才",
              "廖先翔",
              "牛煦庭",
              "涂權吉",
              "魯明哲",
              "萬美玲",
              "呂玉玲",
              "邱若華",
              "鄭正鈐",
              "徐欣瑩",
              "林思銘",
              "邱鎮軍",
              "顏寬恆",
              "楊瓊瓔",
              "廖偉翔",
              "黃健豪",
              "羅廷瑋",
              "江啟臣",
              "謝衣鳳",
              "馬文君",
              "游顥",
              "丁學忠",
              "傅崐萁",
              "黃建賓",
              "陳玉珍",
              "陳雪生",
              "陳超明",
            ],
          },
          {
            type: "string",
            name: "tags",
            label: "標籤",
            list: true,
            ui: {
              component: "tags",
            },
          },
          {
            type: "rich-text",
            name: "body",
            label: "詳細內容",
            isBody: true,
          },
        ],
      },
    ],
  },
}); 