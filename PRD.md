### **1. 產品總覽 (Overview)**

### **1.1 產品願景 (Vision)**

針對立法委員葉元之的罷免案，打造一款名為《罷免元兇？》的快節奏攻防遊戲。玩家將親身體驗罷免理由與其答辯書之間的矛盾與話術，在 3 分鐘內快速掌握罷免核心爭議，並激發分享與參與連署的動力。
*(註：「元兇」與「元之」諧音，帶有雙關意味，增加記憶點。)*

### **1.2 背景與問題 (Background & Problem)**

葉委員的答辯書篇幅長，充滿了「努力服務」、「惡意抹黑」、「扭曲事實」等防禦性詞彙，並試圖用大量地方建設實績來轉移焦點。普通民眾難以在短時間內辨別其答辯是否回應了罷免理由的核心。本遊戲旨在將此過程簡化、視覺化、趣味化。

### **1.3 目標客群 (Target Audience)**

- **板橋第七選區選民：** 對罷免案有直接關係，需要清晰易懂的資訊來做判斷。
- **全國關心政治的年輕網民：** 對國會亂象感到不滿，樂於透過新媒體形式了解議題。
- **罷免案支持者與志工：** 需要一個強而有力的數位文宣工具，方便在社群媒體和即時通訊軟體中傳播。

---

### **2. 遊戲核心概念與流程 (Core Concept & Flow)**

### **2.1 遊戲核心玩法**

延續「立委攻防戰」的核心玩法，但加入針對葉委員答辯風格的特殊機制。

- **主要玩法：** 在其答辯文字中，快速點擊「**話術破綻**」關鍵字。
- **特殊機制：煙霧彈攻擊 (Smokescreen Attack)：** 當對方使用大量無關的地方政績來迴避問題時，會觸發「煙霧彈」回合，玩家需要採取不同行動來「撥開迷霧」。

### **2.2 遊戲流程 (Game Flow)**

1. **開場：** Q版葉元之委員站在立法院前，擺出電視名嘴的招牌姿勢。標題《罷免元兇？》出現。
2. **遊戲開始，共五個回合（對應五大理由）。**
    - **回合一：預算審查爭議**
    - **回合二：國會擴權法案爭議**
    - **回合三：機密會議直播爭議**
    - **回合四：黃牛預算與民意對作爭議**
    - **回合五 (特殊)：黨意 vs 民意 - 煙霧彈攻擊！**
3. **回合結束，結算畫面：** 根據玩家表現給予評級，如「識謊專家」、「板橋吹哨者」、「普通路人」。
4. **行動呼籲 (CTA)：** 強力引導至「罷免連署官網」、「分享給板橋的朋友」、「查看完整理由書」。

---

### **3. 功能規格拆解 (Feature Breakdown)**

### **模組一：美術與介面 (M1: Art & UI)**

- **M1-T1 (角色美術):**
    - 設計 Q 版葉元之形象，需要多個表情：**自信辯解**（預設）、**被戳中痛處**（受擊）、**施放煙霧彈**（特殊技能）。
    - 設計 Q 版公民監督者形象。
- **M1-T2 (UI 設計):**
    - 設計「公民怒氣值」（玩家血條）和「委員誠信值」（Boss 血條）。
    - 設計理由卡、答辯框、Fact Check 卡片的視覺風格，需要清晰易讀。
    - 為「煙霧彈攻擊」回合設計特別的視覺效果，如螢幕被煙霧或大量無關圖片（如會勘照片）遮蔽。

### **模組二：遊戲回合邏輯 (M2: Round Logic)**

- **M2-T1 (回合引擎):** 建立能依序載入五個回合資料的遊戲引擎。
- **M2-T2 (標準回合：點擊破綻):**
    - **功能：** 在答辯文字中，將預設的「話術關鍵字」以高亮、閃爍或放大等方式短暫提示。
    - **互動：** 玩家在時限內點擊正確關鍵字，扣除「委員誠信值」。點錯或錯過，扣除「公民怒氣值」。
- **M2-T3 (特殊回合：煙霧彈攻擊):**
    - **觸發：** 在第五回合觸發。
    - **畫面：** 螢幕出現大量葉委員的「地方建設實績」圖片或文字，形成煙霧彈效果，遮蔽核心問題。
    - **互動：** 玩家需要 **快速連續點擊** 或 **按住一個「拒絕模糊焦點！」按鈕** 來撥開煙霧，直到核心問題「為何總是跟隨黨意？」再次清晰出現。成功撥開迷霧即算勝利。
- **M2-T4 (Fact Check 系統):** 每回合結束後，彈出一個簡潔的 Fact Check 卡片，用一句話總結爭議點，並可附上「查看證據」連結（例如連到新聞報導或直播片段）。

### **模組三：遊戲內容資料庫 (M3: Content Database - JSON)**

這是遊戲的靈魂，需要將攻防內容 meticulously 轉化為遊戲數據。

```json
{
  "character": "葉元之",
  "gameTitle": "罷免元兇？",
  "rounds": [
    {
      "id": 1,
      "reasonTitle": "【理由一】胡亂刪預算",
      "rebuttalText": "此為不實指控...本人審預算非常認真，絕未說過『沒看內容』，這是斷章取義的政治操作。",
      "flaws": ["不實指控", "非常認真", "斷章取義", "政治操作"],
      "factCheck": "事實查核：委員曾在節目上親口承認沒看預算就舉手，網路皆有影片存證。"
    },
    {
      "id": 2,
      "reasonTitle": "【理由二】盲從國會擴權",
      "rebuttalText": "這是『國會改革法案』，是為強化監督，且經歷多次討論。官員專斷傲慢，才突顯需要制衡。",
      "flaws": ["國會改革法案", "多次討論", "專斷傲慢", "需要制衡"],
      "factCheck": "事實查核：法案未經實質討論，且已被憲法法庭初判違憲，侵害人民權益。"
    },
    {
      "id": 3,
      "reasonTitle": "【理由三】機密會議直播",
      "rebuttalText": "因有議事攻防，藍綠均有人直播。我察覺後立即關掉，並非罷團扭曲的『故意』開直播。",
      "flaws": ["均有人直播", "立即關掉", "並非...故意", "扭曲"],
      "factCheck": "事實查核：無論動機，在應保持機密的場合進行直播，本身就是嚴重失職的行為。"
    },
    {
      "id": 4,
      "reasonTitle": "【理由四】刪打黃牛預算",
      "rebuttalText": "預算一毛未刪！是政府打黃牛機制功能不彰，花大錢的辦公室裁罰率極低，當然需要被監督。",
      "flaws": ["一毛未刪", "功能不彰", "花大錢", "需要被監督"],
      "factCheck": "事實查核：其答辯迴避了『要求罷免連署附身分證』意圖提高罷免門檻，與民意對作的核心指控。"
    },
    {
      "id": 5,
      "roundType": "smokescreen",
      "reasonTitle": "【理由五】只跟黨意不顧民意",
      "rebuttalText": "（此處顯示大量地方建設成就，如會勘國小、爭取輕軌、關心醫療、活化土地等文字快速滾動）",
      "smokescreenCoreQuestion": "這些地方服務值得肯定，但為何在國家級重大法案上，您總是選擇跟隨黨意而非民意？",
      "factCheck": "事實查核：地方服務是立委本職，但不能迴避在重大憲政議題上，選擇黨意而背棄競選承諾的問題。"
    }
  ]
}

```

### **模組四：結算與傳播 (M4: Result & Viral Loop)**

- **M4-T1 (結算畫面):** 根據最終「誠信值」剩餘血量給予不同評級和圖像。
    - **高分:** 「罷免達人！你已看穿一切！」+ 葉元之被戳破謊言的狼狽 Q 版圖。
    - **中分:** 「監督新星！差一點就讓他現形！」
    - **低分:** 「要小心他的話術！再試一次！」
- **M4-T2 (行動呼籲按鈕):**
    - **【最重要】[主要按鈕] 前往連署教學** (連結至罷免官網的連署頁面)
    - **[次要按鈕] 分享給朋友，揭穿真相**
    - **[文字連結] 查看完整罷免理由書與答辯書**
- **M4-T3 (分享文案客製化):**
    - 分享出去的預設文案：「我玩了《罷免元兇？》遊戲，葉元之的話術真的很高明！快來挑戰看看你能不能識破！#罷免葉元之 #板橋」