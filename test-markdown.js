import { marked } from 'marked';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 讀取葉元之的 MDX 檔案內容
const mdxPath = path.join(__dirname, 'src/content/legislators/葉元之.mdx');
const mdxContent = fs.readFileSync(mdxPath, 'utf8');

console.log('=== 原始 MDX 檔案內容 ===');
console.log(mdxContent);
console.log('\n');

// 提取 body 內容（去除 frontmatter）
const bodyMatch = mdxContent.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
if (bodyMatch) {
  const bodyContent = bodyMatch[2];
  console.log('=== 提取的 body 內容 ===');
  console.log(bodyContent);
  console.log('\n');

  // 測試 marked 處理
  console.log('=== marked 處理結果 ===');
  try {
    const parsedContent = marked(bodyContent);
    console.log(parsedContent);
    console.log('\n');

    // 檢查是否包含粗體標籤
    if (parsedContent.includes('<strong>')) {
      console.log('✅ 成功：包含 <strong> 標籤');
    } else {
      console.log('❌ 失敗：沒有 <strong> 標籤');
    }

    // 檢查特定的粗體內容
    if (parsedContent.includes('<strong>「凹之」</strong>')) {
      console.log('✅ 成功：找到 <strong>「凹之」</strong>');
    } else {
      console.log('❌ 失敗：沒有找到 <strong>「凹之」</strong>');
      console.log('實際內容：', parsedContent.replace(/\n/g, '\\n'));
    }

  } catch (error) {
    console.error('❌ marked 處理錯誤：', error);
  }
} else {
  console.log('❌ 無法提取 body 內容');
}

// 測試簡單的 markdown
console.log('\n=== 測試簡單 markdown ===');
const simpleMarkdown = '**「凹之」**';
const simpleResult = marked(simpleMarkdown);
console.log('原始：', simpleMarkdown);
console.log('結果：', simpleResult);
console.log('包含 <strong>：', simpleResult.includes('<strong>')); 